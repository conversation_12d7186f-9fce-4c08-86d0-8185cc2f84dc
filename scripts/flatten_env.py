import sys
import os

def read_and_flatten(file_path: str) -> str:
    """读取文件并将换行转换为文本形式的 \\n"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    return content.replace('\n', ';')

def save_flattened_content(output_path: str, content: str):
    """将处理后的内容写入输出文件"""
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(content)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python trans.py <文件路径>")
        sys.exit(1)

    input_path = os.path.abspath(sys.argv[1])

    if not os.path.isfile(input_path):
        print(f"错误: 找不到文件 {input_path}")
        sys.exit(1)

    flattened = read_and_flatten(input_path)
    print(f"处理后的一行内容:\n{flattened}")

    # 生成输出路径
    directory, filename = os.path.split(input_path)
    output_filename = f"flattened_{filename}"
    output_path = os.path.join(directory, output_filename)

    save_flattened_content(output_path, flattened)
    print(f"已保存处理结果到: {output_path}")
