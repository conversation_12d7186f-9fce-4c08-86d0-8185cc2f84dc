# Use the official Node.js 18 image as the base image
FROM node:18-alpine AS builder

# Install pnpm
RUN npm install -g pnpm

# Set the working directory
WORKDIR /app

# Copy package.json and pnpm-lock.yaml
COPY heracles/server/admin/frontend/package.json heracles/server/admin/frontend/pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install

# Copy the rest of the application code
COPY heracles/server/admin/frontend .

# Build the Next.js application
RUN pnpm run build

# Use a smaller base image for the final stage
FROM node:18-alpine

# Install pnpm
RUN npm install -g pnpm

# Set the working directory
WORKDIR /app

# Copy the built application from the builder stage
# 仅复制必要的文件
COPY --from=builder /app/ ./

# Expose the port the app runs on
EXPOSE 3021

# Command to run the application
ENTRYPOINT ["pnpm", "start"]