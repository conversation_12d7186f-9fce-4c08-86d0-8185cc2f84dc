from multilspy import SyncLanguageServer
from multilspy.multilspy_config import MultilspyConfig
from multilspy.multilspy_logger import MultilspyLogger

config = MultilspyConfig.from_dict({"code_language": "python"}) # Also supports "python", "rust", "csharp", "typescript", "javascript", "go", "dart", "ruby"
logger = MultilspyLogger()
lsp = SyncLanguageServer.create(config, logger, "/Users/<USER>/works/clacky-ai-agent/")
with lsp.start_server():
    result = lsp.request_definition(
        "heracles/core/config.py", # Filename of location where request is being made
        9, # line number of symbol for which request is being made
        10 # column number of symbol for which request is being made
    )
    # print(result)
