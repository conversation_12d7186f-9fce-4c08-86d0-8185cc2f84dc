# syntax=docker/dockerfile:1

# Please see https://docs.docker.com/engine/reference/builder for information about
# the extended buildx capabilities used in this file.
# Make sure multiarch TARGETPLATFORM is available for interpolation
# See: https://docs.docker.com/build/building/multi-platform/
ARG TARGETPLATFORM=${TARGETPLATFORM}
ARG BUILDPLATFORM=${BUILDPLATFORM}

# Python image to use for base image, change with [--build-arg PYTHON_VERSION="3.11.x"]
ARG PYTHON_VERSION="3.11"
# Debian image to use for base image, change with [--build-arg DEBIAN_VERSION="bookworm"]
ARG DEBIAN_VERSION="bookworm"

FROM docker.io/python:${PYTHON_VERSION}-slim-${DEBIAN_VERSION} as base

# Timezone used by the Docker container and runtime, change with [--build-arg TZ=America/New_York]
ARG TZ="Etc/UTC"

ENV \
# Apply timezone
  TZ=${TZ} \
# Optimize jemalloc 5.x performance
  MALLOC_CONF="narenas:2,background_thread:true,thp:never,dirty_decay_ms:1000,muzzy_decay_ms:0"

# Set default shell used for running commands
SHELL ["/bin/bash", "-o", "pipefail", "-o", "errexit", "-c"]

ARG TARGETPLATFORM

RUN echo "Target platform is $TARGETPLATFORM"

RUN \
# Remove automatic apt cache Docker cleanup scripts
  rm -f /etc/apt/apt.conf.d/docker-clean; \
# Sets timezone
  echo "${TZ}" > /etc/localtime;

# Set /app as working directory
WORKDIR /app

# hadolint ignore=DL3008,DL3005
RUN \
# Mount Apt cache and lib directories from Docker buildx caches
--mount=type=cache,id=apt-cache-${TARGETPLATFORM},target=/var/cache/apt,sharing=locked \
--mount=type=cache,id=apt-lib-${TARGETPLATFORM},target=/var/lib/apt,sharing=locked \
# Apt update & upgrade to check for security updates to Debian image
  apt-get update; \
  apt-get dist-upgrade -yq; \
  apt-get install -y --no-install-recommends \
    curl \
    make \
    libjemalloc2 \
    patchelf \
    tini \
    tzdata \
    npm \
  ; \
  npm install -g eslint; \
# Patch Python to use jemalloc
  patchelf --add-needed libjemalloc.so.2 /usr/local/bin/python; \
# Discard patchelf after use
  apt-get purge -y \
    patchelf \
  ;

FROM base as builder

ARG TARGETPLATFORM

WORKDIR /app

ENV PYTHONPATH='/app' \
    POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_IN_PROJECT=1 \
    POETRY_VIRTUALENVS_CREATE=1

# hadolint ignore=DL3008
RUN \
# Mount Apt cache and lib directories from Docker buildx caches
--mount=type=cache,id=apt-cache-${TARGETPLATFORM},target=/var/cache/apt,sharing=locked \
--mount=type=cache,id=apt-lib-${TARGETPLATFORM},target=/var/lib/apt,sharing=locked \
--mount=type=cache,id=pip-cache-${TARGETPLATFORM},target=/usr/local/share/.cache/pip,sharing=locked \
# Install build tools and bundler dependencies from APT
  apt-get install -y --no-install-recommends \
    build-essential \
    git \
  ; \
  python3 -m pip install poetry==1.8.2  --break-system-packages \
  ;

COPY ./pyproject.toml ./poetry.lock ./

RUN \
# Mount Python Poetry caches
--mount=type=cache,id=poetry-cache-${TARGETPLATFORM},target=/usr/local/share/.cache/pypoetry,sharing=locked \
  poetry install --no-root

FROM base as prod

ARG TARGETPLATFORM

WORKDIR /app

ENV VIRTUAL_ENV=/app/.venv \
    PATH="/app/.venv/bin:$PATH" \
    PYTHONPATH='/app'

COPY --chmod=770 --from=builder ${VIRTUAL_ENV} ${VIRTUAL_ENV}

COPY --chmod=770 . .

WORKDIR /app

# Expose default uvicorn ports
EXPOSE 3000
# Set container tini as default entry point
ENTRYPOINT ["/usr/bin/tini", "--"]

# Move to k8s deployment
# CMD ["uvicorn", "heracles.server.api:app", "--host", "0.0.0.0", "--port", "3000"]
