from heracles.core.schema import MemoryMessage, AssistantMemoryMessage, ToolMemoryMessage
from typing import Union, Optional, List

class Memory:
    """ 内存记忆系统, 注意这里不考虑记录 system_prompt, 由用户自行维护
    TODO: 限制每条长度, 限制最大条数
    """
    def __init__(self):
        self.storage: list[MemoryMessage] = []

    def add(self, memory_message: MemoryMessage):
        self.storage.append(memory_message)

    def add_from_dict(self, memory_message_dict: dict):
        memory_message: Union[AssistantMemoryMessage, ToolMemoryMessage, MemoryMessage]
        if memory_message_dict['role'] == 'assistant':
            memory_message = AssistantMemoryMessage(**memory_message_dict)
        elif memory_message_dict['role'] == 'tool':
            memory_message = ToolMemoryMessage(**memory_message_dict)
        else:
            memory_message = MemoryMessage(**memory_message_dict)
        self.add(memory_message)

    def add_user_message(self, message: Union[str, List[dict]]):
        self.add(MemoryMessage(content=message))

    def add_system_message(self, message: str):
        raise Exception('add_system_message not support in current architecture')

    def add_assistant_message(self, message: str, tool_calls: Optional[list[dict]] = None):
        self.add(AssistantMemoryMessage(content=message, tool_calls=tool_calls))

    def add_tool_message(self, message: str, tool_call_id: str):
        self.add(ToolMemoryMessage(content=message, tool_call_id=tool_call_id))

    def add_batch(self, memory_messages: list[MemoryMessage]):
        for memory_message in memory_messages:
            self.add(memory_message)

    def get(self, k=0) -> list[MemoryMessage]:
        """Return the most recent k memories, return all when k=0"""
        if k == 0:
            return self.storage

        recent_memories = self.storage[-k:]
        # 如果第一条是 tool，那么需要找到最近的 user
        if recent_memories and recent_memories[0].role in ['tool', 'assistant']:
            # 倒序
            for i in range(len(self.storage) - k - 1, -1, -1):
                if self.storage[i].role == 'user':
                    recent_memories = self.storage[i:]
                    break
        return recent_memories

    def remove_newest_pair_memories(self):
        # 倒序
        for i in range(len(self.storage) - 1, -1, -1):
            if self.storage[i].role == 'user':
                self.storage.pop(i)
                break
            self.storage.pop(i)

    def transform_last_message_with_result(self, result: str):
        last_message = self.storage[-1]
        if isinstance(last_message, ToolMemoryMessage):
            last_message.content = result

    def reset(self):
        self.storage = []

    def count(self):
        """Return the number of messages in storage"""
        return len(self.storage)

    def to_llm_messages(self, system_prompt, *, max_size=10):
        """ 转换为 llm 支持的消息
        """
        system_prompt_message = MemoryMessage(role='system', content=system_prompt).dict()
        memory_messages = list(map(lambda message: message.dict(), self.get(max_size)))
        if not memory_messages or memory_messages[0]['role'] != 'system':
            memory_messages.insert(0, system_prompt_message)
        return memory_messages
