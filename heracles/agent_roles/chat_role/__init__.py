from heracles.agent_roles.role_action import (
    Agent<PERSON><PERSON><PERSON>reateSpecAction,
    Agent<PERSON><PERSON><PERSON>ullAction,
    AgentRoleCreateStepAction,
)
from heracles.agent_roles.role_base import RoleBase
from heracles.core.schema import LLMAbilityType
from heracles.core.schema.models import TaskIntentModel, AppendStepIntentModel
from heracles.core.utils.context_prompt_builder import PromptBuilder
from heracles.agent_roles.utils import extract_referenced_contents
from heracles.core.utils.llm_function import tool_call_to_argument_pair
from .prompts import SYSTEM_PROMPT, USER_PROMPT, TASK_IDENTIFIER_PROMPT, SUPER_MODE_RULE_PROMPT

class ChatRole(RoleBase):
    """聊天, 识别制定计划(根据需要)"""

    def __init__(self, workspace):
        super().__init__(workspace)
        self.system_prompt = SYSTEM_PROMPT
        self.super_mode = False

    def get_llm_ability(self):
        return LLMAbilityType.STRONG

    async def run(self, message, memory_regenerate=False, need_consider_task=False):
        """根据系统当前状态决定是否考虑任务创建"""
        system_context_builder = PromptBuilder(SYSTEM_PROMPT, self.workspace)
        response_model: type[TaskIntentModel | AppendStepIntentModel]

        if need_consider_task:
            system_context_builder.template = SYSTEM_PROMPT + TASK_IDENTIFIER_PROMPT
            self.system_prompt = await system_context_builder.format()
            response_model = TaskIntentModel
        else:
            self.super_mode = True
            system_context_builder.template = SYSTEM_PROMPT + SUPER_MODE_RULE_PROMPT
            self.system_prompt = await system_context_builder.format()
            response_model = AppendStepIntentModel

        user_prompt_builder = PromptBuilder(USER_PROMPT, self.workspace)
        message, content_dict = await self.extract_user_message(message)
        if content_dict['files'] or content_dict['errors'] or content_dict['webpages'] or content_dict['images']:
            message += "\n\n---\n" + "Respond with following information:"
            if content_dict['files']:
                user_prompt_builder.file_snippets = content_dict['files']
            if content_dict['errors']:
                user_prompt_builder.errors = content_dict['errors']
                ref_ids = (error.ref_id for error in content_dict['errors'])
                await self.workspace.trigger_project_errors_message(ref_ids=ref_ids)
                user_prompt_builder.playbooks = self.workspace.playbook_manager.search_by_tags(tags=['handling_errors'])
            if content_dict['webpages']:
                user_prompt_builder.webpages = content_dict['webpages']
            if content_dict['images']:
                user_prompt_builder.images = content_dict['images']
        user_prompt = await user_prompt_builder.format(USER_MESSAGE=message)
        res = await self.aask_with_memory(
            user_prompt,
            images=content_dict['images'],
            tools=['read_file', 'read_playbook', 'search_codebase', 'run_project'],
            response_model=response_model,
            response_model_optional=True,
            chunk_callback=self.trigger_chunk_message,
            tools_callback=self.trigger_tool_callback,
            memory_regenerate=memory_regenerate
        )

        # 分析完错误后，从错误列表中移除
        if content_dict['errors']:
            for error in content_dict['errors']:
                if error in self.workspace.smart_detect.errors:
                    self.workspace.smart_detect.errors.remove(error)

        if isinstance(res, TaskIntentModel):
            return AgentRoleCreateSpecAction(goal=res.goal, goal_detail=res.goal_detail)

        if isinstance(res, AppendStepIntentModel):
            return AgentRoleCreateStepAction(goal=res.goal, plan_draft=res.plan_draft, references=res.references)

        if isinstance(res, str):
            return AgentRoleNullAction(res)

    async def extract_user_message(self, message: str):
        message_content = message
        content_dict: dict[str, list] = {
            'files': [],
            'errors': [],
            'images': [],
            'webpages': []
        }
        if "@@user_context" in message:
            message_content = message.split("@@user_context[")[0]
            references_str = message.split("@@user_context[")[1].split("]@@")[0]
            references = list(map(lambda x: x.strip(), references_str.split(",")))
            content_dict = await extract_referenced_contents(references, self.workspace)
        return message_content, content_dict

    async def trigger_tool_callback(self, tool_call, status):
        try:
            if any(char.isupper() for char in tool_call.function.name):
                await self.workspace.trigger(
                    'tool_call_status_updated', {'tool_id': tool_call.id, 'tool_name': tool_call.function.name, 'status': status}
                )
            elif status == 'end':
                tool_name, value = tool_call_to_argument_pair(tool_call)
                message = f"\n> {tool_name}: `{value}`\n\n" if value else f"\n> {tool_name}\n\n"
                await self.workspace.trigger('chunk_message', message)
        except Exception as e:
            self.logger.error(f"Error in trigger_tool_callback: {e}, tool_call: {tool_call}")

    async def trigger_chunk_message(self, word: str):
        await self.workspace.trigger('chunk_message', word)

    def reset_session(self):
        self.memory.reset()


__all__ = ['ChatRole']
