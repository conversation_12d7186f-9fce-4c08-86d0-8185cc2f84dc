# The prompts format is jinja template

SUMMARY_SYSTEM_PROMPT = """
You are a responsible software engineer and always write good summaries.
"""  # noqa

SUMMARY_USER_PROMPT = """
Below is the project you are working on:

# Context

{FILE_TREE}
{PROJECT_BASIC_INFO}
{PROJECT_STRUCTURE}
{PROJECT_COMPONENTS}
{PROJECT_DEPENDENCIES}

Now you completed some tasks and make some file changes as below:
{TASK}
{RECENT_FILE_CHANGES}

Please analyze the project context, understand the context, task and file changes. Identify key elements like:

Which files are affected?
What types of changes were made (e.g., new features, bug fixes, refactoring, documentation, testing)?

Then you should generate a summary that succinctly summarizes the changes in English.

The summary should include:
A title that clearly states the purpose of the changes.
A detailed description if the changes are complex or need further explanation.
A procedure of steps to modify files according to diff messages. Imagine you are splitting the diff messages into smaller, precise, human-friendly steps.

You should return json response using below format, and DO NOT include '```' or '```json' in beginning and end of the output:
{{
    'title': str, # purpose of the changes.
    'description': str # further explanation of the changes
    'tags': list[str] # a list of tags to describes the topic of the changes, e.g. programming language, used framwork, and any other word or phrase to help identify the changes
    'steps': list[object] # a list of detailed step by step instructions of what file diff do and what goal to implement
}}
below is an example:
{{
    "title": "Add read_cars API to FastAPI application",
    "description": "Implement a new /car GET endpoint to return all car data and update car data model as necessary.",
    "tags": ["FastAPI", "Python"],
    "steps": [
        {{
            "title": "Add GET endpoint for car data",
            "description": "Implement a new route in api.py that handles GET requests to '/car' and returns car data from cars.json.",
        }},
        {{
            "title": "Update Car Model in models.py",
            "description": "Revise the car model in models.py to ensure it accommodates all necessary attributes for the API response.",
        }},
        {{
            "title": "Route requests to the new endpoint",
            "description": "Modify the main.py file to include the new '/car' route in the API.",
        }}
    ]
}}
"""  # noqa

PLAYBOOK_PROCEDURE = """
## Overview
{{ procedure.title }}
{{ procedure.description }}

{% if procedure.steps -%}
## Procedure
{% for step in procedure.steps -%}
{{ loop.index }}. {{ step.title }}: {{ step.description }}
{% endfor %}
{% endif %}
"""

PLAYBOOK_CONTENT_TEMPLATE = """
{PLAYBOOK_PROCEDURE}
{PROJECT_BASIC_INFO}
{PROJECT_STRUCTURE}
{PROJECT_COMPONENTS}
{PROJECT_DEPENDENCIES}
{FILE_TREE}
{RECENT_FILE_CHANGES}
"""  # noqa
