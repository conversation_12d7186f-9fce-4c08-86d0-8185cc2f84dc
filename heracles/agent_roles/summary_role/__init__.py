from pydantic import BaseModel, <PERSON>
from jinja2 import Template
from heracles.agent_roles.role_base import RoleB<PERSON>
from heracles.core.schema import LLMAbilityType
from heracles.core.utils.context_prompt_builder import PromptBuilder
from heracles.agent_workspace import AgentWorkspace
from heracles.agent_workspace.playbook import Playbook
from heracles.agent_roles.summary_role.summary_prompts import (
    SUMMARY_SYSTEM_PROMPT,
    SUMMARY_USER_PROMPT,
    PLAYBOOK_CONTENT_TEMPLATE,
    PLAYBOOK_PROCEDURE,
)


class Step(BaseModel):
    title: str = Field(default='')
    description: str = Field(default='')


class Procedure(BaseModel):
    title: str = Field(default='')
    description: str = Field(default='')
    steps: list[Step] = Field(default=[])


class SummaryRole(RoleBase):
    """项目总结输出 playbook"""

    def __init__(self, workspace: AgentWorkspace):
        super().__init__(workspace)

    async def run(self) -> Playbook | None:
        self.logger.info("summary role: begin summary into playbook")
        system_prompt_builder = PromptBuilder(SUMMARY_SYSTEM_PROMPT, self.workspace)
        user_prompt_builder = PromptBuilder(SUMMARY_USER_PROMPT, self.workspace)

        self.system_prompt = await system_prompt_builder.format()
        user_prompt = await user_prompt_builder.format()
        summaried_procedure_model = await self.aask(user_prompt, response_model=Procedure)

        playbook_procedure = Template(PLAYBOOK_PROCEDURE).render(procedure=summaried_procedure_model)
        playbook_content_builder = PromptBuilder(PLAYBOOK_CONTENT_TEMPLATE, self.workspace)
        playbook_content = await playbook_content_builder.format(PLAYBOOK_PROCEDURE=playbook_procedure)
        tags = self.workspace.playbook_manager.generate_playbook_tags()
        playbook = Playbook(
            title=summaried_procedure_model.title,
            description=summaried_procedure_model.description,
            original_content=playbook_content,
            tags=tags,
            applicable_rules=[],
            source='ai',
            status='pending',
        )
        self.logger.info("summary role: upload playbook")
        await self.upload_playbook(playbook)
        self.logger.info("summary role: summary and upload playbook done")
        return playbook

    def get_llm_ability(self):
        return LLMAbilityType.NORMAL

    async def upload_playbook(self, playbook):
        return await self.workspace.playbook_manager.upload(playbook)
