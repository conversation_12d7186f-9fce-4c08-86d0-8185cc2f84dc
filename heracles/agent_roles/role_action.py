from dataclasses import dataclass
from pydantic import Field

@dataclass
class Agent<PERSON>oleActionBase:
    "Agent Role Action Base Class"

@dataclass
class AgentRoleTransferActionBase(AgentRoleActionBase):
    with_memory: bool = False

@dataclass
class AgentRoleTaskActionFinishAction(AgentRoleActionBase):
    "Agent Task Finish Class"
    result: str = ''

@dataclass
class AgentRoleTaskActionAbandonAction(AgentRoleActionBase):
    "Agent Task Finish Class"
    result: str = ''

@dataclass
class AgentRoleNullAction(AgentRoleActionBase):
    "Agent Null Class"
    result: str = ''

@dataclass
class AgentRoleCreateSpecAction(AgentRoleTransferActionBase):
    "Agent Task Delegate Spec Class"
    goal: str = ''
    goal_detail: str = ''

@dataclass
class AgentRoleCreateStepAction(AgentRoleTransferActionBase):
    "Agent Task Delegate Step Class"
    goal: str = ''
    plan_draft: str = ''
    references: list[str] = Field(default_factory=list)
