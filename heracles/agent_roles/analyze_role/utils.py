from heracles.core.exceptions import AgentRunException
from heracles.agent_workspace.paas_sdk.utils import get_environments, get_paas_middlewares

async def get_available_environments_and_middlewares():
    """获取可用的环境和中间件列表"""
    environment_res = await get_environments()
    if environment_res['success']:
        environment_list = environment_res['data']
        environment_list = [{
            'id': env['id'],
            'name': env['name'],
            'runtime': env['runtime'],
            'language': env['language'],
            'packageManagers': env['packageManagers'],
        } for env in environment_list]
    else:
        raise AgentRunException(f"Failed to get environment list, reason: {environment_res['error']}")

    middleware_res = await get_paas_middlewares()
    if middleware_res['success']:
        middleware_list = middleware_res['data']
    else:
        raise AgentRunException(f"Failed to get middleware list, reason: {middleware_res['error']}")

    return environment_list, middleware_list
def merge_ai_result_to_environment_list(environment_list, ai_result):
    for env in environment_list:
        env['score'] = 0
        env['reason'] = ''
        for match_env in ai_result.environments:
            if env['id'] == match_env.id:
                env['score'] = match_env.score


def merge_ai_result_to_middleware_list(middleware_list, ai_result):
    for middleware in middleware_list:
        middleware['score'] = 0
        middleware['reason'] = ''
        for match_middleware in ai_result.middlewares:
            if middleware['id'] == match_middleware.id:
                middleware['score'] = match_middleware.score

def group_and_sort_environments(environment_list):
    # Group environments by language and calculate highest score per language
    language_groups = {}
    for env in environment_list:
        language = env.get('language', 'Unknown')
        if language not in language_groups:
            language_groups[language] = {
                'name': language,
                'environments': [],
                'score': 0
            }
        env_score = env.get('score', 0)
        language_groups[language]['environments'].append(env)
        language_groups[language]['environments'].sort(key=lambda x: -(x.get('score', 0)))
        language_groups[language]['score'] = max(
            language_groups[language]['score'],
            env_score
        )

    environment_list = [
        {
            'id': str(idx + 1),
            'name': lang,
            'versionList': group['environments'],
            'score': group['score']
        }
        for idx, (lang, group) in enumerate(sorted(
            language_groups.items(),
            key=lambda x: -x[1]['score']
        ))
    ]
    return environment_list


def find_best_match_env(environment_list):
    # 提取出分数最高的
    best_env = {"score": 0}
    for env in environment_list:
        if env['score'] < best_env['score']:
            continue
        for version in env['versionList']:
            if version['score'] > best_env['score']:
                best_env = version
                break
    return best_env


def find_match_middlewares(middleware_list):
    # 提取出被选中的中间件
    one_score_middleware_list = []
    for middleware in middleware_list:
        if middleware['score'] == 1:
            one_score_middleware_list.append(middleware)
    return one_score_middleware_list
