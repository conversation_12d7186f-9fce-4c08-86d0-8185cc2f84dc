import re
from typing import List

from heracles.core.schema import LLMAbilityType
from heracles.core.schema.models import PRMessageModel
from heracles.core.exceptions import AgentRunException
from heracles.core.utils.context_prompt_builder import Prompt<PERSON>uilder
from heracles.core.exceptions import IDEServerFun<PERSON>allTimeoutException
from heracles.core.utils.git_message_parser import parse_file_list_message_to_list
from .prompts import MAKE_COMMIT_SYSTEM_MESSAGE, MAKE_COMMIT_USER_MESSAGE, MAKE_PR_SYSTEM_MESSAGE, MAKE_PR_USER_MESSAGE
from ..role_base import RoleBase

class GitRole(RoleBase):

    def __init__(self, workspace):
        super().__init__(workspace)
        # 限制最大处理行数
        self.max_lines = 20
        self.playground = self.workspace.playground

    def get_llm_ability(self):
        return LLMAbilityType.FAST

    async def run(self):
        await self.make_commit_message()

    async def make_commit_message(self):
        """ 返回 commit 建议 message

        :return:

        具体见接口 "docs/pr-design.md"
        """
        file_list = await self.make_commit_file_list()
        if not file_list:
            raise AgentRunException("No changed files found, maybe your branch is clean")
        if len(file_list) > self.max_lines:
            await self.workspace.trigger('message', (
                f"Commit with more than {self.max_lines} changed files might be too large, please review the changes and check if any unnecessary files are included."  # noqa
            ))

        git_diff_message = await self.make_commit_diff_message()

        system_prompt_builder = PromptBuilder(MAKE_COMMIT_SYSTEM_MESSAGE, self.workspace)
        self.system_prompt = await system_prompt_builder.format()

        user_prompt_builder = PromptBuilder(MAKE_COMMIT_USER_MESSAGE, self.workspace)
        message = await user_prompt_builder.format(DIFF_LIST=git_diff_message)
        commit_message = await self.aask(message)
        return {
            "suggested_message": commit_message.strip(),
            "file_list": file_list
        }

    async def make_pr_message(self, git_upstream_branch: str) -> dict:
        """ 返回要更新的 PR 的 Message

        :return:
            suggested_title: 建议标题
            suggested_description: 建议描述
            file_list: 文件变更列表

        具体见接口 "docs/pr-design.md"
        """
        file_list = await self.make_pr_file_list(git_upstream_branch)
        if not file_list:
            raise AgentRunException("No PR files found")

        system_prompt_builder = PromptBuilder(MAKE_PR_SYSTEM_MESSAGE, self.workspace)
        self.system_prompt = await system_prompt_builder.format()

        user_prompt_builder = PromptBuilder(MAKE_PR_USER_MESSAGE, self.workspace)
        message = await user_prompt_builder.format(FILE_CHANGE_LIST=file_list)
        pr_message_model = await self.aask(message, response_model=PRMessageModel)
        pr_message_res = pr_message_model.model_dump()
        pr_message_res['file_list'] = file_list
        return pr_message_res

    async def make_commit_file_list(self) -> List[dict]:
        """ 获取 commit 所需要的文件列表并返回

        :return:

        具体见接口 "docs/pr-design.md"
        """
        git_status_message: str = await self._safe_git_status()
        return parse_file_list_message_to_list(git_status_message)

    async def _safe_git_status(self) -> str:
        """git status message, 超时会抛出异常"""
        try:
            git_status_message = await self.playground.func_call('agent_terminal_with_result', 'git status --porcelain --no-renames')
        except IDEServerFunCallTimeoutException:
            await self.workspace.trigger('message', (
                'Git operation takes longer than expected. Please check for unnecessary large files/directories, add them to `.gitignore` and try again. '  # noqa
                "If that's not the issue, there may be network connectivity problems. Please try again later."
            ))
            raise AgentRunException("Git operation takes longer than expected.") from None
        self.logger.debug(git_status_message)
        return git_status_message

    async def make_pr_file_list(self, git_upstream_branch: str) -> List[dict]:
        """获取 PR 所需要的文件变更列表"""
        git_file_change_message = await self._safe_git_diff_branch(git_upstream_branch)
        return parse_file_list_message_to_list(git_file_change_message)

    async def _safe_git_diff_branch(self, git_upstream_branch: str) -> str:
        """获取 PR 所需要的文件变更列表"""
        self.logger.debug(f'upstream branch: {git_upstream_branch}')

        git_file_change_message = await self.playground.func_call(
            'agent_terminal_with_result',
            f'git --no-pager diff --name-status --no-renames {git_upstream_branch}...HEAD',
        )
        self.logger.debug(f'git_file_change_message: {git_file_change_message}')

        pattern = r'^fatal'
        if not git_file_change_message or re.search(pattern, git_file_change_message, re.IGNORECASE):
            raise AgentRunException("Get PR changed file list failed, maybe your branch is clean")

        return git_file_change_message

    async def make_commit_diff_message(self) -> str:
        """git diff message, 超时可跳过"""
        try:
            git_diff_message = await self.playground.func_call('agent_terminal_with_result', 'git --no-pager diff HEAD')
        except IDEServerFunCallTimeoutException:
            await self.workspace.trigger('message', (
                'Git operation takes longer than expected. Please check for unnecessary large files/directories, add them to `.gitignore` and try again.'  # noqa
                "If that's not the issue, there may be network connectivity problems."
            ))
            return '...(content too long, truncated)...'
        return git_diff_message
