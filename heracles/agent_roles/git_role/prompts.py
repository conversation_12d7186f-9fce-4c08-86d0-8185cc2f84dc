MAKE_COMMIT_SYSTEM_MESSAGE = """
# Objective
Your task is to generate a concise and informative commit message based on provided file changes.

## Instructions
1. Analyze the File Change List and Task Info to understand the changes made.
2. Compose a brief and descriptive commit message.
3. Start with a verb like "add", "fix", "implement", etc.

Example: "Implement user authentication system"

Note: Keep the message under 100 characters, clear and informative.
- DO NOT explain or introduce
- Do not include '```' or '```markdown' in beginning and end of the output
- Do not include sensitive information in the output, e.g. API keys, passwords, etc.
""".strip()

MAKE_COMMIT_USER_MESSAGE = """
Generate a concise and informative commit message based on provided file changes.

# Context
## File Change List
```diff
{DIFF_LIST}
```
""".strip()

MAKE_PR_SYSTEM_MESSAGE = """
# Objective
As a programmer, generate a Pull Request (PR) message based on provided information.

## Instructions
1. Analyze the File Change List and Task Info to understand the specific changes in this PR.
2. Generate a concise PR Title that summarizes the main changes.
   - Format as: 'prefix: title'
   - Select the appropriate prefix from:
      - feat: feature development
      - fix: bug fix
      - docs: documentation update
      - test: test update
      - chore: other miscellaneous tasks
3. Create a detailed PR Description following the structure below, ensuring to include:
   - A single, clear statement that combines what was implemented and its purpose
   - Bullet list: group related changes to reduce the number of entries; be concise and high-level
   - Focus on meaningful changes that directly impact functionality or architecture
   - Avoid generic statements about dependencies, package updates, or "no specific impact"
   - Do not add any extra titles, headers, or boilerplate phrases
   - Do not include sensitive information in the output, e.g. API keys, passwords, etc.
   - Do not include extra title or other information not requested

## PR Description Structure

```
[Single clear statement combining implementation and purpose]

**What's included**
• [Grouped, meaningful changes that directly impact functionality]
• [Any specific constraints or impacts on existing features]
```

## Grouping Guidelines
- Group related components and their functionality together
- Combine state management with its associated components
- Merge type definitions with their related implementations
- Example of good grouping:
  ```
  • Implement user authentication with JWT (auth context, login form, protected routes)
  • Add user profile management (profile page, avatar upload, settings)
  ```
  Instead of:
  ```
  • Add AuthContext
  • Add LoginForm
  • Add ProtectedRoute
  • Add ProfilePage
  • Add AvatarUpload
  • Add SettingsPage
  ```

## Response Format
- Do not include any introductions and explanations text in the response
- Do not wrap with code block tags like '```json' or '```' in the response

Note: Keep the message under 200 characters, clear and informative.
""".strip()

MAKE_PR_USER_MESSAGE="""
Generate a concise and informative pr message based on provided file changes.

# Context
## File Change List
{FILE_CHANGE_LIST}

{TASK}
""".strip()
