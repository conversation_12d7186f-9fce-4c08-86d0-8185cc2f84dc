export default [
  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        // Node.js 全局变量
        require: 'readonly',
        module: 'readonly',
        process: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        exports: 'writable',
        global: 'readonly',
        console: 'readonly',
        Buffer: 'readonly'
      }
    },
    rules: {
      // 只保留会直接导致程序崩溃的核心规则
      'no-const-assign': 'error',         // 禁止修改 const 声明的变量
      'no-dupe-args': 'error',            // 禁止函数参数重名
      'no-dupe-keys': 'error',            // 禁止对象字面量中的重复键
      'no-duplicate-case': 'error',       // 禁止 switch 中的重复 case 标签
      'no-empty-pattern': 'error',        // 禁止空解构模式
      'no-ex-assign': 'error',            // 禁止对 catch 子句中的异常重新赋值
      'no-func-assign': 'error',          // 禁止对 function 声明重新赋值
      'no-import-assign': 'error',        // 禁止对导入的绑定进行赋值
      'no-invalid-regexp': 'error',       // 禁止无效的正则表达式
      'no-obj-calls': 'error',            // 禁止将全局对象当作函数调用
      'no-self-assign': 'error',          // 禁止自身赋值
      'no-sparse-arrays': 'error',        // 禁止稀疏数组
      'no-unreachable': 'error',          // 禁止在 return、throw、continue 和 break 后的不可达代码
      'no-unsafe-finally': 'error',       // 禁止在 finally 中出现控制流语句
      'no-unsafe-negation': 'error',      // 禁止对关系运算符的左操作数使用否定操作符
      'use-isnan': 'error',               // 要求使用 isNaN() 检查 NaN
      'valid-typeof': 'error',            // 强制 typeof 表达式与有效字符串比较
      'no-undef': 'error',                // 禁用未声明的变量
    }
  }
];
