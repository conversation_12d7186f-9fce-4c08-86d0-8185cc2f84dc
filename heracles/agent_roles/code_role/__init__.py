from asyncio import Future
from functools import partial
from typing import cast, Literal

import litellm

from heracles.agent_roles.utils import llm_ability, extract_referenced_contents, with_general_loading
from heracles.agent_roles.role_action import AgentRoleTaskActionFinishAction
from heracles.agent_roles.role_base import RoleBase
from heracles.core.schema.models import CommandResultModel, TerminalInteractResponseModel
from heracles.core.utils.context_prompt_builder import PromptBuilder
from heracles.agent_roles.task_role import TaskAction
from heracles.core.exceptions import (
    IDEServerFileNotFoundError, IDEServerFileBinaryError, IDEServerFunCallTimeoutException,
    IDEServerFunCallFailureException, AgentRunException
)
from heracles.core.schema import LLMAbilityType, FileSnippet, FileContent
from heracles.core.schema.task import CommandLifetimeType, FileActionObject, CommandActionObject, ActionType
from heracles.agent_roles.utils import sanitize_generated_text
from .utils import generate_file_diff, add_line_numbers, check_syntax, SUPPORTED_EXTENSIONS, SupportedExtension  # noqa
from .prompts import FULL_CODE_EDIT_SYSTEM_PROMPT, FULL_CODE_EDIT_USER_PROMPT, FULL_CODE_STREAM_APPEND_FILE_PROMPT, \
    REGENERATE_COMMAND_SYSTEM_PROMPT, REGENERATE_COMMAND_USER_PROMPT, \
    GENERATE_INSTRUCTIONS_SYSTEM_PROMPT, GENERATE_INSTRUCTIONS_USER_PROMPT, \
    APPLY_INSTRUCTIONS_SYSTEM_PROMPT, APPLY_INSTRUCTIONS_USER_PROMPT, \
    ESTIMATE_FILE_LINES_PROMPT, ESTIMATE_FILE_LINES_USER_PROMPT, \
    COMMAND_HISTORY_ITEM_TEMPLATE, TERMINAL_INTERACT_SYSTEM_PROMPT, TERMINAL_INTERACT_USER_PROMPT, \
    ANALYZE_CMD_RESULT_SYSTEM_PROMPT, ANALYZE_CMD_RESULT_USER_PROMPT

WRITE_STRATEGY = Literal['stream_append', 'write']


class CodeRole(RoleBase):
    """一个专注于写代码的 Role"""

    def get_llm_ability(self):
        return LLMAbilityType.STRONG

    async def run(self, task_action: TaskAction, is_rerun: bool = False):
        if task_action.action in [ActionType.MODIFY_FILE, ActionType.ADD_FILE, ActionType.DELETE_FILE]:
            #  Code Role 在 run 之前先准备：涉及文件操作时获取文件内容并提前打快照
            original_file_content = await self._handle_original_file_content(task_action)

        action_object = task_action.action_object
        if task_action.action in [ActionType.MODIFY_FILE, ActionType.ADD_FILE]:
            action_object = cast(FileActionObject, action_object)
            res = await self._handle_file_modification(task_action, original_file_content)
            return res

        if task_action.action == ActionType.RUN_COMMAND:
            return await self._handle_command_execution(task_action, is_rerun)

        elif "file" in task_action.action or "directory" in task_action.action:
            action_object = cast(FileActionObject, task_action.action_object)
            if task_action.action == ActionType.DELETE_FILE:
                await self.workspace.tools.delete_file(action_object.path)
            elif task_action.action == ActionType.DELETE_DIRECTORY:
                await self.workspace.tools.delete_directory(action_object.path)
            elif task_action.action == ActionType.CREATE_DIRECTORY:
                await self.workspace.tools.create_directory(action_object.path)
            elif task_action.action == ActionType.MOVE_FILE:
                await self.workspace.tools.move_file(action_object.path, action_object.target)
            elif task_action.action == ActionType.MOVE_DIRECTORY:
                await self.workspace.tools.move_directory(action_object.path, action_object.target)

        return AgentRoleTaskActionFinishAction("done")

    async def _handle_original_file_content(self, task_action: TaskAction) -> str:
        action_object = cast(FileActionObject, task_action.action_object)
        if action_object.snapshot_uuid:
            original_file_content = await self.workspace.tools.query_snapshot_file_by_uuid(action_object.path, action_object.snapshot_uuid)
        else:
            # 虽然 TaskModel 对 action 有校正，但用户手动操作或命令操作后，实际执行仍有可能出现不一致
            if task_action.action == ActionType.MODIFY_FILE:
                try:
                    original_file_content = await self.workspace.tools.read_file_content(action_object.path)
                except IDEServerFileNotFoundError:
                    self.logger.warning(f"[Task] File {action_object.path} not found, will create it")
                    await self.workspace.tools.create_file(action_object.path)
                    original_file_content = ''
            elif task_action.action == ActionType.DELETE_FILE:
                try:
                    original_file_content = await self.workspace.tools.read_file_content(action_object.path)
                except IDEServerFileNotFoundError:
                    raise AgentRunException(f"[Task] File {action_object.path} not found, please check the file path") from None
                except IDEServerFileBinaryError:
                    self.logger.warning(f"[Task] 'delete_file' {action_object.path} is binary or too big, will skip snapshot")
                    original_file_content = ''
            elif task_action.action == ActionType.ADD_FILE:
                try:
                    original_file_content = await self.workspace.tools.read_file_content(action_object.path)
                    self.logger.warning(f"Though action is 'add_file', File {action_object.path} already exists, will overwrite it")
                except IDEServerFileNotFoundError:
                    await self.workspace.tools.create_file(action_object.path)
                    await self.workspace.tools.read_file_content(action_object.path)  # 再读一遍 确保前端focus到文件上
                    original_file_content = ''
            uuid = await self.workspace.tools.snapshot_file(action_object.path, original_file_content)
            action_object.snapshot_uuid = uuid
        return original_file_content

    @with_general_loading(with_start=False)
    async def _handle_file_modification(self, task_action, original_file_content: str):
        # For add_file operations, estimate the lines of code that will be generated
        action_object = cast(FileActionObject, task_action.action_object)
        write_strategy: WRITE_STRATEGY

        async def try_full_file_solution(solution_func):
            try:
                return await solution_func(task_action.id, action_object, original_file_content, write_strategy)
            except litellm.Timeout:
                self.logger.warning(f"Full file solution timed out for {action_object.path}")
                return await self._generate_apply_solution(task_action.id, action_object, original_file_content)

        if task_action.action == ActionType.ADD_FILE:
            # TODO: flash 还是时常出现代码错误，暂不使用 fast 方案，等 lint 集成后再考虑
            write_strategy = 'stream_append' if not original_file_content else 'write'
            new_file_content = await try_full_file_solution(self._strong_full_file_solution)
            # estimated_lines = await self._estimate_file_lines(action_object.path, action_object.detailed_requirement)
            # if estimated_lines <= 100:
            #     new_file_content = await try_full_file_solution(self._strong_full_file_solution)
            # else:
            #     new_file_content = await try_full_file_solution(self._fast_full_file_solution)
        else:
            write_strategy = 'write'
            line_count = original_file_content.count('\n') + 1 if original_file_content else 0

            if line_count <= 100:
                new_file_content = await try_full_file_solution(self._strong_full_file_solution)
            elif line_count <= 400:
                new_file_content = await try_full_file_solution(self._fast_full_file_solution)
            else:
                new_file_content = await self._generate_apply_solution(task_action.id, action_object, original_file_content)
        return await self._process_and_save_file(original_file_content, new_file_content, action_object, write_strategy)

    async def _process_and_save_file(
        self, original_file_content: str, new_file_content: str, action_object: FileActionObject, write_strategy: WRITE_STRATEGY
    ) -> AgentRoleTaskActionFinishAction:
        extension = cast(SupportedExtension, action_object.path.split('.')[-1].lower())

        # 原始代码检查（仅支持的语言）
        original_errors = await check_syntax(original_file_content, extension)

        new_file_content = sanitize_generated_text(new_file_content)
        # Add syntax checking for new code
        if len(original_errors) > 0:
            new_errors = await check_syntax(new_file_content, extension)
            if new_errors:
                self.logger.warning(f'[Code Check] Original no errors, but new contains in {action_object.path}: {str(new_errors)}')

        # Write new content to file
        file_diff = generate_file_diff(original_file_content, new_file_content)
        if file_diff == "":
            return AgentRoleTaskActionFinishAction("No change made for file")
        if write_strategy == 'write':
            await self.workspace.tools.write_file(action_object.path, new_file_content)
        return AgentRoleTaskActionFinishAction(file_diff)

    async def _full_file_solution(self, task_action_id: str, action_object: FileActionObject, original_file_content: str, write_strategy: WRITE_STRATEGY): # noqa
        content_dict = await extract_referenced_contents(action_object.references, self.workspace)
        system_prompt_template = FULL_CODE_EDIT_SYSTEM_PROMPT
        if write_strategy == 'stream_append':
            system_prompt_template += FULL_CODE_STREAM_APPEND_FILE_PROMPT
        system_prompt_builder = PromptBuilder(system_prompt_template, self.workspace)
        system_prompt_builder.playbooks = content_dict['playbooks']
        context_prompt = await system_prompt_builder.format()
        self.system_prompt = context_prompt

        user_prompt_builder = PromptBuilder(FULL_CODE_EDIT_USER_PROMPT, self.workspace)
        if not any(snippet.path == action_object.path for snippet in content_dict['files']):
            user_prompt_builder.file_snippets.append(FileSnippet(path=action_object.path, content=add_line_numbers(original_file_content))) #noqa
        user_prompt_builder.file_snippets += content_dict['files']
        user_prompt_builder.references = content_dict['filtered_references']
        user_prompt_builder.webpages = content_dict['webpages']
        user_prompt = await user_prompt_builder.format()

        if write_strategy == 'write':
            new_file_res = await self.aask(
                user_prompt,
                images=content_dict["images"],
                response_model=FileContent,
                generation_name=f"{task_action_id}",
            )
            new_file_res = new_file_res.content
        elif write_strategy == 'stream_append':
            await self.init_stream_append_file(action_object.path)
            await self.aask(
                user_prompt,
                images=content_dict["images"],
                chunk_callback=self.stream_append_file,
                generation_name=f"{task_action_id}"
            )
            await self.end_stream_append_file()
            new_file_res = self._full_filtered_file_content
        else:
            raise AgentRunException(f"Unsupported write_strategy: {write_strategy}")

        return new_file_res

    async def init_stream_append_file(self, stream_file_path: str = ''):
        await self.workspace.tools.write_file(stream_file_path, '')
        self._stream_file_path = stream_file_path
        self._full_file_content = ''
        self._full_filtered_file_content = ''
        self._accept_content = False
        self._start_marker = '<__file_content__>'
        self._end_marker = '</__file_content__>'
        self._buffer = ''  # 用于缓存未完成的行
        self._stream_activated  = False

    async def end_stream_append_file(self):
        # 当没有激活流式写入时，直接将所有内容写入文件
        if not self._stream_activated:
            await self.workspace.tools.write_file(self._stream_file_path, self._full_file_content)
            self._full_filtered_file_content = self._full_file_content
        self._accept_content = False
        self._buffer = ''
        self._stream_activated = False

    async def stream_append_file(self, chunk: str):
        self._full_file_content += chunk
        self._buffer += chunk

        if '\n' in self._buffer:
            lines = self._buffer.split('\n')
            for line in lines[:-1]:
                raw_line = line
                if self._start_marker in line:
                    self._accept_content = True
                    self._stream_activated = True
                    line = line[line.index(self._start_marker)+len(self._start_marker):]
                if self._end_marker in line:
                    line = line[:line.index(self._end_marker)]
                    if line:
                        await self._append_file_content(self._stream_file_path, line + '\n')
                    self._accept_content = False
                # raw_line == line 处理本身就是空行的情况
                if (line or raw_line == line) and self._accept_content:
                    await self._append_file_content(self._stream_file_path, line + '\n')
            self._buffer = lines[-1]

    async def _append_file_content(self, file_path: str, chunk: str):
        self._full_filtered_file_content += chunk
        await self.workspace.tools.append_file(file_path, chunk)


    @llm_ability(LLMAbilityType.STRONG)
    async def _strong_full_file_solution(self, task_action_id: str, action_object: FileActionObject, original_file_content: str, write_strategy: WRITE_STRATEGY): # noqa
        return await self._full_file_solution(task_action_id, action_object, original_file_content, write_strategy)

    @llm_ability(LLMAbilityType.NORMAL)
    async def _fast_full_file_solution(self, task_action_id: str, action_object: FileActionObject, original_file_content: str, write_strategy: WRITE_STRATEGY): # noqa
        return await self._full_file_solution(task_action_id, action_object, original_file_content, write_strategy)

    async def _generate_apply_solution(self, task_action_id: str, action_object: FileActionObject, original_file_content: str):
        """分两步的Generate & Apply方案"""
        # 第一阶段：生成修改指令
        instructions = await self._generate_edit_instructions(task_action_id, action_object, original_file_content)

        # 第二阶段：应用指令生成代码
        return await self._apply_instructions_for_code(task_action_id, action_object, instructions, original_file_content)

    async def _generate_edit_instructions(self, task_action_id: str, action_object: FileActionObject, file_content: str):
        """生成自然语言修改指令"""
        content_dict = await extract_referenced_contents(action_object.references, self.workspace)

        system_prompt_builder = PromptBuilder(GENERATE_INSTRUCTIONS_SYSTEM_PROMPT, self.workspace)
        system_prompt_builder.playbooks = content_dict['playbooks']
        self.system_prompt = await system_prompt_builder.format()

        user_prompt_builder = PromptBuilder(GENERATE_INSTRUCTIONS_USER_PROMPT, self.workspace)
        user_prompt_builder.file_snippets = [
            FileSnippet(path=action_object.path, content=add_line_numbers(file_content))
        ] + content_dict['files']
        user_prompt_builder.references = content_dict['filtered_references']
        user_prompt_builder.webpages = content_dict['webpages']
        user_prompt = await user_prompt_builder.format(
            REQUIREMENTS=action_object.detailed_requirement
        )
        return await self.aask(
            user_prompt,
            images=content_dict["images"],
            generation_name=f"{task_action_id}",
        )

    @llm_ability(LLMAbilityType.FAST)
    async def _apply_instructions_for_code(self, task_action_id: str, action_object: FileActionObject, instructions: str, original: str):
        """基于指令生成完整代码"""
        content_dict = await extract_referenced_contents(action_object.references, self.workspace)

        system_prompt_builder = PromptBuilder(APPLY_INSTRUCTIONS_SYSTEM_PROMPT, self.workspace)
        system_prompt_builder.playbooks = content_dict['playbooks']
        self.system_prompt = await system_prompt_builder.format()

        user_prompt_builder = PromptBuilder(APPLY_INSTRUCTIONS_USER_PROMPT, self.workspace)
        user_prompt_builder.file_snippets = [
            FileSnippet(path=action_object.path, content=add_line_numbers(original))
        ] + content_dict['files']
        user_prompt_builder.references = content_dict['filtered_references']
        user_prompt_builder.webpages = content_dict['webpages']
        user_prompt = await user_prompt_builder.format(
            REQUIREMENTS=action_object.detailed_requirement,
            INSTRUCTIONS=instructions
        )

        file_content = await self.aask(
            user_prompt,
            images=content_dict["images"],
            generation_name=f"{task_action_id}",
        )
        return file_content

    async def _handle_command_execution(self, task_action: TaskAction, is_rerun: bool):
        action_object = cast(CommandActionObject, task_action.action_object)
        original_command = action_object.command
        max_attempts = 3
        current_attempt = 0
        history_items:list[tuple[str,str]] = []

        while current_attempt < max_attempts:
            try:
                current_attempt += 1
                partial_ai_fix_soft_timeout = partial(self.generate_terminal_interact_response, task_action)
                cmd_args = action_object.command.split(' ')

                need_restore_cwd = any(cmd in cmd_args for cmd in ['cd', 'pushd', 'popd'])
                if need_restore_cwd:
                    cwd = await self.workspace.tools.run_cmd("echo $PWD") # FIXME 这种信息应该缓存，避免重复执行

                try:
                    timeout = 600 if action_object.lifetime == CommandLifetimeType.LONG else 240
                    timeout = 0 if is_rerun else timeout
                    terminal_output = await self.workspace.tools.run_cmd_with_soft_callback(
                        action_object.command,
                        soft_timeout=4,
                        hard_timeout=timeout,
                        soft_callback=partial_ai_fix_soft_timeout
                    )
                except IDEServerFunCallTimeoutException as e:
                    self.logger.warning(f'Command execution failed for Timeout: {e}')
                    raise AgentRunException((
                        'Command ran failed due to timeout, click Retry to try again, '
                        'or you can Copy to terminal and run by yourself to avoid timeout check.'
                    )) from None
                finally:
                    if need_restore_cwd:
                        await self.workspace.tools.run_cmd(f"cd {cwd.strip()}")

                # 检查命令执行结果
                cmd_execute_success, reason = await self.analyze_cmd_result(action_object.command, terminal_output) # noqa
                reason = f"{reason}\n\noriginal terminal output: \n{terminal_output}\n"

                if current_attempt > 1 and cmd_execute_success:
                    await self.workspace.trigger('chunk_message', 'Cool! The new command ran successfully now.\r\n')
                    break

            except IDEServerFunCallFailureException as e:
                cmd_execute_success = False
                reason = str(e)

            if current_attempt >= max_attempts:
                await self.workspace.trigger('chunk_message', f'Sorry that I failed to fix the failed command {task_action.id}, the task will go on running and you can investigate it later.\r\n')  # noqa
                action_object.command = original_command
                raise AgentRunException(f"Command execution failed: {reason}")

            if cmd_execute_success:
                return AgentRoleTaskActionFinishAction('success')

            await self.workspace.trigger('chunk_message', f'Task action {task_action.id} failed: `{action_object.command}`, I will try to fix it...')  # noqa
            new_command = await self._regenerate_command(task_action, reason, history_items)
            action_object.command = new_command
            await self.workspace.trigger('chunk_message', f'\n\nI will rerun with `{new_command}`...\n\n')  # noqa

        return AgentRoleTaskActionFinishAction('success')

    @llm_ability(LLMAbilityType.FAST)
    async def _regenerate_command(self, task_action: TaskAction, error_message: str, history_items: list) -> str:
        action_object = cast(CommandActionObject, task_action.action_object)

        history_items.append((action_object.command, error_message))

        system_prompt_builder = PromptBuilder(REGENERATE_COMMAND_SYSTEM_PROMPT, self.workspace)
        self.system_prompt = await system_prompt_builder.format()

        history_item_str_list = []
        for i, (cmd, err) in enumerate(history_items[:-1]):
            history_item_str_list.append(COMMAND_HISTORY_ITEM_TEMPLATE.format(
                    attempt_number=i+1,
                    command=cmd,
                    error=err
            ))

        user_prompt_builder = PromptBuilder(REGENERATE_COMMAND_USER_PROMPT, self.workspace)
        user_prompt = await user_prompt_builder.format(
            COMMAND=action_object.command,
            ERROR_MESSAGE=error_message,
            COMMAND_HISTORY_ITEMS='\n'.join(history_item_str_list)
        )
        command = await self.aask(user_prompt, generation_name=f'{task_action.id}')
        return command.strip()

    @llm_ability(LLMAbilityType.FAST)
    async def generate_terminal_interact_response(self, action: CommandActionObject, future: Future):
        # 1. 获取终端结果
        cmd = future._cmd # type: ignore
        terminal_result = future._terminal_result.to_string() # type: ignore

        # 2. 使用 LLM 分析终端结果并生成响应
        self.system_prompt = await PromptBuilder(TERMINAL_INTERACT_SYSTEM_PROMPT, self.workspace).format(
            MIDDLEWARE_LIST=await self.workspace.tools.list_bound_middlewares_in_environment()
        )

        user_prompt = await PromptBuilder(TERMINAL_INTERACT_USER_PROMPT, self.workspace).format(
            command=cmd,
            terminal_result=terminal_result
        )

        response = await self.aask(user_prompt, response_model=TerminalInteractResponseModel)

        if response.needs_input:
            return response.response
        return None

    @llm_ability(LLMAbilityType.FAST)
    async def analyze_cmd_result(self, command: str, output: str) -> tuple[bool, str]:
        """检查命令执行结果是否成功

        Args:
            command: 执行的命令
            output: 命令的输出结果

        Raises:
            AgentRunException: 当命令执行失败时抛出异常
        """
        output = output or ''

        self.system_prompt = await PromptBuilder(ANALYZE_CMD_RESULT_SYSTEM_PROMPT, self.workspace).format()

        user_prompt = await PromptBuilder(ANALYZE_CMD_RESULT_USER_PROMPT, self.workspace).format(
            command=command,
            output=output
        )

        result = await self.aask(user_prompt, response_model=CommandResultModel)

        return result.success, result.reason

    async def _estimate_file_lines(self, file_path: str, requirements: str) -> int:
        """Estimates the number of lines of code that will be generated for a new file.

        Args:
            file_path: Path to the file being created
            requirements: Detailed requirements for the file

        Returns:
            int: Estimated number of lines of code
        """
        system_prompt_builder = PromptBuilder(ESTIMATE_FILE_LINES_PROMPT, self.workspace)
        self.system_prompt = await system_prompt_builder.format()

        user_prompt = await PromptBuilder(ESTIMATE_FILE_LINES_USER_PROMPT, self.workspace).format(
            FILE_PATH=file_path,
            REQUIREMENTS=requirements
        )

        result = await self.aask(user_prompt)

        # Parse the result to get an integer
        try:
            estimated_lines = int(result.strip())
            return estimated_lines
        except ValueError:
            self.logger.warning(f"Failed to parse line estimation result: {result}")
            return 0
