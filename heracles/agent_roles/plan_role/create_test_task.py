from heracles.core.schema.task import ActionType, Task, TaskAction, TaskStep, FileActionObject, CommandActionObject, CommandLifetimeType


def create_test_task():
    task = Task(title="添加用户注册问题")  # 添加 id 和 title
    task_step_1 = TaskStep(title="添加模型")
    task.add_task_step(task_step_1)
    task_step_1_action_1 = TaskAction(
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path="model.rb",
            target="createModel",
            detailed_requirement="控制器类需要继承自 ApplicationController"
        )
    )
    task_step_1_action_2 = TaskAction(
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path="model2.rb",
            target="writeUserClass",
            detailed_requirement="控制器类需要继承自 ApplicationController"
        )
    )
    task_step_1.add_task_action(task_step_1_action_1)
    task_step_1.add_task_action(task_step_1_action_2)

    task_step_2 = TaskStep(title="创建控制器")
    task.add_task_step(task_step_2)
    task_step_2_action_1 = TaskAction(
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path="controller.rb",
            target="createController",
            detailed_requirement="控制器类需要继承自 ApplicationController"
        )
    )
    task_step_2_action_2 = TaskAction(
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command="echo 'hello world'",
            lifetime=CommandLifetimeType.SHORT
        )
    )
    task_step_2_action_3 = TaskAction(
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command="echo 'hello world'",
            lifetime=CommandLifetimeType.LONG
        )
    )
    task_step_2.add_task_action(task_step_2_action_1)
    task_step_2.add_task_action(task_step_2_action_2)
    task_step_2.add_task_action(task_step_2_action_3)
    return task
