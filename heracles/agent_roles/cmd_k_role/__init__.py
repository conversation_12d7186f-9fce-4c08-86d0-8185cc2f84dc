from heracles.core.schema import LLMAbilityType
from heracles.agent_roles.role_base import RoleBase
from heracles.core.exceptions import UserEventArgumentException
from heracles.core.utils.context_prompt_builder import PromptBuilder
from heracles.agent_roles.code_role.utils import async_fix_code_indention
from .prompts import CMDK_SYSTEM_PROMPT, CMDK_USER_PROMPT

class CmdKRole(RoleBase):
    """ Cmd + K 支持
    """

    def get_llm_ability(self):
        return LLMAbilityType.NORMAL

    async def run(self, file_path: str, start_line: int, end_line: int, user_prompt: str, trigger_callback):
        self.check_cmd_k_params(file_path, start_line, end_line)

        original_file_content = await self.workspace.tools.read_file_content(file_path)
        file_lines = original_file_content.split('\n')

        selected_code_snippet = file_lines[start_line - 1:end_line]
        marked_selected_code_snippet = ["*" + line for line in selected_code_snippet]
        adjusted_file_content = '\n'.join(file_lines[:start_line - 1] + marked_selected_code_snippet + file_lines[end_line:])

        system_prompt_builder = PromptBuilder(CMDK_SYSTEM_PROMPT, self.workspace)
        self.system_prompt = await system_prompt_builder.format()

        user_prompt_builder = PromptBuilder(CMDK_USER_PROMPT, self.workspace)
        # FIXME: RAG 尚无法发挥价值，会导致数秒延时
        # related_code_search_context = f'{user_prompt}\n{selected_code_snippet}'.strip('\n')
        # related_file_snippets = await self.workspace.tools.search_related_code(related_code_search_context, need_expand=False)
        # user_prompt_builder.file_snippets = related_file_snippets
        user_prompt = await user_prompt_builder.format(
            FILE_CONTENT=adjusted_file_content,
            SELECTED_SNIPPET='\n'.join(selected_code_snippet),
            USER_PROMPT=user_prompt
        )
        new_file_content = await self.aask(user_prompt, chunk_callback=trigger_callback)
        edited_lines = await async_fix_code_indention(selected_code_snippet, new_file_content.split('\n'))
        new_file_content = '\n'.join(edited_lines)
        return new_file_content

    def check_cmd_k_params(self, file_path: str, start_line: int, end_line: int):
        if start_line <= 0 or end_line <= 0:
            raise UserEventArgumentException("start_line and end_line must be positive integers")
        if start_line > end_line:
            raise UserEventArgumentException("start_line must be less than or equal to end_line")

    async def code_completion_action(self, file_path: str, code_snippet: str):
        content = ''
        return content


__all__ = ['CmdKRole']
