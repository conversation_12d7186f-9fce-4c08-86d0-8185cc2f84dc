from heracles.core.schema.spec import Spec

def create_test_spec(goal, goal_detail):
    spec_data = {
        'suggested_name': 'test_name',
        'suggested_branch': 'test_branch',
        'goal': goal,
        'goal_detail': goal_detail,
        'current_list': ['this is an current example info'],
        'proposed_list': ['this is an proposed_list example info'],
        'proposed_filechange_list': ['app/new/page.tsx'],
        'task_scale': 'micro'
    }
    spec = Spec.model_validate(spec_data)
    return spec
