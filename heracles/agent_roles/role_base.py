import json
from abc import ABC, abstractmethod
from typing import List, Tuple, Union, Optional, Iterable, get_origin
from collections.abc import Iterable as CollectionsIterable

import traceback
from pydantic import BaseModel, ValidationError

from heracles.agent_controller.llm import LLM
from heracles.agent_memory.memory import Memory
from heracles.agent_roles.role_action import Agent<PERSON><PERSON><PERSON><PERSON>A<PERSON>
from heracles.core.exceptions import Agent<PERSON><PERSON>Ex<PERSON>, IDEServerFunCallException
from heracles.core.schema import Too<PERSON><PERSON><PERSON><PERSON>, LangfuseTraceOption, LLMAbilityType, SubBaseModelOrIterable, MemoryMessage
from heracles.agent_roles.utils import llm_ability, async_sanitize_generated_json
from heracles.core.utils.find_caller import find_caller
from heracles.core.utils.llm_function import llm_function_to_json, response_model_to_llm_tool_schema, response_model_to_json_schema

from .prompts import MULTIPLE_RESPONSE_MODEL_FIX_PROMPT, RESPONSE_MODEL_AUTOFIX_PROMPT


# 代理人的基类
# 项目资料
# 目标任务
# 短记忆
# 长记忆
# 工具集合 / 事件监听
# RAG检索


class RoleBase(ABC):
    """所有 Role 的基类, 所有 Role 都应该从这里继承"""

    def __init__(self, workspace):
        self.system_prompt = ''
        self.workspace = workspace
        self.memory = Memory()
        self.logger = self.workspace.logger
        self.llm = LLM(self.get_llm_ability())
        self._set_llm_metadata()

    def _set_llm_metadata(self):
        langfuse_option = LangfuseTraceOption(
            trace_id=self.workspace.playground.playground_id, trace_name=self.__class__.__name__, generation_name=self.__class__.__name__
        )
        self.llm.set_llm_metadata(langfuse_option)

    @abstractmethod
    def get_llm_ability(self) -> LLMAbilityType:
        return LLMAbilityType.NORMAL

    @abstractmethod
    def run(self, *args):
        """每个 Role 开始思考与执行的地方"""
        return AgentRoleNullAction()

    async def aask(
        self,
        message: str,
        *,
        images: Optional[List[str]] = None,
        memory=False,
        tools=None,
        tools_callback=None,
        chunk_callback=None,
        auto_save=True,
        auto_call=True,
        response_model: Optional[type[SubBaseModelOrIterable]] = None,
        response_model_optional=False,
        memory_regenerate=False,
        generation_name: Optional[str] = None,
        post_text_validate_model=None,
        reasoning_effort: Optional[str] = None,
        max_tool_call_loop: int = 7,
    ):
        """封装更易用的 llm 调用接口, 带有回忆(默认不开) 和保存记忆功能(默认启用)
        :param message: str, 要发送的消息, 会根据 memory 设定自动组装带有全部记忆的版本或者只带有 system_prompt + message 的版本
        :param memory: bool, 是否打开记忆
        :param tools: 要支持的 tools 列表( 从 workspace 中获取 )
        :param tools_callback: 执行 tools 时的回调
        :param chunk_callback: 设定 stream 模式, 并实时读取 chunk_word, 一般用于聊天实时回复
        :param auto_save: bool, 成功调用后, 自动保存记忆(默认启用)
        :param auto_call: 当传入了 tools, 如果大模型决定要调用, 这个参数决定是否自动调用, 返回结果会自动转为 str, 添加为结果
        :param response_model: 指定一个响应对象, 返回值是它的一个实例
        :param response_model_optional: 是否允许可选生成 response_model, 默认必须生成 (通过 tool_choice=required 来控制)
        :param memory_regenerate: 是否就最近一条聊天进行重新生成, 会自动移掉最近一对用户/模型 memory
        :param generation_name: str, 用于 langfuse 的 generation_name，默认为对应方法名
        :param post_text_validate_model: 让模型以文本生成model, 并在最后验证结果 - (此时 response_model 应该为 None并在prompt中说明model schema, 一般用于流式输出model生成过程)
        :param max_tool_call_loop: int, 工具自动调用最大循环次数，默认7

        注意: auto_call 为 False 时, 返回值是一个 ToolCalls, 需要自行判断条件再调用, 举例

        ```
        for tool_call in ToolCalls:
            res = await self._function_call(tool_call)
            print(res)
        ```
        """  # noqa
        caller_name = find_caller()
        if generation_name:
            self.llm.update_llm_metadata('generation_name', f'{self.__class__.__name__}:{caller_name}:{generation_name}')
        else:
            self.llm.update_llm_metadata('generation_name', f'{self.__class__.__name__}:{caller_name}')

        if post_text_validate_model and response_model:
            raise AgentRunException('post_text_validate_model and response_model should not be set at the same time')

        tool_choice = 'auto'
        # tools 与 response_model 兼容用法
        tool_response_model = None
        if tools or response_model is not None:
            if not tools:
                tools = []
            else:
                tools = self._get_llm_function_info_list(tools)
            if response_model is not None:
                tool_response_model = response_model
                response_model = None
                tools.append(response_model_to_llm_tool_schema(tool_response_model))
                if not response_model_optional:
                    tool_choice = 'required'

        if memory and memory_regenerate:
            self.logger.debug('remove newest pair memories')
            self.memory.remove_newest_pair_memories()

        messages = self._make_llm_messages(message, images=images, memory=memory, tools=tools)
        res = await self.llm.async_send(
            origin_messages=messages,
            tools=tools,
            tools_callback=tools_callback,
            tool_choice=tool_choice,
            chunk_callback=chunk_callback,
            reasoning_effort=reasoning_effort,
        )

        async def check_res_and_auto_call_once(
            res, messages, tool_call_loop: int, max_tool_call_loop: int
        ) -> Tuple[Union[str, ToolCalls], List[str], List[dict]]:
            if not isinstance(res, ToolCalls):
                raise AgentRunException('check_res_and_auto_call error')

            tool_calls = res
            new_memories = []
            tool_call_message = {
                'role': 'assistant',
                'content': tool_calls.message,
                'tool_calls': [tool_call.model_dump() for tool_call in tool_calls.functions],
            }
            new_memories.append(tool_call_message)
            messages.append(tool_call_message)
            for tool_call in tool_calls.functions:
                try:
                    res_function_call = await self._function_call(tool_call)
                except IDEServerFunCallException as e:
                    self.logger.warning(f'Traceback: {traceback.format_exc()}')
                    res_function_call = f'call error: {str(e)}'
                except Exception as e:
                    # FIXME 考虑给用户侧上报错误
                    self.logger.warning(f'Traceback: {traceback.format_exc()}')
                    res_function_call = f'fatal: call error: {str(e)}'

                tool_call_response = {
                    'role': 'tool',
                    'tool_call_id': tool_call.id,
                    'name': tool_call.function.name,
                    'content': str(res_function_call),
                }
                new_memories.append(tool_call_response)
                messages.append(tool_call_response)

            new_tools = tools
            new_tool_choice: str | None = tool_choice
            # 如果达到最大调用次数，则发送最终指令并限制工具
            if tool_call_loop >= max_tool_call_loop:
                self.logger.warning(f'aask: auto_call tools {tool_call_loop} times, send final instruction')
                final_instruction = {
                    'role': 'user',
                    'content': "You have reach the max_tool_call limit, now carefully give the answer or conclude that you can't.",
                }
                new_memories.append(final_instruction)
                messages.append(final_instruction)

                if tool_response_model:
                    new_tools = [response_model_to_llm_tool_schema(tool_response_model)]
                else:
                    new_tool_choice = None

            new_res = await self.llm.async_send(
                origin_messages=messages,
                tools=new_tools,
                tools_callback=tools_callback,
                tool_choice=new_tool_choice,
                chunk_callback=chunk_callback,
                reasoning_effort=reasoning_effort,
            )
            if isinstance(new_res, type) and issubclass(new_res, BaseModel):  # 确保类型检查通过
                raise AgentRunException('check_res_and_auto_call error: return a BaseModel')
            return new_res, messages, new_memories

        # 自动调用 function_call, 并尝试重复调用 LLM 将结果进行处理, 默认最多3次
        all_new_memories = []  # 记录tool_calls产生的新记忆
        if auto_call and isinstance(res, ToolCalls):
            tool_call_loop = 0
            new_res: Union[str, ToolCalls, BaseModel, Iterable[BaseModel]] = res
            new_messages = messages
            while True:
                if not isinstance(new_res, ToolCalls):
                    break
                # TODO: 临时方案：判断 function 是否有大写（例如xxModel），后续需要进一步优化
                models = [
                    function
                    for function in new_res.functions
                    if function.function.name and any(char.isupper() for char in function.function.name)
                ]
                if len(models) == len(new_res.functions):
                    res_model = models[0].model_dump()
                    model_function_dict = res_model['function']
                    if len(models) > 1:
                        model_function_dict['arguments'] = await self.merge_multiple_response_models(models, tool_response_model)
                    new_res = await self.validate_model_with_retry(
                        tool_response_model, model_function_dict['arguments'], tools=tools, origin_messages=messages
                    )
                    tool_call_message = {'role': 'assistant', 'content': res.message, 'tool_calls': [res_model]}
                    tool_call_response = {
                        'role': 'tool',
                        'tool_call_id': res_model['id'],
                        'name': model_function_dict['name'],
                        'content': 'Success',
                    }
                    all_new_memories += [tool_call_message, tool_call_response]
                else:
                    if any(models):
                        # 将 models 从 functions 中移除
                        new_res.functions = [
                            function
                            for function in new_res.functions
                            if function.function.name and not any(char.isupper() for char in function.function.name)
                        ]
                    _new_res, _new_messages, new_memories = await check_res_and_auto_call_once(
                        new_res, new_messages, tool_call_loop, max_tool_call_loop
                    )
                    new_res = _new_res
                    new_messages = _new_messages
                    all_new_memories += new_memories
                    tool_call_loop += 1
            res = new_res
        # 只要是 chunk 模式调用，在请求时就会发送终止符号，用于前端提供判断消息结束依据
        if chunk_callback:
            await chunk_callback('\r\n')

        # 模型最后返回的结果为空，则抛出异常
        if isinstance(res, str):
            if not res:
                raise AgentRunException('LLM returned empty response, please retry')

        # 成功后再保存记忆
        if memory and auto_save:
            self.memory.add_user_message(message)
            for memory in all_new_memories:
                self.memory.add_from_dict(memory)
            if isinstance(res, str):
                self.memory.add_assistant_message(res)

        if isinstance(res, str) and post_text_validate_model:
            res = await self.validate_model_with_retry(post_text_validate_model, res, tools=tools, origin_messages=messages)
        return res

    async def aask_with_memory(
        self,
        message: str,
        *,
        images: Optional[List[str]] = None,
        tools=None,
        tools_callback=None,
        auto_call=True,
        response_model=None,
        response_model_optional=False,
        chunk_callback=None,
        memory_regenerate=False,
        post_text_validate_model=None,
        reasoning_effort: Optional[str] = None,
        max_tool_call_loop: int = 7,
    ):
        """同 aask, 开启记忆与自动保存功能"""
        return await self.aask(
            message,
            images=images,
            tools=tools,
            tools_callback=tools_callback,
            chunk_callback=chunk_callback,
            memory=True,
            auto_save=True,
            auto_call=auto_call,
            response_model=response_model,
            response_model_optional=response_model_optional,
            memory_regenerate=memory_regenerate,
            post_text_validate_model=post_text_validate_model,
            reasoning_effort=reasoning_effort,
            max_tool_call_loop=max_tool_call_loop,
        )

    async def merge_multiple_response_models(self, models, tool_response_model):
        if len(models) > 1:
            self.logger.warning(f'Multiple {tool_response_model} called, will ask AI to merge until one left')
            return await self.llm.async_send(
                MULTIPLE_RESPONSE_MODEL_FIX_PROMPT.format(
                    DUPLICATE_MODELS_DICT=[await async_sanitize_generated_json(model.function.arguments) for model in models],
                    MODEL_SCHEMA=response_model_to_json_schema(tool_response_model),
                )
            )

    # @with_general_loading
    @llm_ability(LLMAbilityType.FAST)
    async def validate_model_with_retry(self, model, model_json, *, tools=None, origin_messages=None, retry_count=0):
        """Validate and retry model instantiation with LLM assistance if needed."""
        MAX_RETRIES = 2
        if isinstance(model_json, ToolCalls):
            if len(model_json.functions) > 0:
                model_json = model_json.functions[0].function.arguments
        try:
            model_dict = await async_sanitize_generated_json(model_json)
            if get_origin(model) == CollectionsIterable:
                item_type = model.__args__[0]
                items = model_dict.get('items', [])
                if not isinstance(items, list):
                    items = [items]
                return [item_type(**item) for item in items]
            return model(**model_dict)
        except (json.JSONDecodeError, ValidationError, TypeError) as e:
            if retry_count >= MAX_RETRIES:
                raise AgentRunException(f'LLM model validation error and reached max retries: {str(e)}') from None
            self.logger.warning(f'Validate {model} failed (attempt {retry_count + 1}/{MAX_RETRIES}), Error: {e}')
            # Ask LLM to fix the model dict
            user_message = RESPONSE_MODEL_AUTOFIX_PROMPT.format(ERROR_MESSAGE=str(e), MODEL_SCHEMA=response_model_to_json_schema(model))
            if origin_messages:
                origin_messages.append({'role': 'assistant', 'content': model_json})
                origin_messages.append({'role': 'user', 'content': user_message})
                fixed_json = await self.llm.async_send(origin_messages=origin_messages, tools=tools)
            else:
                fixed_json = await self.llm.async_send(user_message)
            return await self.validate_model_with_retry(
                model, fixed_json, tools=tools, origin_messages=origin_messages, retry_count=retry_count + 1
            )

    async def _function_call(self, tool_call):
        """用于帮助 Role 调用来自大模型返回的 tool_call 函数调用

        架构上设计我们都是从 workspace 取来 tools, 所以去找 workspace 的 tools 直接调用即可

        注意: 这个接口会抛异常, 使用要非常小心
        """
        function_name = tool_call.function.name
        function_args = await async_sanitize_generated_json(tool_call.function.arguments or '{}')
        self.logger.info(f'Will call: {function_name}, {function_args}')
        function_call = getattr(self.workspace.tools, function_name)
        res = await function_call(**function_args)
        return res

    def _make_llm_messages(
        self, message: str, *, images: Optional[List[str]] = None, memory: bool = False, tools: Optional[List[str]] = None
    ):
        messages: List[dict] = []
        user_message = MemoryMessage(role='user', content=message)
        if images:
            user_message = MemoryMessage.new(message, images)
        if memory:
            messages = self.memory.to_llm_messages(self.system_prompt)
            messages.append(user_message.model_dump())
        else:
            if self.system_prompt:
                messages.append({'role': 'system', 'content': self.system_prompt})
            messages.append(user_message.model_dump())
        return messages

    def _get_llm_function_info_list(self, tools: list) -> list:
        """Return usable FunCall information

        Parameters
        ----------
        tools : list
            List of function names, e.g. ['file_tree', 'write_file']
        """

        # Reference
        # https://litellm.vercel.app/docs/completion/function_call
        # https://github.com/BerriAI/litellm/blob/main/litellm/utils.py#L5051
        def single_function_name_to_json(function_name):
            target_method = getattr(self.workspace.tools, function_name)
            if not target_method:
                raise AgentRunException(f'_get_llm_function_info_list error: `{function_name}` not found')
            return llm_function_to_json(target_method)

        return list(map(lambda x: single_function_name_to_json(x), tools))
