import asyncio
import litellm
import re
import json_repair
from typing import List, Dict, Any, cast
from urllib.parse import urlparse
from heracles.agent_workspace.playbook import Playbook
from heracles.core.schema import FileSnippet, ProjectErrorMessage, Webpage
from heracles.core.schema import LLMAbilityType
from heracles.core.exceptions import IDEServerFileNotFoundError, IDEServerFileBinaryError, IDEServerFunCallException, AgentRunException
from heracles.core.logger import heracles_logger as logger
import functools
from collections import OrderedDict
from heracles.core.config import get_env_var

def llm_ability(ability_type):
    def decorator(func):
        async def wrapper(self, *args, **kwargs):
            if self.llm.ability_type != ability_type:
                original_ability = self.llm.ability_type
                self.llm.ability_type = ability_type
                try:
                    log_msg = []
                    if self.llm.ability_type != original_ability:
                        log_msg.append(f"能力为 {ability_type}")
                    logger.warning(f"临时设置 LLM {' '.join(log_msg)}")
                    return await func(self, *args, **kwargs)
                except litellm.exceptions.Timeout:
                    logger.warning(f"LLM 超时，设置能力为 {LLMAbilityType.FAST} 并重试...")
                    self.llm.ability_type = LLMAbilityType.FAST
                    return await func(self, *args, **kwargs)
                finally:
                    self.llm.ability_type = original_ability
                    log_msg = []
                    if ability_type != original_ability:
                        log_msg.append(f"能力为 {original_ability}")
                    logger.warning(f"恢复 LLM {' '.join(log_msg)}")
            else:
                return await func(self, *args, **kwargs)
        return wrapper
    return decorator

def with_general_loading(func=None, with_start=True):
    if func is None:
        return lambda f: with_general_loading(f, with_start=with_start)

    if not callable(func):
        # 如果 func 不是可调用对象，说明装饰器被错误地调用了
        raise TypeError(f"with_general_loading 装饰器被错误地调用: {func}")

    @functools.wraps(func)
    async def wrapper(self, *args, **kwargs):
        if hasattr(self, 'workspace'):
            if with_start:
                await self.workspace.trigger_general_loading(func.__name__, 'start')
            try:
                return await func(self, *args, **kwargs)
            finally:
                await self.workspace.trigger_general_loading(func.__name__, 'end')
        else:
            # 如果 self 没有 workspace 属性，直接调用原函数
            return await func(self, *args, **kwargs)

    return wrapper

def sanitize_generated_text(content: str) -> str:
    """清理AI生成的文本格式"""
    content = re.sub(r'<think>[\s\S]*?</think>', '', content)
    if content.startswith('```'):
        content = '\n'.join(content.split('\n')[1:])
    if content.endswith('```'):
        content = '\n'.join(content.split('\n')[:-1])
    return content

def sanitize_generated_json(content: str) -> Dict[str, Any]:
    """清理AI生成的JSON格式"""
    content = sanitize_generated_text(content)
    parsed_content = json_repair.loads(content)
    return cast(Dict[str, Any], parsed_content)

async def async_sanitize_generated_json(content: str) -> Dict[str, Any]:
    return await asyncio.to_thread(sanitize_generated_json, content)

def convert_examples_to_prompt(examples: List[Dict[str, str]]) -> str:
    prompts = []
    for example in examples:
        user_input = example["user"]
        assistant_output = example["assistant"]
        prompts.append(
            f"<Example>\n\n```\n<Input>\n{user_input}\n</Input>\n<Output>\n{assistant_output}\n</Output>\n```\n\n</Example>"
        )
    return "\n".join(prompts)

async def extract_referenced_contents(references: List[str], workspace) -> dict:
    files: list[FileSnippet] = []
    playbooks: list[Playbook] = []
    filtered_references: list[str] = []
    images: list[str] = []
    errors: list[ProjectErrorMessage] = []
    webpages: list[Webpage] = []
    unique_references = list(OrderedDict.fromkeys(references).keys())

    for reference in unique_references:
        if reference.startswith('file://'):
            try:
                file_reference = reference[7:]  # 去掉 'file://'
                file_path, row_start, row_end = file_reference, 0, 0
                if ':' in file_reference:
                    file_path, lines = file_reference.split(':')
                    if '-' in lines:
                        row_start, row_end = map(int, lines.split('-'))
                if row_start == 0 or row_end == 0:
                    file_content = await workspace.tools.read_file(
                        file_path, should_read_entire_file=True, with_line_numbers=True
                    )
                    row_start, row_end = 1, len(file_content.split('\n'))
                else:
                    file_content = await workspace.tools.read_file(
                        file_path,
                        start_line=row_start,
                        end_line=row_end,
                        with_line_numbers=True
                    )
                files.append(FileSnippet(path=file_path, content=file_content, row_start=row_start, row_end=row_end))
            except IDEServerFileNotFoundError:
                workspace.logger.warning(f"Referenced file `{file_path}` not found")
            except IDEServerFileBinaryError:
                workspace.logger.warning(f"Referenced file `{file_path}` is binary or too big")
            except IDEServerFunCallException as e:
                workspace.logger.warning(f"Referenced file `{file_path}` error: {e}")
        elif reference.startswith('image_url://'):
            # TODO: 不支持读本地图片, 需要把仓库图片变成可访问的
            if not get_env_var('ENABLE_IMAGE_REFERENCE_OPTIONAL', True):
                continue
            image_url = reference[12:]
            if image_url.startswith('data:image/jpeg;base64,'):
                images.append(image_url)
                continue
            parsed_image_url = urlparse(image_url)
            if parsed_image_url.scheme in ['http', 'https']:
                try:
                    validated_image_url = await workspace.tools.validate_image_url(image_url)
                    if validated_image_url:
                        images.append(validated_image_url)
                except AgentRunException as e:
                    workspace.logger.warning(f"Referenced image `{image_url}` error: {e}")
            else:
                workspace.logger.warning(f"Referenced image `{image_url}` is not a valid URL")
        elif reference.startswith('playbook://'):
            playbook = workspace.playbook_manager.get(reference[11:])
            if not playbook:
                workspace.logger.warning(f"Referenced playbook `{reference[11:]}` not found")
            else:
                playbooks.append(playbook)
        elif reference.startswith('webpage://'):
            url = reference[10:]
            try:
                content = await workspace.tools.read_webpage(url)
                webpages.append(Webpage(url=url, content=content))
            except AgentRunException as e:
                workspace.logger.warning(f"Referenced web `{url}` error: {e}")
        elif reference.startswith('error://'):
            ref_id = reference[8:]
            error_message_obj = ProjectErrorMessage.find_by_id(ref_id, workspace)
            if not error_message_obj:
                workspace.logger.warning(f"Referenced error message `{ref_id}` not found")
            else:
                errors.append(error_message_obj)
        else:
            filtered_references.append(reference)

    # TODO: 更合理的图片数量限制
    return {
        'files': files,
        'playbooks': playbooks,
        'filtered_references': filtered_references,
        'errors': errors,
        'images': images[:5],
        'webpages': webpages,
        'origin_references': unique_references
    }


async def extract_user_message(message: str, workspace) -> tuple[str, dict]:
    message_content = message
    content_dict: dict[str, list] = {'files': [], 'errors': [], 'images': [], 'webpages': []}
    if '@@user_context' in message:
        message_content = message.split('@@user_context[')[0]
        references_str = message.split('@@user_context[')[1].split(']@@')[0]
        references = list(map(lambda x: x.strip(), references_str.split(',')))
        content_dict = await extract_referenced_contents(references, workspace)
    return message_content, content_dict
