MULTIPLE_RESPONSE_MODEL_FIX_PROMPT = """We encountered a data validation issue, generated data cannot work due to duplication issue.

Please check the problematic objects and fix them into one object:

```json
{DUPLICATE_MODELS_DICT}
```

### Model Schema:
Merge the problematic objects, and generate a new object following the model schema.

```json
{MODEL_SCHEMA}
```

### Important:
- Respond with pure JSON format matching the model schema.
- DO NOT add any other introduction or comments.
- DO NOT use '```json' or '```' to wrap your response.
- Please ensure that the data object is in the correct format and all required fields are present.
"""

RESPONSE_MODEL_AUTOFIX_PROMPT = """We encountered a data validation issue, the program returned the following error message:

```
{ERROR_MESSAGE}
```

Here is the model schema to be generated:

```json
{MODEL_SCHEMA}
```

Please check the problematic object and fix it.

### Response Format:
- Respond with pure JSON format matching the model schema.
- DO NOT add any other introduction or comments.
- DO NOT use '```json' or '```' to wrap your response.
- Please ensure that the data object is in the correct format and all required fields are present.
- DO NOT change the value of any field in the data object."""
