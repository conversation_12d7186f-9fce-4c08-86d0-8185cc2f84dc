from typing import Callable
from heracles.core.utils.event_emitter import EventEmitter

class Observations:
    def __init__(self, workspace):
        self.workspace = workspace
        self.playground = self.workspace.playground
        self.event_emitter = EventEmitter()

    def on_message(self, handler: Callable):
        self.event_emitter.on('message', handler)

    def off_message(self, handler: Callable):
        self.event_emitter.off('message', handler)

    def on_terminal(self, handler: Callable):
        self.playground.on_ide_event('terminal', handler)

    def off_terminal(self, handler):
        self.playground.off_ide_event('terminal', handler)
