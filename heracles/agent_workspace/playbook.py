import re
import asyncio
from jinja2 import Template
from typing import Literal
from pydantic import BaseModel
from heracles.core.exceptions import AgentRunException


class Playbook(BaseModel):
    """ playbook 记录 markdown 技术文章
    """
    title: str
    description: str = ''
    tags: list[str]
    applicable_rules: list[str]
    original_content: str
    content: str = ''
    status: Literal['inited', 'processing', 'not_applicable', 'ready', 'pending'] = 'inited'
    source: Literal['system', 'user', 'ai'] = 'system'

    @property
    def id(self):
        return self.title.lower().replace(' ', '-').replace('.', '')

    def dict(self, **kwargs):
        d = super().dict(include={'title', 'description', 'tags'}, exclude_defaults=True)
        d['id'] = self.id
        return d

    async def process(self, workspace):
        self.status = 'processing'
        if not await self._check_applicable(workspace):
            self.status = 'not_applicable'
            return False
        self.status = 'ready'
        return True

    async def _check_applicable(self, workspace):
        async def check_rule(rule):
            result = await replace_calls(rule, workspace)
            if isinstance(result, str):
                if result.lower() == 'true' or result.lower() == 'not false':
                    return True
                elif result.lower() == 'false' or result.lower() == 'not true':
                    return False
            raise AgentRunException(f"Check rule error: `{rule}` result is not a boolean, result: {result}")
        if self.applicable_rules:
            results = [await check_rule(rule) for rule in self.applicable_rules]
            return all(results)
        return True

    async def render_content(self, workspace) -> str:
        try:
            if self.source == 'system':
                replaced_content = await replace_calls(self.original_content, workspace)
                return Template(replaced_content).render()
            else:
                return Template(self.original_content).render()
        except Exception as e:
            raise AgentRunException(f"Render playbook content error: {e}") from None

async def replace_calls(content: str, workspace):
    # 兼容 xxx://xxx(), xxx://xxx("param") 和 xxx://xxx 三种格式
    pattern = r'(\w+)://(\w+)(?:\("([^"]*)"\)|\(\)|)'
    matches = []
    for match in re.finditer(pattern, content):
        key, name, param = match.groups()
        matches.append((key, name, param, match))

    # 如果没有匹配项，直接返回原内容
    if not matches:
        return content

    # 从后向前替换，避免位置变化影响
    for key, name, param, match in reversed(matches):
        if key not in ['tools', 'project_knowledge']:
            raise AgentRunException(f"Invalid schema: {key}")

        replacement = await call_function_or_property(key, name, param, workspace)
        replacement = replacement.replace('"', '\\"')
        start, end = match.span()
        content = content[:start] + replacement + content[end:]

    return content

async def call_function_or_property(key, name, param, workspace):
    if key not in ['tools', 'project_knowledge']:
        raise AgentRunException(f"Invalid schema: {key}")

    target = workspace.tools if key == 'tools' else workspace.project_knowledge

    if key == 'tools':
        whitelist = ['check_path_exists', 'list_bound_middlewares_in_environment', 'middlewares_not_empty']
        if name not in whitelist:
            raise AgentRunException(f"tools.{name} is not whitelisted, not allowed to call")

    if not hasattr(target, name):
        raise AgentRunException(f"`{key}.{name}` not found")

    obj = getattr(target, name)

    try:
        if not callable(obj):
            # 处理属性访问
            if param is not None:
                raise AgentRunException(f"Property {name} does not accept parameters")
            workspace.logger.info(f"access property name: {name}, value: {obj}")
            return str(obj)

        # 处理函数调用
        is_async = asyncio.iscoroutinefunction(obj)
        if is_async:
            result = await obj() if param is None else await obj(param)
        else:
            result = obj() if param is None else obj(param)
        workspace.logger.info(f"call function name: {name}, param: {param}({type(param)}), result: {result}")
        return str(result)
    except Exception as e:
        raise AgentRunException(f"`{key}.{name}` error: {e}") from None
