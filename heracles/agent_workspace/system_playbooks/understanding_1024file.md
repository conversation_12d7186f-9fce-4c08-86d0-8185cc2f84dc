---
title: Handling .1024 file
description: ''
tags: ['initialization']
---
## Overview
Understanding and correctly using the `.1024` file format in this article, and configure the project environment.

## Important Information
{% if not tools://check_path_exists(".1024") -%}
> The `.1024` file does not exist. You should create it first.
{%- endif %}
{% set run_command = "project_knowledge://run_command" -%}
{% if run_command -%}
- Possible run commands for this project in development environment: `{{ run_command }}`
{% endif %}
{% set install_command = "project_knowledge://dependency_command" -%}
{% if install_command and install_command != 'N/A' -%}
- Possible install command for this project to install/update dependencies: `{{ install_command }}`
{% endif %}
- Be aware of which folder the project needs to be run in, and add `cd` command to the run command if needed.

## Specification
- This file is a yaml format file.
- The file should include possible parameters: `run_command`, `compile_command`, `dependency_command`, `linter_config`.
- These parameter names are case-sensitive.
- If no value is provided, this line should be entirely omitted, rather than leaving value empty.
- Add a line of comment for each parameter to explain the purpose.

`run_command` is mandatory and must always be included in the file, it indicates what command to run when "RUN" button clicked.
- It must be precisely written as `run_command`: with no other variations like `RUN_CMD` accepted.
- The value of `run_command` is string if there is only one command, otherwise it should be list of string.
- If project identified as pure HTML/CSS/JS projects without any framework used, add command like this:
  `cd {app_dir} && browser-sync start --server --no-ui --no-notify --no-open --files '**/*.css, **/*.html, **/*.js'`
  - 'browser-sync' is already installed in environment, thus do not perform any package manager commands.

`compile_command` is used only when the project requires compilation before run, such as for Go, C++, or Rust projects.
- If the project does not require compilation to run, DO NOT include this key.
- It is **NOT** for build purpose(independent of development environment running), like npm build, go build, etc.

`dependency_command` is command used to update dependencies for the project every time pull new code, will execute each time a new thread created to ensure dependencies up-to-date.
- If the project does not require dependencies, DO NOT include this key.
- In most cases, there is no need to include command to install package manager, for example, `yarn install` rather than `npm install -g yarn && yarn install`.
- The value of `dependency_command` MUST be string.

`linter_config` specifies the file paths of linter configurations and linter names corresponding to various programming languages in the project.
- Path should be relative path from the project root, for example, .eslintrc.js instead of /home/<USER>/project/.eslintrc.js.
- If the configuration file is not found, do not include that language.

## Forbidden
- DO NOT miss key name
- Do not use any other variations like `RUN_CMD`
- DO NOT use multiple lines for a single configuration
- DO NOT modify `.1024nix` file or mention it (modify `.1024` for command configuration).
- DO NOT remove already existing comments in the file, for user to better understand the purpose of `.1024` file.

## Example
A valid `.1024` file a **yaml** format config file, which might look like this:

```correct
# Compile command before running the project
compile_command: go build -o myapp
# Command to run when "Run" button clicked
run_command: './myapp'
```

a python project that doesn't require compilation:

```correct
# Command to run when "Run" button clicked
run_command: 'python main.py'
linter_config:
  - config_path: 'pyproject.toml'
    type: 'ruff'
    language: 'python'
```

a web service project that use `pnpm` and doesn't require compilation:

```correct
# Command to run when "Run" button clicked
run_command: 'pnpm run dev'
# Command to install or update dependencies, will execute each time a new thread created to ensure dependencies up-to-date
dependency_command: pnpm install
linter_config:
  - config_path: '.eslintrc.js'
    type: 'eslint'
    language: 'javascript'
```

different languages in one repo:

```correct
# Commands to run when "Run" button clicked
run_command:
  - 'cd ./frontend && pnpm run dev'
  - 'uvicorn app.main:app --reload'
dependency_command: '(cd ./frontend && pnpm install); poetry install'
linter_config:
  - config_path: 'pyproject.toml'
    type: 'ruff'
    language: 'python'
  - config_path: 'frontend/tsconfig.json'
    type: 'eslint'
    language: 'javascript'
```
