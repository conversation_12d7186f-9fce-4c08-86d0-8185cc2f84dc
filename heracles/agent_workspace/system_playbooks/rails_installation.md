---
title: Ruby on Rails Installation Guide
description: 'Comprehensive guide for installingRuby on Rails in Clacky environment'
tags: ['rails']
---

## Overview

This document provides detailed steps for installing Ruby on Rails in the Clacky environment. We strongly recommend using Rails 7.x as it provides the latest features and security updates.

## Installation Steps

### 1. Rails Installation

In the Clacky environment, we only support Rails 7.x versions. Other versions (including Rails 6.x or Rails 8.x) may not work properly.

⚠️ Installation Notes:
- You MUST install Rails with a specific version
- This step CANNOT be skipped or combined with other package installations

```bash
# ✅ Correct Installation - Specify Version
gem install rails -v '~> 7.2.0'

# ❌ Incorrect Installation - Do Not Use
gem install rails                    # No version specified
gem install rails -v '~> 6.0.0'      # Unsupported 6.x version
gem install rails -v '~> 8.0.0'      # Unsupported 8.x version
```
