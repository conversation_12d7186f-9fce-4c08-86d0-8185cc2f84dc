---
title: Handling errors in Clacky
description: 'the common errors in <PERSON><PERSON><PERSON> and their solutions'
tags: ['handling_errors']
---
## Console Errors

### `configuration error`

**Reason:**
- Configuration format of `.1024` file is incorrect

**Solution:**
- Check `.1024` file format and fix by following specifications:
    - This file is key-value format, every configuration MUST be on a single line.
    - The file should include two possible parameters: `compile_command` and `run_command`.
    - These parameter names are case-sensitive.
    - If no value is provided, this line should be entirely omitted, rather than leaving value empty.

## Terminal Errors

### `Failed to check cmd completion within the specified time`

**Reason:**
- <PERSON><PERSON><PERSON> has a default timeout for command execution of 10 seconds

**Solution:**
- Retry the command or ask user to run by himself

### `not a git repository`

**Reason:**
- The current directory is not a git repository, mainly because user changed the directory to other place.

**Solution:**
- cd back to `$HOME/app` directory and try again
