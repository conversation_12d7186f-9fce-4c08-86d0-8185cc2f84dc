---
title: Environment Initialization Task Instruction
description: ''
tags: ['initialization']
---
## Overview

Initialize the project to successfully run development environment in Clacky.

## Important information

{% if tools://check_path_exists("README.md") -%}
- Read `README.md` file and understand how to run the project in development environment
  - Carefully integrate necessary steps and actions mentioned in `README.md` into procedure plan in proper order.
  - If mentioned steps in `README.md` conflict with following instructions, follow steps in `README.md`.
{% endif %}
- `.1024` file is a special file used in Clacky to configure the project commands, such as `run_command`, `compile_command`, etc.
- Check for dependency files (e.g. package.json, requirements.txt, go.mod) exist, if so, install required dependencies using appropriate command.
- Project directory structure has been created already; and all commands would be executed in the project root directory.

{% set run_command = "project_knowledge://run_command" -%}
{% if run_command -%}
- Possible run command for this project in development environment: `{{ run_command }}`
{% endif %}
- Project language: `project_knowledge://language`, framework: `project_knowledge://framework`
- Required Runtime: `project_knowledge://runtime` `project_knowledge://runtime_version` is already installed
{% set framework = "project_knowledge://framework" -%}
{% if framework == 'html/css/js' -%}
- for `html/css/js` project, `browser-sync` is already installed, thus do not perform any package manager commands.
{% endif %}
{% set install_command = "project_knowledge://dependency_command" -%}
{% if install_command and install_command != 'N/A' -%}
- Possible install command for this project to install/update dependencies: `{{ install_command }}`
{% endif %}

{% set linter_config = "project_knowledge://linter_config" -%}
{% if linter_config -%}
- Possible linter configuration for this project: `{{ linter_config }}`
- Write linter configuration into `.1024` file as `linter_config` in yaml format.
- Supported languages are python, javascript, typescript, and go, if other languages are in linter_config, ignore them.
- If no linter configuration found, do not include `linter_config` key.
{% endif %}

## Procedure

Plan should be generated in ONE SHOT with following concerns.
1. Install required package manager and dependencies using appropriate command if needed, otherwise skip.
  - Install dependencies only when the project uses dependencies.
  - If package manager not installed, install it first via default package manager provided by runtime(e.g. npm, pip, etc.).
2. Modify `.1024` file to configure how to run in development environment
  - `.1024` support these case-sensitive keys: `compile_command` and `run_command`, `dependency_command`, `linter_config`.
  - If the project require compilation before running, `compile_command` should be provided as well.
3. Add `.gitignore` file if not exists and configure it
  - Add `.1024*`, `!.1024` and `.breakpoints` to `.gitignore`
  - Carefully analyze files and directories that do not need to be tracked by git based on the project's language and framework
{% if tools://check_path_exists(".env.example") -%}
4. {% if not tools://check_path_exists(".env") -%}
  Run `cp .env.example .env` to copy `.env.example` to `.env`
  {%- endif %}
5. Modify `.env` file to write necessary environment variables for used middlewares.
{% endif %}
{% if tools://check_path_exists("*.example") -%}
- Analyze if need to copy existing `.example` files to new configuration files(without `.example` suffix).
  - If middleware is used in these files, configure them with provided values and configured keys.
{% endif %}
{% if framework == 'rails' -%}
- Add `config.hosts << /.*/` to `config/environments/development.rb` file to allow Clacky domains to access the project
{% endif %}

## Forbidden Actions

- The project codebase has been cloned already; Do not perform `git clone` operations.
- DO NOT modify `.1024nix` file or mention it (modify `.1024` for command configuration).
- DO NOT delete existing content in `.gitignore` file.
- DO NOT ignore `.gitignore` file itself.
