---
title: Handling .gitignore File
description: ''
tags: ['initialization']
---
## Overview

Configure `.gitignore` file to add `.1024*` and other unnecessary files

## Important information

- Project language: `project_knowledge://language`, framework: `project_knowledge://framework`
{% set install_command = "project_knowledge://dependency_command" -%}
{% if install_command and install_command != 'N/A' -%}
- Package manager: This project uses `{{ install_command }}` to install packages/dependencies
{% endif %}
{% if 'pnpm' in install_command -%}
- Specifically add `.pnpm-store` directory for `pnpm` package manager
{% endif %}

## Procedure

1. Add `.1024*`, `!.1024` and `.breakpoints` to `.gitignore`
2. Add other entries need to be ignored by git based on the project's language and framework.
    - Carefully analyze files and directories that do not need to be tracked
    - Organize generated entries into sections, with comment-title for:
        - Clacky-related configuration
        - Language-specific files (e.g. compiled binaries, cache)
        - Framework-related files (e.g. build output, temp files)
        - Package manager directories (e.g. node_modules, vendor)
    - Generate necessary entries to avoid unnecessary files being tracked

## Forbidden Actions

- DO NOT delete existing content in `.gitignore` file.
- DO NOT ignore `.gitignore` file itself.
