---
title: Instructions for databases and other middlewares usage
description: ''
tags: ['initialization']
applicable_rules: ['project_knowledge://middlewares_not_empty()']
---
## Overview

If middleware is used, edit configuration files with provided values, be sure to use the correct keys

## Important Information

- Clacky comes pre-configured with various databases, Redis, and other middlewares, automatically providing environment variables with connection details
{%- if tools://middlewares_not_empty() -%}
- Here are the list used in this project:
   ```json
   tools://list_bound_middlewares_in_environment()
   ```
- [important] Use values in `connect_info` to complete project environment configuration, use values rather than keys
- Always specify the host parameter explicitly (e.g. `-h localhost` or `-h 127.0.0.1`) instead of relying on default socket
{%- endif %}

## Procedure

{%- if tools://check_path_exists(".env.example") %}
- Copy example environment configuration files
{%- if not tools://check_path_exists(".env") %}
- run_cmd `cp .env.example .env` to copy `.env.example` to `.env`
  - DO NOT modify `.env.example`
{% endif %}
- modify file `.env` to include necessary environment variables for used middlewares.
  - Reference file `.env.example` to copy the preset keys
  - DO NOT use `echo` command to modify
{%- endif %}

### Middleware Usage

Specify the host parameter explicitly (e.g. `-h 127.0.0.1`) when interacting with the database server
- Use provided connection parameters for the database server
- Never try to install or restart the database server
- Before starting the project setup, ensure the target database exists
- Use `run_cmd` tool to create the database if it doesn't exist
- Database schema and table creation should be handled separately using migrations or scripts
   Examples:
   ```bash
   # PostgreSQL
   PGPASSWORD=your_password createdb -h your_db_host -U your_db_username your_db_name

   # MySQL
   MYSQL_PWD=your_password mysql -h your_db_host -u your_db_username -e "CREATE DATABASE your_db_name"
   ```
