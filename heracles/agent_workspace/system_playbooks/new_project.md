---
title: New Project Instruction
description: 'Everything you need to know for creating and configuring new projects in Clacky environment.'
tags: ['new_project',]
---
## Overview

When creating a new project, you must follow the following principles:

## Important information
- `.1024` and `.gitignore` file need to be created for project
  - refer to 'Handling .1024 file' and 'Handling .gitignore file' playbook for more details.
- Prioritize simplicity and efficiency in your implementation approach.
- Unless explicitly requested by the user, there is no need to add tests and documentation files for the project.
- Never use docker/docker compose to run the project in development environment.
- Use newer versions of dependencies without specifying version
- Clearly indicate files need to be modified in plan to accommodate version (e.g., vue 3, pydantic v2, etc.) to avoid breaking changes.
  - e.g. For pydantic, The `__modify_schema__` method is not supported in Pydantic v2. Use `__get_pydantic_json_schema__` instead.

## Procedure

Plan should be generated in ONE SHOT with following concerns.
1. Init Project Structure in root directory
   - When using project creation tools (like `npx`, `create-*` commands), avoid and NEVER create nested directories
   - Use `.` to create the project in the current directory (project root)
     - For repository cloning, you should also use `.` to create the project in the current directory
   - This ensures a clean and consistent project structure

   Examples:
   ```bash
   npx create-next-app/create-react-app . # ✅ Correct - Create project in current directory
   npx create vite . # ✅ Correct - Create project in current directory
   npx create-next-app/create-react-app new-project # ❌ Wrong, Creates nested directory is forbidden
   npx create vite new-project # ❌ Wrong, Creates nested directory is forbidden
   ```
2. Write code based on requirements.
3. Add and install required package manager and dependencies using appropriate command if needed, otherwise skip.
  - Use package manager commands to add/install dependencies instead of manually editing dependency files
  - Use the latest version of dependencies unless specific version requirements are mentioned
  - Do not forget to install dependencies before running the project
  - If package manager not installed, install it first via default package manager provided by runtime(e.g. npm, pip, etc.).
4. Add `.1024` file to configure how to run in development environment
  - `.1024` support these case-sensitive keys: `compile_command` and `run_command`, `dependency_command`, `linter_config`.
  - If the project require compilation before running, `compile_command` should be provided as well.
5. Add `.gitignore` file if not exists and configure it
  - Add `.1024*`, `!.1024` and `.breakpoints` to `.gitignore`
  - Carefully analyze files and directories that do not need to be tracked by git based on the project's language and framework
6. If dotenv is planned to be used, add `.env` file to write necessary environment variables for used middlewares.
  - refer to 'Instructions for databases and other middlewares usage' playbook for more details.

## Additional Tips For Frontend Project

### TypeScript Usage

Unless mentioned otherwise, always prefer TypeScript (`.tsx`) over JavaScript (`.jsx`) for better type safety and developer experience, `jsx` may not work correctly in clacky environment.
- Ensure your configuration file modification plan is accurate. For example, use 'next.config.ts' instead of 'next.config.js' when initializing npx scaffold with TypeScript.

## Core Dependencies

### Shadcn UI
1. The 'shadcn-ui' package is deprecated. Please use the 'shadcn' package instead:
   ```bash
   npx shadcn@latest init --yes --defaults
   ```
