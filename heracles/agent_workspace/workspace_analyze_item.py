from typing import Optional
from enum import StrEnum
from datetime import datetime

from heracles.core.exceptions import AgentRunException


class WorkspaceStatus(StrEnum):
    INIT = 'init'
    RUNNING = 'running'
    DONE = 'done'
    FAILED = 'failed'

class WorkspaceAnalyzeItem():
    name: str
    description: str
    status: WorkspaceStatus = WorkspaceStatus.INIT
    version: int = 0
    last_updated_at: Optional[str] = None
    started_at: Optional[str] = None
    estimated_seconds: int
    section_header: str
    instruction_prompt: Optional[str] = None

    PRESETS = [
        {
            'name': 'project_basic_info',
            'description': 'Basic information like language and framework',
            'estimated_seconds': 10,
            'section_header': 'Project Basic Info'
        },
        {
            'name': 'project_structure',
            'description': 'File and directory structure of the project',
            'estimated_seconds': 30,
            'section_header': 'Project Structure',
            'instruction_prompt': 'main file structure of the project, should be detailed, describe this project and its purpose/scenarios',
        },
        {
            'name': 'project_dependencies',
            'description': 'Project notable dependencies',
            'estimated_seconds': 30,
            'section_header': 'Project Dependencies',
            'instruction_prompt': 'dependencies and their purpose/scenarios',
        },
        {
            'name': 'project_components',
            'description': 'Analysis of reusable components',
            'estimated_seconds': 80,
            'section_header': 'Project Components',
            'instruction_prompt': 'reusable components and their purpose/scenarios',
        }
    ]

    def __init__(self, workspace, name, description, estimated_seconds, section_header, instruction_prompt=None):
        self.workspace = workspace
        self.name = name
        self.description = description
        self.estimated_seconds = estimated_seconds
        self.instruction_prompt = instruction_prompt
        self.section_header = section_header

    async def _update_status(self, status):
        self.status = status
        await self.workspace.trigger('workspace_analyze_items_updated', self.workspace.detail_status)

    async def set_running(self):
        self.started_at = datetime.now().isoformat()
        await self._update_status(WorkspaceStatus.RUNNING)

    async def set_done(self):
        self.version += 1
        self.last_updated_at = datetime.now().isoformat()
        await self._update_status(WorkspaceStatus.DONE)

    async def set_failed(self):
        await self._update_status(WorkspaceStatus.FAILED)

    def to_dict(self):
        return {
            'name': self.name,
            'description': self.description,
            'status': self.status,
            'version': self.version,
            'last_updated_at': self.last_updated_at,
            'started_at': self.started_at,
            'estimated_seconds': self.estimated_seconds
        }

    @classmethod
    def find_by_name(cls, workspace, name: str) -> 'WorkspaceAnalyzeItem':
        for analyze_item in workspace.analyze_items:
            if analyze_item.name == name:
                return analyze_item
        raise AgentRunException(f"workspace analyze item: {name} not found")
