from typing import Literal
from heracles.core.schema import FocusComponentType


class AutoFix:
    def __init__(self, workspace):
        self.workspace = workspace
        self.enable = True
        self.current_turn = 0
        self.MAX_FIX_TURN = 2
        self.status: Literal['init', 'start', 'check_errors', 'fix_errors', 'fix_completed', 'fix_stopped'] = 'init'
        self.start_auto_fix_hint_message = 'I will try to run the project to check if any error occurs'  # noqa: E501
        self.checking_error_hint_message = "I'm now running the project to verify the effectiveness of the fixes. "
        self.detect_no_error_hint_message = 'The project appears to be running well. Further testing is recommended to confirm everything works as expected. Let me know if you need any assistance.'  # noqa: E501
        self.fix_done_hint_message = "I've applied the fixes, and the project appears to be running well. Further testing is recommended to confirm everything works as expected. Let me know if you need any assistance."  # noqa: E501

    async def update_status_and_event(
        self, status: Literal['init', 'start', 'check_errors', 'fix_errors', 'fix_completed', 'fix_stopped'], data=None
    ):
        if status == self.status:
            return
        self.status = status
        if status == 'init':
            self.current_turn = 0
        elif status == 'start':
            self.current_turn = 0
            await self.trigger_auto_fix_start('')
            await self.workspace.trigger_general_loading(tool_name='AutoFix', status='start')
            await self.workspace.trigger('message', self.start_auto_fix_hint_message)
        elif status == 'check_errors':
            await self.workspace.trigger_general_loading(tool_name='AutoFix', status='Clacky is checking errors')
        elif status == 'fix_errors':
            await self.workspace.playground.ide_server_client.following_focus_component(FocusComponentType.TERMINAL)
            await self.workspace.trigger_general_loading(tool_name='AutoFix', status='Clacky is fixing errors')
        elif status == 'fix_completed':
            await self.trigger_auto_fix_completed(self.detect_no_error_hint_message)
            await self.workspace.trigger_general_loading(tool_name='AutoFix', status='end')
        elif status == 'fix_stopped':
            await self.trigger_auto_fix_stopped(data)
            await self.workspace.trigger_general_loading(tool_name='AutoFix', status='end')

    def completed_message(self):
        return self.detect_no_error_hint_message

    def is_working(self):
        return self.status in ['start', 'check_errors', 'fix_errors']

    async def set_auto_fix_enable(self, enable: bool = False):
        if self.enable == enable:
            return
        self.enable = enable
        if not enable and self.is_working():
            await self.update_status_and_event('fix_stopped')
        if enable:
            await self.workspace.smart_detect.set_status('monitoring_errors')
        else:
            await self.workspace.smart_detect.set_status('stopped')

    async def trigger_auto_fix_start(self, message: str):
        data = {
            'status': 'start',
            'message': message,
        }
        await self.workspace.trigger('auto_fix_status_updated', data)

    async def trigger_auto_fix_stopped(self, message: str):
        data = {
            'status': 'stopped',
            'message': message,
        }
        await self.workspace.trigger('auto_fix_status_updated', data)

    async def trigger_auto_fix_completed(self, message: str):
        data = {
            'status': 'completed',
            'message': message,
        }
        await self.workspace.trigger('auto_fix_status_updated', data)
