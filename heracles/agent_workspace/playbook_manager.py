import traceback
import itertools
from typing import Any

from heracles.agent_workspace.playbook import Playbook
from heracles.agent_workspace.system_playbook_loader import clone_system_playbook
from heracles.core.schema.knowledge import LanguageType, RuntimeType, FrameworkType
from heracles.server.admin.admin import query_playbook, create_playbook, AdminPlaybookModel, AdminPlaybookQuery
from heracles.core.config import get_env_var


class PlaybookManager:
    def __init__(self, workspace):
        self.workspace = workspace
        self.system_playbooks = []
        self.project_playbooks = []

    def get(self, id_or_title: str) -> Playbook | None:
        for playbook in itertools.chain(self.system_playbooks, self.project_playbooks):
            if id_or_title == playbook.id:
                return playbook
            if id_or_title == playbook.title:
                return playbook
        return None

    def search_by_tags(self, tags: list[str]):
        playbooks = []
        for playbook in itertools.chain(self.system_playbooks, self.project_playbooks):
            if all(tag in playbook.tags for tag in tags):
                playbooks.append(playbook)
        return playbooks

    async def load_from_files(self):
        # 复制系统的playbook
        playbooks = clone_system_playbook()
        self.system_playbooks.extend(playbooks)

    def generate_playbook_tags(self):
        tags = []
        for project in self.workspace.project_knowledge.basic_info_list:
            language = project.language
            runtime = project.runtime
            framework = project.framework
            if language != LanguageType.NA:
                tags.append(language)
            if runtime != RuntimeType.NA:
                runtime_version = project.runtime_version
                tags.append(language.lower())
                if runtime_version:
                    tags.append(language.lower() + '~' + runtime_version.lower())
            if framework != FrameworkType.NA:
                framework_version = project.framework_version
                tags.append(framework.lower())
                if framework_version:
                    tags.append(framework.lower() + '~' + framework_version.lower())
        return tags

    async def search_playbook_from_db(self, text: str, tags: list[str] | None = None) -> list[Playbook]:
        if not get_env_var('ADMIN_VECTOR_DB_HOST_OPTIONAL'):
            return []

        tags = self.generate_playbook_tags()
        self.workspace.logger.info(f'PlaybookManager: playbook query text: {text}, query tags: {tags}')
        query_dict: dict[str, Any] = {'text': text}
        if tags:
            query_dict['tags'] = tags
        try:
            res = await query_playbook('playbooks', AdminPlaybookQuery.model_validate(query_dict))
            res = res.model_dump(mode='json')
        except Exception as e:
            self.workspace.logger.warning(f'{e}\nTraceback: {traceback.format_exc()}')
            res = {}
        self.workspace.logger.debug(f'PlaybookManager: searched result: {res}')
        playbooks = []
        if res.get('status') == 'ok' and isinstance(res.get('data'), list):
            for playbook_dict in res.get('data'):
                applicable_rules = playbook_dict.get('applicable_rules', [])
                playbook_dict['applicable_rules'] = applicable_rules
                playbook = Playbook.model_validate(playbook_dict)
                playbooks.append(playbook)
        self.project_playbooks = playbooks
        self.workspace.logger.debug(f'PlaybookManager: searched playbooks: {playbooks}')
        return self.project_playbooks

    async def upload(self, playbook: Playbook):
        if not get_env_var('ADMIN_VECTOR_DB_HOST_OPTIONAL'):
            return
        self.workspace.logger.info(f'PlaybookManager: playbook upload: {playbook}')
        try:
            await create_playbook('playbooks', AdminPlaybookModel.model_validate(playbook.model_dump(mode='json')))
        except Exception as e:
            self.workspace.logger.warning(f'{e}\nTraceback: {traceback.format_exc()}')
