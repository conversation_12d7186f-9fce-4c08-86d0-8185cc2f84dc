from heracles.agent_controller.llm import LLM
from heracles.core.schema import LangfuseTraceOption


class ToolBase:
    """所有 workspace 旗下工具类的 Base 基类, 帮助初始化好 logger, llm 等关键数据"""

    def __init__(self, workspace):
        self.workspace = workspace
        self.playground = self.workspace.playground
        self.logger = self.workspace.logger
        self.llm = LLM()
        self._set_llm_metadata()

    def _set_llm_metadata(self):
        langfuse_option = LangfuseTraceOption(
                trace_id=self.workspace.playground.playground_id,
                generation_name=self.__class__.__name__
        )
        self.llm.set_llm_metadata(langfuse_option)
