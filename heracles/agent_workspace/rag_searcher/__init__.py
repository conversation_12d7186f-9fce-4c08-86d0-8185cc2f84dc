import re
import traceback
from typing import Iterable

from heracles.core.schema import FileSnippet
from heracles.core.exceptions import IDEServerFunCallException
from heracles.agent_roles.utils import llm_ability
from heracles.core.schema import LLMAbilityType
from heracles.agent_roles.code_role.utils import add_line_numbers

from ..tool_base import ToolBase
from .prompts import EXPAND_QUERY_SYSTEM_PROMPT

class RagSearcher(ToolBase):

    async def search(self, query: str, need_expand=True) -> list[FileSnippet]:
        """ Fetch relevant snippets from the LLM and Go Agent

        :param query: string, query content
        :param need_expand: boolean, expand more questions from query by llm, default is True
        """
        try:
            # 1. Expand the search query
            queries = await self.expand_queries(query) if need_expand else [query]

            # 2. Fetch relevant snippets
            res = await self.playground.func_call('agent_rag_search', queries)

            if isinstance(res, Iterable):
                return [FileSnippet(
                    path=snippet['origin_file'],
                    content=add_line_numbers(snippet['content'], start=snippet['row_start']),
                    row_start=snippet['row_start'],
                    row_end=snippet['row_end']
                ) for snippet in res]
            else:
                return []
        except IDEServerFunCallException:
            self.workspace.logger.warning("RAG search failed\nTraceback: %s", traceback.format_exc())
            return []

    def convert_snippets_to_prompt(self, snippets: list[FileSnippet], span_name='RELATED_FILE') -> str:
        """
        Convert Snippets to a string with wrapper that can be used in the prompt
        """
        relevant_snippet_template = """
<{span_name} index="{i}">\n
<FilePath>\n{file_path}\n</FilePath>\n
<source>\n{content}\n</source>\n
</{span_name}>'
"""

        joined_relevant_snippets = "\n".join(
            relevant_snippet_template.format(
                span_name=span_name,
                i=i,
                file_path=snippet.path,
                content=snippet.content,
            ) for i, snippet in enumerate(snippets)
        )

        return joined_relevant_snippets

    def parse_queries(self, res):
        queries = []
        pattern = re.compile(r"<query>(?P<query>.*?)</query>", re.DOTALL)
        for q in pattern.finditer(res):
            query = q.group("query").strip()
            if query:
                queries.append(query)
        return queries

    @llm_ability(LLMAbilityType.FAST)
    async def expand_queries(self, query: str) -> list[str]:
        res = await self.llm.async_send(user_message=query, system_message=EXPAND_QUERY_SYSTEM_PROMPT)
        queries = self.parse_queries(res)

        queries.insert(0, query)
        return queries
