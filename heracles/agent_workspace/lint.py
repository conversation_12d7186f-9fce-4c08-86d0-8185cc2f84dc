from heracles.core.exceptions import PaasAgentException, LintException
from heracles.core.utils.paas_agent import paas_request
from heracles.core.schema import FocusComponentType
from heracles.server.clacky.ide_server_client_mixins.focus_components import FollowFocusInterface, FocusAreaMixin


class Lint(FocusAreaMixin):
    def __init__(self, url: str, follow_focus: FollowFocusInterface):
        super().__init__()

        self.url = url + '/lint'
        self.follow_focus = follow_focus

    async def following_focus_component(self, area: FocusComponentType):
        await self.follow_focus.following_focus_component(area)

    @FocusAreaMixin.focus(FocusComponentType.EDITOR)
    async def diagnostic(self, paths: list[str]):
        url = self.url + '/diagnostic'

        data = {'paths': paths}

        try:
            response = await paas_request('post', url, data)
            return response

        except PaasAgentException as e:
            raise LintException(e) from e

    @FocusAreaMixin.focus(FocusComponentType.EDITOR)
    async def fix(self):
        url = self.url + '/fix'

        try:
            response = await paas_request('post', url)
            return response

        except PaasAgentException as e:
            raise LintException(e) from e
