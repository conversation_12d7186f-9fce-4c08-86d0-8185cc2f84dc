import copy
import os
import traceback
from typing import List

from heracles.agent_workspace.playbook import Playbook
from heracles.core.logger import get_logger


class SystemPlaybookLoader:
    system_playbook: List[Playbook] = []

    @classmethod
    def load_playbook_files(cls):
        """从 system_playbooks 目录中读取所有 md 文件，解析后存入 playbooks"""
        directory = os.path.join(os.path.dirname(__file__), 'system_playbooks')

        # 收集所有需要解析的文件
        for filename in os.listdir(directory):
            if filename.endswith('.md') and not filename.startswith('_'):
                filepath = os.path.join(directory, filename)
                playbook = cls.parse_playbook_file(filepath)
                if playbook:
                    cls.system_playbook.append(playbook)

    @classmethod
    def parse_playbook_file(cls, filepath: str) -> Playbook | None:
        try:
            with open(filepath, 'r', encoding='utf-8') as file:
                content = file.read()
                parts = content.split('---')
                if len(parts) >= 3:
                    metadata = parts[1].strip()
                    content_body = parts[2].strip()
                    title = cls.extract_field(metadata, 'title')
                    description = cls.extract_field(metadata, 'description')
                    tags = cls.extract_list_field(metadata, 'tags')
                    applicable_rules = cls.extract_list_field(metadata, 'applicable_rules')
                    return Playbook(
                        title=title,
                        description=description,
                        tags=tags,
                        applicable_rules=applicable_rules,
                        original_content=content_body,
                        source='system'
                    )
        except Exception as e:
            get_logger().error(f'Parse system playbook file `{filepath}` error: {e}\nTraceback: {traceback.format_exc()}')
        return None

    @classmethod
    def extract_list_field(cls, metadata: str, field: str) -> list[str]:
        """从元数据中提取列表字段"""
        line = cls.extract_field(metadata, field)
        if not line:
            return []
        return [item.strip() for item in line.strip('[]').split(',')]

    @classmethod
    def extract_field(cls, metadata: str, field: str):
        """从元数据中提取特定字段"""
        for line in metadata.split('\n'):
            if line.startswith(field + ':'):
                return line.split(':', 1)[1].strip().replace("'", '')
        return None


SystemPlaybookLoader.load_playbook_files()

def clone_system_playbook():
    return copy.deepcopy(SystemPlaybookLoader.system_playbook)
