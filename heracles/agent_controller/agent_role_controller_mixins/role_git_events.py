# mypy: ignore-errors


class RoleGitEventsMixin:  # type: ignore

    async def on_u_make_commit_file_list(self):
        """ event: 获取 commit file list

        see: "docs/pr-design.md"
        """
        return await self.git_role.make_commit_file_list()

    async def on_u_make_commit_message(self):
        """ event: 获取 commit message

        see: "docs/pr-design.md"
        """
        return await self.git_role.make_commit_message()

    async def on_u_make_pr_file_list(self, git_upstream_branch: str):
        """ event: 获取 pr file list

        see: "docs/pr-design.md"
        """
        return await self.git_role.make_pr_file_list(git_upstream_branch)

    async def on_u_make_pr_message(self, git_upstream_branch: str):
        """ event: 获取 pr message

        see: "docs/pr-design.md"
        """
        return await self.git_role.make_pr_message(git_upstream_branch)
