import os

from dotenv import load_dotenv


def load_example_env_vars(example_file):
    """加载 .env.example 文件中的环境变量名字"""
    with open(example_file, 'r') as file:
        lines = file.readlines()
    env_var_names = [
        line.split('=')[0].strip()
        for line in lines
        if line.strip() and not line.startswith('#')
    ]
    return env_var_names


def check_required_env_vars(example_env_vars):
    """检查所有非 _OPTIONAL 后缀的环境变量是否存在且有值"""
    missing_vars = [
        key
        for key in example_env_vars
        if not key.endswith('_OPTIONAL')
        and (get_env_var(key) is None or get_env_var(key) == '')
    ]
    if missing_vars:  # pragma: no cover
        raise EnvironmentError(
            f"Config error, missing these env keys: {', '.join(missing_vars)}"
        )


def get_env_var(var_name, default=None, must=False):
    """获取环境变量的值，如果不存在则返回默认值"""
    env_var = os.getenv(var_name, default)
    if env_var == '':
        env_var = default
    if must and env_var is None:
        raise EnvironmentError(
            f"get_env_var error, missing key: {var_name}"
        )
    return env_var


# 加载 .env 文件中的环境变量
load_dotenv()

# 加载 .env.example 文件中的环境变量名字
example_env_vars = load_example_env_vars('.env.example')

# 检查必填环境变量
check_required_env_vars(example_env_vars)

# export get_env_var 给团队使用
__all__ = ['get_env_var']
