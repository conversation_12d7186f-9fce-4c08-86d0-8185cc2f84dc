from enum import StrEnum
from typing import Dict, List, Optional, Union
from pydantic import BaseModel, Field
from heracles.core.schema import FileSnippet
from heracles.core.schema.task import ActionStatus

class PlanStepType(StrEnum):
    THINK = 'think'
    RETRIEVE = 'retrieve'
    GENERATE = 'generate'

class PlanStep(BaseModel):
    status: ActionStatus = ActionStatus.INIT_STATUS
    result: Optional[Union[str, list, FileSnippet]] = Field(default='', description='可能是字符串，或数组，或 FileSnippet')

    def set_status_doing(self):
        self.status = ActionStatus.IN_PROGRESS_STATUS

    def set_status_abandoned(self):
        self.status = ActionStatus.ABANDONED_STATUS

    def set_status_completed(self):
        self.status = ActionStatus.COMPLETED_STATUS

    def set_result(self, res: Union[str, list, FileSnippet]):
        self.result = res

class Plan(BaseModel):
    goal: str = ''
    goal_detail: str = ''
    proposed_list: List[str] = []
    steps: Dict[PlanStepType, PlanStep] = {
        PlanStepType.THINK: PlanStep(),
        PlanStepType.RETRIEVE: PlanStep(),
        PlanStepType.GENERATE: PlanStep(),
    }

    def update_plan_step(self, plan_step_type: PlanStepType, plan_step_dict):
        plan_step = self.steps[plan_step_type]
        for key, value in plan_step_dict.items():
            if not hasattr(plan_step, key):
                raise Exception(f'update_plan_step error: no such key `{key}`')
            setattr(plan_step, key, value)

    def get_plan_step_dict(self, plan_step_type: PlanStepType):
        plan_step = self.steps[plan_step_type]
        res = {}
        res[plan_step_type] = plan_step.dict()
        return {'steps': res}


class ThinkResult(BaseModel):
    items: list[str] = []

    def to_str(self):
        res = ''
        for index, item in enumerate(self.items):
            res += f'\n{index + 1}. {item}'
        res = res.strip('\n')
        return res
