from enum import StrEnum
from pydantic import BaseModel, Field
from typing import Any


class LanguageType(StrEnum):
    """ 编程语言类型
    定义时字符串不能含有“.”和空格
    """
    NA = 'N/A'
    PYTHON = 'python'
    JAVASCRIPT = 'javascript'
    JAVA = 'java'
    CPP = 'cpp'
    CSHARP = 'c#'
    PHP = 'php'
    SWIFT = 'swift'
    KOTLIN = 'kotlin'
    RUBY = 'ruby'
    GO = 'go'
    RUST = 'rust'
    TYPESCRIPT = 'typescript'
    SCALA = 'scala'
    PERL = 'perl'
    R = 'r'
    OBJECTIVEC = 'objectivec'
    DART = 'dart'
    HASKELL = 'haskell'
    LUA = 'lua'
    SQL = 'sql'


class RuntimeType(StrEnum):
    """ 编程运行时类型
    定义时字符串不能含有"."和空格

    注意:
    1. 运行时是程序代码实际执行的环境
    2. 有些语言可能有多个运行时实现
    3. 有些运行时可以执行多种语言
    """
    NA = 'N/A'
    # JavaScript 运行时
    NODEJS = 'nodejs'  # Node.js - 服务端 JavaScript 运行时
    DENO = 'deno'      # Deno - 安全的 JavaScript/TypeScript 运行时
    BUN = 'bun'        # Bun - 高性能 JavaScript 运行时

    # Python 运行时
    CPYTHON = 'cpython'   # CPython - Python 的参考实现
    PYPY = 'pypy'         # PyPy - JIT 编译的 Python 实现

    # Java 平台运行时
    JVM = 'jvm'          # Java 虚拟机,可运行 Java/Kotlin/Scala 等
    GRAALVM = 'graalvm'  # 通用虚拟机,支持多语言

    # .NET 平台运行时
    DOTNET = 'dotnet'    # .NET 运行时,支持 C#/F#/VB.NET

    # 其他语言运行时
    GO = 'go'            # Go 运行时
    RUST = 'rust'        # Rust 运行时
    PHP = 'php'          # PHP 运行时
    RUBY = 'ruby'        # Ruby 运行时
    SWIFT = 'swift'      # Swift 运行时

class FrameworkType(StrEnum):
    """ 编程框架类型
    定义时字符串不能含有“.”和空格
    """
    NA = 'N/A'
    ANGULAR = 'angular'
    ANGULARDART = 'angulardart'
    ASPNET = 'aspnet'
    ASPNET_CORE = 'aspnet_core'  # Added ASP.NET Core framework
    BLAZOR = 'blazor'              # Added Blazor framework
    BOOST = 'boost'
    CATALYST = 'catalyst'
    DJANGO = 'django'
    DOTNET = 'dotnet'
    ECHO = 'echo'
    EXPRESS = 'express'
    FLASK = 'flask'
    FASTAPI = 'fastapi'
    FLUTTER = 'flutter'
    GIN = 'gin'
    HTML_CSS_JS = 'html/css/js'
    HIBERNATE = 'hibernate'
    HIBERNATE_ORM = 'hibernate'
    LARAVEL = 'laravel'
    NESTJS = 'nestjs'
    NEXTJS = 'nextjs'
    PLAY = 'play'
    QT = 'qt'
    RAILS = 'rails'
    REACT = 'react'
    SINATRA = 'sinatra'
    SPRING = 'spring'
    TENSORFLOW = 'tensorflow'      # Added TensorFlow framework
    VUEJS = 'vuejs'


class LinterType(StrEnum):
    """Enumeration of supported code linting tools"""

    RUFF = 'ruff'
    MYPY = 'mypy'
    ESLINT = 'eslint'
    GOLANGCI_LINT = 'golangci-lint'


class LinterConfig(BaseModel):
    """Configuration information for code linting tools"""

    type: LinterType = Field(description='Type of the code linting tool')
    config_path: str = Field(description='Relative path to the project root path of linter configuration file')


class BasicInfoModel(BaseModel):
    """Project basic information storing AI generated content"""

    app_dir: str = Field(
        default='.',
        description="""relative path to the project root directory, for example
`.` means it's the root directory,
`./web` means it's the web folder under the root directory
`./src/app` means it's the src/app folder under the root directory.""",
    )
    language: LanguageType = LanguageType.NA
    runtime: RuntimeType = RuntimeType.NA
    runtime_version: str = ''
    framework: FrameworkType = FrameworkType.NA
    framework_version: str = ''
    dependency_command: str = Field(
        default='',
        description='command to install packages/dependencies for the project'
    )
    run_command: str = Field(
        default='',
        description='command to run the project in development environment'
    )
    middlewares: list[str] = Field(
        default_factory=list,
        description='middlewares used in the project, included only when evidence is found. leave empty if no exact match found'
    )
    linter_config: list[LinterConfig] = Field(
        default_factory=list,
        description="linter type and configuration path of used language",  # noqa: E501
    )

    def language_equals(self, language: str) -> bool:
        return any(lang.strip() == self.language.value for lang in language.split('|'))

    def framework_equals(self, framework: str) -> bool:
        return any(fw.strip() == self.framework.value for fw in framework.split('|'))

    def framework_not_equals(self, framework: str) -> bool:
        return not self.framework_equals(framework)

class RepoBasicInfo(BaseModel):
    # this class is only for extracting basic info for each project without dependencies and components
    basic_info_list: list[BasicInfoModel] = Field(
        default_factory=list,
        description='list of basic info of different projects'
    )

class DetailedInfo(BasicInfoModel):
    project_dependencies: str = Field(
        default='',
        description='Project notable dependencies'
    )
    project_components: str = Field(
        default='',
        description='Project notable components'
    )

class RepoInfo(BaseModel):
    basic_info_list: list[DetailedInfo] = Field(
        default_factory=list,
        description='list of basic info of different projects'
    )
    project_structure: str = Field(
        default='',
        description='description of file and directory structure of the project'
    )

class ProjectKnowledge(RepoInfo):
    """Project basic information storing all project knowledge content"""

    runtime_environment: dict[str, Any] = Field(
        default_factory=dict,
        description='runtime environment infomation of the project'
    )

    def language(self) -> str:
        """all languages used by the project, split by `|`"""
        all_language = []
        for basic_info in self.basic_info_list:
            all_language.append(str(basic_info.language))
        return '|'.join(all_language)

    def runtime(self) -> str:
        """all runtimes used by the project, split by `|`"""
        all_runtime = []
        for basic_info in self.basic_info_list:
            all_runtime.append(str(basic_info.runtime))
        return '|'.join(all_runtime)

    def runtime_version(self) -> str:
        """all runtime versions used by the project, split by `|`"""
        all_runtime_version = []
        for basic_info in self.basic_info_list:
            all_runtime_version.append(basic_info.runtime_version)
        return '|'.join(all_runtime_version)

    def framework(self) -> str:
        """all frameworks used by the project, split by `|`"""
        all_framework = []
        for basic_info in self.basic_info_list:
            all_framework.append(str(basic_info.framework))
        return '|'.join(all_framework)

    def framework_version(self) -> str:
        """all framework versions used by the project, split by `|`"""
        all_framework_version = []
        for basic_info in self.basic_info_list:
            all_framework_version.append(basic_info.framework_version)
        return '|'.join(all_framework_version)

    def run_command(self) -> list[str]:
        """list of commands to run all projects"""
        all_commands = []
        for basic_info in self.basic_info_list:
            all_commands.append(basic_info.run_command)
        return all_commands

    def dependency_command(self) -> str:
        """command to install dependencies used by the project, split by `;`"""
        all_commands = []
        for basic_info in self.basic_info_list:
            all_commands.append(basic_info.dependency_command)
        return ';'.join(all_commands)

    def linter_config(self) -> list[LinterConfig]:
        """linter config used by the project"""
        all_linter_config = []
        for basic_info in self.basic_info_list:
            all_linter_config.extend(basic_info.linter_config)
        return all_linter_config

    @property
    def middlewares(self) -> list[str]:
        """all middlewares used in the project"""
        all_middlewares = []
        for basic_info in self.basic_info_list:
            all_middlewares.extend(basic_info.middlewares)
        return all_middlewares

    def language_equals(self, language: str) -> bool:
        """ check if language match project knowledge languages
        param: language can be single language or multiple language split by `|` like 'javascript|typescript'
        """
        return any(basic_info.language_equals(language) for basic_info in self.basic_info_list)

    def framework_equals(self, framework: str) -> bool:
        """ check if framework match project knowledge framwork
        param: framework can be single framework or multiple frameworks split by `|` like 'react|nextjs'
        """
        return any(basic_info.framework_equals(framework) for basic_info in self.basic_info_list)

    def framework_not_equals(self, framework: str) -> bool:
        """ check if framework not match project knowledge framwork
        param: framework can be single framework or multiple frameworks split by `|` like 'react|nextjs'
        """
        return not self.framework_equals(framework)

    def middleware_in(self, middleware: str) -> bool:
        """ check if middle is in project middles
        param: middleware is a single middleware
        """
        return middleware in self.middlewares

    def middlewares_not_empty(self) -> bool:
        """ check if project middlewares is not empty """
        return len(self.middlewares) > 0
