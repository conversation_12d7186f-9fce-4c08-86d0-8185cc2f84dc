import random

from typing import List, Any, Optional, TypeVar, Union, Iterable, Literal
from pydantic import BaseModel, Field
from enum import StrEnum
from uuid import uuid4

from heracles.core.config import get_env_var

# llm response_model 的类型
SubBaseModelOrIterable = TypeVar('SubBaseModelOrIterable', bound=Union[BaseModel, Iterable[BaseModel]])

class PlaygroundStatusType(StrEnum):
    """ PlaygroundStatusType """
    DRAFTED = 'drafted'
    CONNECTING = 'connecting'
    OK = 'ok'  # 正常状态
    CONNECT_FAILED = 'connect_failed'  # 连接失败
    CONNECT_TERMINATED = 'connect_terminated'  # 连接中止

class LLMAbilityType(StrEnum):
    """ 大模型能力类型
    """
    NORMAL = 'normal'
    FAST = 'fast'
    STRONG = 'strong'
    REASONING = 'reasoning'

class MemoryMessage(BaseModel):
    """ 记忆消息
    """
    role: str = 'user'
    content: Union[str, List[dict]]

    @classmethod
    def new(cls, message: str, images: Optional[List[str]] = None) -> 'MemoryMessage':
        final_message: Union[str, List[dict]] = message
        if images:
            final_message = [{'type': 'image_url', 'image_url': image} for image in images] + [{'type': 'text', 'text': message}]
        return cls(role='user', content=final_message)

class AssistantMemoryMessage(MemoryMessage):
    role: str = 'assistant'
    tool_calls: Optional[List[Any]] = None

class ToolMemoryMessage(MemoryMessage):
    role: str = 'tool'
    tool_call_id: Optional[str] = None

class ToolCalls(BaseModel):
    """ 大模型 tool calls 容器
    """
    functions: List[Any] = []
    message: Any = ''

class LangfuseTraceOption(BaseModel):
    """ Langfuse 的 Trace 参数
    """
    trace_id: str = f"auto-{uuid4()}"
    trace_name: str = ''
    generation_name: str = 'auto-litellm-generation'
    tags: List[str] = [get_env_var('LANGFUSE_PROJECT_OPTIONAL', 'unset-project')]

class FileChangeLineItem(BaseModel):
    """ Git 某个文件内容的变更信息
    """
    start: int
    end: int

class FileChangeItem(BaseModel):
    """ Git 某个文件路径等变化信息
    """
    path: str
    status: str
    ai: bool = False
    changes: Optional[List[FileChangeLineItem]]

class FileContent(BaseModel):
    """文件内容"""
    content: str

class FileSnippet(FileContent):
    """含有路径的代码片段"""
    path: str = ''
    row_start: int = -1
    row_end: int = -1
    instructions: list[str] = []

class EditedFileSnippet(BaseModel):
    """编辑后的代码片段"""
    original: str
    edited: str
    thought: str = ''

class Webpage(BaseModel):
    """网页"""
    url: str
    content: str

class ProjectErrorMessage(BaseModel):
    """项目错误信息"""
    title: str
    ref_id: str | None = None
    category: Literal['lint', 'browser_console', 'terminal', 'screenshot']
    status: Literal['init', 'working', 'failed'] = 'init'
    content: str

    @classmethod
    def find_by_id(cls, ref_id: str, workspace):
        for error in workspace.smart_detect.errors:
            if error.ref_id == ref_id:
                return error
        return None

    def generate_suggestion(self):
        """Generate suggestion templates using error title"""
        templates = [
            f"Fix this error: {self.title}",
            f"Analyze the cause: {self.title}",
            f"{self.title}. Give me some suggestions",
        ]
        return random.choice(templates) + f"@@user_context[error://{self.ref_id}]@@"

class FocusComponentType(StrEnum):
    """ 聚焦组件类型, ideserver 使用 """
    TREE = 'Tree'
    EDITOR = 'Editor'
    TERMINAL = 'Terminal'
    BROWSER = 'Browser'
    REMOTE_ACCESS = 'RemoteAccess'


class LimitedStack:
    """ 限制最多 n 条内容的堆栈 """
    def __init__(self, max_size):
        self.max_size = max_size
        self.items = []
        self.truncated = False  # 标记是否有被截断的元素

    def clear(self):
        self.items.clear()
        self.truncated = False

    def push(self, item):
        if len(self.items) == self.max_size:
            self.truncated = True
        self.items.append(item)
        if len(self.items) > self.max_size:
            self.items = self.items[-self.max_size:]

    def append_to_last_one(self, item):
        if len(self.items) == 0:
            self.push(item)
        else:
            self.items[-1] += item

    def get_items(self):
        return self.items

    def to_string(self):
        if not self.items:
            return ""
        result = "\n".join(self.items)
        if self.truncated:
            return "..." + result
        return result

class ErrorHandlerMessageType(StrEnum):
    """ 大模型能力类型
    retry: 前端重试
    warn: 前端 toast
    info: 前端 console.log
    """
    DEFAULT = 'warn:default'
    SPEC = 'retry:spec'
    PLAN = 'retry:plan'
    TASK = 'retry:task'
    ANALYZE = 'warn:analyze'
    CHECK = 'warn:check'
    CLEANUP = 'warn:cleanup'
    CMDK = 'retry:cmdk'
    RERUN = 'retry:rerun'
    MESSAGE = 'retry:message'

class ErrorHandlerMessage(BaseModel):
    """ 错误消息
    """
    error_type: ErrorHandlerMessageType = ErrorHandlerMessageType.DEFAULT
    content: str

class AdminPlaybookStatus(StrEnum):
    ready = 'ready'
    pending = 'pending'


class AdminPlaybookModel(BaseModel):
    id: str = Field(default_factory=lambda: f'{uuid4()}')
    title: str
    description: str = ''
    tags: list[str]
    original_content: str
    source: Literal['system', 'user', 'ai'] = 'ai'
    status: AdminPlaybookStatus = AdminPlaybookStatus.pending


class AdminPlaybookQuery(BaseModel):
    text: str
    tags: Optional[list[str]] = None
    status: AdminPlaybookStatus = AdminPlaybookStatus.ready


class AdminDataResponse(BaseModel):
    status: str
    statusCode: int
    error_msg: str
    data: Optional[Any] = None
