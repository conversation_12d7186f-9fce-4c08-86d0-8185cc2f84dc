from typing import List, Literal

from pydantic import BaseModel, Field, field_validator


class Spec(BaseModel):
    """Specification for user requirements"""

    suggested_name: str = Field(
        description="Suggested name of the thread (dealing with this user's work goal), no longer than 40 characters"
    )
    suggested_branch: str = Field(
        description="Suggested local branch name for handling the current work"
    )
    goal: str = Field(description="Concise goal of the task")
    goal_detail: str = Field(
        description=(
            "Detailed description of the task, incorporate information sent by the user and summarized from the clarification process."
            "Be sure to include necessary references information such as project main pages, dev docs pages and ui design image links."
        )
    )
    current_list: List[str] = Field(
        description="List of project's current status in relation to the goal or requirement, maximum 20 items allowed"
    )
    proposed_list: List[str] = Field(
        description="List of proposed items, maximum 20 items allowed"
    )
    proposed_filechange_list: List[str]
    task_scale: Literal["medium", "small", "micro", "N/A"]

    @field_validator('proposed_list')
    @classmethod
    def validate_proposed_list_length(cls, v):
        if len(v) > 20:
            raise ValueError(f"proposed_list cannot have more than 20 items, got {len(v)} items. Please prioritize and combine related items to stay within the limit while ensuring no critical items are omitted.") # noqa
        return v

    @field_validator('current_list')
    @classmethod
    def validate_current_list_length(cls, v):
        if len(v) > 20:
            raise ValueError(f"current_list cannot have more than 20 items, got {len(v)} items. Please prioritize and combine related items to stay within the limit while ensuring no critical items are omitted.") # noqa
        return v
