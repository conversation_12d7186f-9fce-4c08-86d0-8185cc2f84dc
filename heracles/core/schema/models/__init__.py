from pydantic import BaseModel, Field
from typing import List, Literal

""" 这里定义用于 response_model 的 Model
"""

class ResultMessageModel(BaseModel):
    success: bool = Field(description='True when able to answer, False when fail')
    result: str = Field(description='final answer when success, or reason when fail')

class ErrorFoundModel(BaseModel):
    title: str = Field(description='Title of the error, only when it is an error, 3-5 words', default='')
    is_new_error: bool = Field(description='True when it is a error and different from history errors, False when it is not')

class TaskIntentModel(BaseModel):
    goal: str = Field(description="Concise goal of the task")
    goal_detail: str = Field(
        description=(
            "Detailed description of the task, incorporate information sent by the user and summarized from the clarification process"
        )
    )

class AppendStepIntentModel(BaseModel):
    goal: str = Field(description="summary of the actions to be performed")
    plan_draft: str = Field(description="detailed description of the actions to be performed, in ordered list")
    references: list[str] = Field(
        description="references to useful infomation of 'FileSnippets', 'KeyInformation', 'WebPages', 'Images' and 'Playbooks'"
    )


class EnvironmentMatch(BaseModel):
    id: str
    score: float = Field(description='score from 0 to 1, with step size of 0.1')

class MiddlewareMatch(BaseModel):
    id: str
    score: float = Field(description='score from 0 to 1, with step size of 0.1')
    reason: str

class EnvironmentAnalysisResultModel(BaseModel):
    """Environment Match Analysis Result for user project"""

    message_type: Literal["info", "warn", "error"]
    reason: str
    instructions: List[str]
    environments: List[EnvironmentMatch]
    middlewares: List[MiddlewareMatch]

class PRMessageModel(BaseModel):
    """ PR message
    """
    suggested_title: str = Field(description='Title for Pull Request submission')
    suggested_description: str = Field(description='Description for Pull Request submission')

class TestCaseRuleModel(BaseModel):
    """用于测试 AI 工作结果的生成式测试用例结构"""
    title: str = Field(description='Test case title for better understanding in reports')
    ref_id: str = Field(description='Unique identifier, "TaskAction/1-1" for TaskAction')
    weight: float = Field(description='Weight between 1-5 based on test case importance')
    score: int = Field(description='0 for fail, 1 for pass')

class TestCaseRuleModels(BaseModel):
    """用于测试 AI 工作结果的生成式测试用例结构"""
    rules: List[TestCaseRuleModel]
    suggestions: str = Field(default='', description='Suggestions for failed cases if any')


class PlanDraftResultModel(EnvironmentAnalysisResultModel):
    project_name: str = Field(
        description="github repository name of the project, unique and descriptive, suitable for use as a GitHub repository name, less than 40 characters")  # noqa: E501


class CommandResultModel(BaseModel):
    """命令执行结果模型"""
    reason: str
    success: bool


class TerminalInteractResponseModel(BaseModel):
    """Terminal interaction response model"""
    needs_input: bool
    response: str
