import asyncio
import json

from enum import StrEnum
from typing import List, Union, Optional

from abc import abstractmethod
from pydantic import BaseModel, validator

from heracles.core.exceptions import AgentRunException
from heracles.core.utils.redis_cache import redis_cache
from heracles.core.logger import heracles_logger as logger


class ActionStatus(StrEnum):
    """ TaskAction status
    """
    INIT_STATUS = 'inited'
    COMPLETED_STATUS = 'completed'
    ABANDONED_STATUS = 'abandoned'
    IN_PROGRESS_STATUS = 'in_progress'
    GENERATING = 'generating' # 仅流式输出时使用，开始流式输出前为IN_PROGRESS, 开始后为GENERATING
    CANCELED_STATUS = 'canceled'

class ActionType(StrEnum):
    """ TaskAction action types
    """
    ADD_FILE = 'add_file'
    MODIFY_FILE = 'modify_file'
    MOVE_FILE = 'move_file'
    DELETE_FILE = 'delete_file'
    CREATE_DIRECTORY = 'create_directory'
    MOVE_DIRECTORY = 'move_directory'
    DELETE_DIRECTORY = 'delete_directory'
    RUN_COMMAND = 'run_command'

class CommandLifetimeType(StrEnum):
    """ Command execution duration/time-consuming types
    """
    SHORT = 'short'
    LONG = 'long'
    PERSISTENT = 'persistent'
    NA = 'N/A'

class FileActionObject(BaseModel):
    path: str
    target: str = ''
    detailed_requirement: str = ''
    references: list[str] = []
    snapshot_uuid: Optional[str] = None

    @validator('detailed_requirement')
    def validate_detailed_requirement_length(cls, v):
        if len(v) > 5000:
            raise ValueError("detailed_requirement cannot exceed 5000 characters")
        return v

    @validator('references')
    def validate_references_length(cls, v):
        for ref in v:
            if len(ref) > 1000:
                raise ValueError("each reference item cannot exceed 1000 characters")
        return v

    @validator('path')
    def path_cannot_start_with_slash(cls, v):
        if v.startswith('/'):
            raise ValueError("path cannot start with '/'")
        return v

    def dict(self, *, full=False, **kwargs):
        if full:
            return super().dict()
        else:
            return super().dict(include={'path', 'target', 'detailed_requirement', 'references', 'snapshot_uuid'})

class CommandActionObject(BaseModel):
    command: str
    lifetime: CommandLifetimeType = CommandLifetimeType.NA

    def dict(self, *, full=False, **kwargs):
        if full:
            return super().dict()
        else:
            return super().dict(include={'command', 'lifetime'})

class RedisModel(BaseModel):
    def save_to_redis(self, redis_key: str):
        value = json.dumps(self.dict(full=True, only_self=True))
        logger.debug(f'[cache] save redis: key={redis_key}, value={value}')
        redis_cache.set(redis_key, value)

    def delete_from_redis(self, redis_key: str):
        logger.debug(f'[cache] delete redis: key={redis_key}')
        redis_cache.delete(redis_key)

    @abstractmethod
    def dict(self, *, full: bool = False, only_self: bool = False, **kwargs):
        return super().dict(**kwargs)

class TaskAction(RedisModel):
    id: Optional[str] = None
    task_step_id: Optional[str] = None
    task_id: Optional[str] = None
    action: ActionType
    action_object: Union[FileActionObject, CommandActionObject]
    status: ActionStatus = ActionStatus.INIT_STATUS
    result: Optional[str] = None

    @validator('action_object')
    def action_object_should_be_right_type(cls, v, values):
        if values.get('action') == ActionType.RUN_COMMAND and not isinstance(v, CommandActionObject):
            raise ValueError('action_object should be CommandActionObject when action is RUN_COMMAND')
        if values.get('action') != ActionType.RUN_COMMAND and not isinstance(v, FileActionObject):
            raise ValueError('action_object should be FileActionObject when action is file-related')
        return v

    @validator('action_object')
    def detailed_requirement_must_be_set(cls, v, values):
        if values.get('action') in [ActionType.ADD_FILE, ActionType.MODIFY_FILE] and not v.detailed_requirement:
            raise ValueError('detailed_requirement must be set for file actions')
        return v

    def __init__(self, raw=False, **data):
        """ raw: 是否按原始数据处理，不做任何过滤，仅在数据确认安全的情况下使用
        """
        if raw:
            super().__init__(**data)
        else:
            safe_fields_task = {'action', 'action_object', 'status', 'result'}
            filtered_data = {k: v for k, v in data.items() if k in safe_fields_task}
            super().__init__(**filtered_data)

    def can_revert(self):
        return self.status == ActionStatus.COMPLETED_STATUS

    def can_restore(self):
        return self.status == ActionStatus.CANCELED_STATUS

    def set_status_doing(self):
        """ 自动保存redis
        """
        self._set_status_to(ActionStatus.IN_PROGRESS_STATUS, None)

    def set_status_abandoned(self, result=None):
        """ 自动保存redis
        """
        self._set_status_to(ActionStatus.ABANDONED_STATUS, result)

    def set_status_completed(self, result=None):
        """ 自动保存redis
        """
        self._set_status_to(ActionStatus.COMPLETED_STATUS, result)

    def set_status_canceled(self, result=None):
        """ 自动保存redis
        """
        self._set_status_to(ActionStatus.CANCELED_STATUS, result)

    def _set_status_to(self, status, result: Optional[str] = None):
        self.status = status
        if result:
            self.result = result

        if self.is_enable_save():
            self.save()

    def set_id(self, id: str):
        self.id = id

    def save(self):
        if not self._is_key_ready():  # pragma: no cover
            raise AgentRunException('Cannot save task_action without id or task_step_id or task_id')
        self.save_to_redis(self._generate_redis_key())

    def delete(self):
        if not self._is_key_ready():  # pragma: no cover
            raise AgentRunException('Cannot remove task_action without id or task_step_id or task_id')
        self.delete_from_redis(self._generate_redis_key())

    def is_enable_save(self) -> bool:
        return self.task_id is not None

    def _is_key_ready(self):
        return self.id and self.task_step_id and self.task_id

    def _generate_redis_key(self):
        return f'task:{self.task_id}:task_step_by:{self.task_step_id}:task_action:{self.id}'

    def dict(self, *, full=False, only_self=False, **kwargs):
        if full:
            base_dict = super().dict()
        else:
            base_dict = super().dict(include={'id', 'action', 'status', 'result'})
        base_dict['action_object'] = self.action_object.dict(full=full)
        return base_dict

class TaskStep(RedisModel):
    id: Optional[str] = None
    task_id: Optional[str] = None
    title: str
    task_actions: List[TaskAction] = []
    turn: int = 1

    def __init__(self, raw=False, **data):
        if raw:
            super().__init__(**data)
        else:
            safe_fields_task = {'title', 'turn'}
            filtered_data = {k: v for k, v in data.items() if k in safe_fields_task}
            super().__init__(**filtered_data)
            self.task_actions = []
            for action_data in data.get('task_actions', []):
                if isinstance(action_data, dict):
                    task_action = TaskAction(**action_data)
                else:
                    task_action = action_data
                self.add_task_action_without_id(task_action)

    def validate_task_actions_duplicate_path(self, values):
        """ 检查 task_action 是否有重复的 path
        """
        file_path_modify_count_map: dict = {}

        for action in values:
            if action.action in [ActionType.ADD_FILE, ActionType.MODIFY_FILE, ActionType.DELETE_FILE]:
                if action.action_object.path not in file_path_modify_count_map:
                    file_path_modify_count_map[action.action_object.path] = []
                file_path_modify_count_map[action.action_object.path].append(action)

        for path, actions in file_path_modify_count_map.items():
            if len(actions) > 1:
                raise ValueError(f"Multiple actions are modifying the same path: {path}")

        return values

    def add_task_action(self, task_action: TaskAction) -> str:
        if not self.id:  # pragma: no cover
            raise AgentRunException('Add task action failed without task step id')

        task_action.set_id(self._generate_new_task_action_id())
        task_action.task_step_id = self.id
        task_action.task_id = self.task_id
        self.validate_task_actions_duplicate_path(self.task_actions + [task_action])
        self.task_actions.append(task_action)

        if self.is_enable_save():
            task_action.save()

        assert task_action.id, 'add_task_action id cannot be null'
        return task_action.id

    def add_task_action_without_id(self, task_action: TaskAction) -> None:
        if self.id:  # pragma: no cover
            raise AgentRunException('Add task action without id failed')
        self.validate_task_actions_duplicate_path(self.task_actions + [task_action])
        self.task_actions.append(task_action)

    def get_next_runnable_action(self):
        return next(
            (ta for ta in self.task_actions if ta.status == ActionStatus.IN_PROGRESS_STATUS or ta.status == ActionStatus.INIT_STATUS),
            None,
        )

    def find_action_by_id(self, action_id):
        task_action = next((x for x in self.task_actions if x.id == action_id), None)
        if not task_action:
            raise AgentRunException(f'Unable to find the task action corresponding to {action_id}')
        return task_action

    def set_id(self, id: str):
        if self.id:
            raise AgentRunException('Id cannot be set repeatedly')
        self.id = id
        for index, task_action in enumerate(self.task_actions):
            if task_action.id:  # pragma: no cover
                raise AgentRunException('Task action id cannot be set repeatedly')
            new_id = f'{self.id}-{index + 1}'
            task_action.set_id(new_id)
            task_action.task_step_id = self.id

    def is_enable_save(self) -> bool:
        return self.task_id is not None

    def set_task_id(self, task_id: str):
        if self.task_id:  # pragma: no cover
            raise AgentRunException('Task id cannot be set repeatedly')
        self.task_id = task_id
        for _index, task_action in enumerate(self.task_actions):
            if task_action.task_id:  # pragma: no cover
                raise AgentRunException("The task_id of Task action cannot be set repeatedly")
            task_action.task_id = task_id

    def append_task_action_with_order(self, new_task_action: TaskAction):
        """ 用于调整 cache 中不按顺序的问题
        """
        assert new_task_action.id, 'append_task_action_with_order new_task_action id cannot be null'
        for index, task_action in enumerate(self.task_actions):
            assert task_action.id, 'append_task_action_with_order task_action id cannot be null'
            if self.compare_task_action_id(task_action.id, new_task_action.id):
                self.task_actions.insert(index, new_task_action)
                return
        self.task_actions.append(new_task_action)

    def compare_task_action_id(self, one_id: str, two_id: str):
        one_first, one_last = one_id.split('-')
        two_first, two_last = two_id.split('-')
        assert one_first == two_first, 'task_step_id of task_action must be the same'
        return int(one_last) >= int(two_last)

    def _generate_new_task_action_id(self):
        if not self.id:  # pragma: no cover
            raise AgentRunException('Generate new task action id error, step id is null')

        if len(self.task_actions) == 0:
            return f'{self.id}-1'

        last_action = self.task_actions[-1]
        if not last_action.id:  # pragma: no cover
            raise AgentRunException("Generate new task action id error, last action id is null")

        first, last = last_action.id.split('-')
        if not first or not last:  # pragma: no cover
            raise AgentRunException("Generate new task action id error, last action id format error")
        new_last = str(int(last) + 1)
        return f'{first}-{new_last}'

    def save(self, *, recursive=False):
        if not self.id or not self.task_id:  # pragma: no cover
            raise AgentRunException('Cannot save task_step without id or task_id')
        self.save_to_redis(self._generate_redis_key())
        if recursive:
            for task_action in self.task_actions:
                task_action.save()

    def delete(self):
        if not self.id or not self.task_id:  # pragma: no cover
            raise AgentRunException('Cannot delete task_step without id or task_id')
        self.delete_from_redis(self._generate_redis_key())
        for task_action in self.task_actions:
            task_action.delete()

    def _generate_redis_key(self):
        return f'task:{self.task_id}:task_step:{self.id}'

    def dict(self, *, full=False, only_self=False, **kwargs):
        if full:
            base_dict = super().dict()
        else:
            base_dict = super().dict(include={'id', 'title', 'task_actions', 'turn'})
        if only_self:
            del base_dict['task_actions']
        else:
            base_dict['task_actions'] = [action.dict(full=full) for action in self.task_actions]
        return base_dict

class Task(RedisModel):
    id: Optional[str] = None
    title: str
    description: str = ''
    think_result: str = ''
    task_steps: List[TaskStep] = []
    run_turn: int = 1 # 用于时光机折叠分组, 用户执行任务后, 轮次加一

    def __init__(self, raw=False, **data):
        if raw:
            super().__init__(**data)
        else:
            safe_fields_task = {'title', 'description'}
            filtered_data = {k: v for k, v in data.items() if k in safe_fields_task}

            super().__init__(**filtered_data)
            self.task_steps = []
            for step_data in data.get('task_steps', []):
                if isinstance(step_data, dict):
                    task_step = TaskStep(**step_data)
                    self.add_task_step(task_step)
                else:
                    self.add_task_step(step_data)

    def add_task_step(self, task_step: TaskStep) -> str:
        """ 注：自动保存 redis
        """
        if task_step.id:  # pragma: no cover
            raise AgentRunException('add_task_step error: id must be null at the beginning')
        task_step.set_id(self._generate_new_task_step_id())
        if self.id:
            task_step.set_task_id(self.id)
        self.task_steps.append(task_step)

        if self.is_enable_save():
            task_step.save(recursive=True)

        assert task_step.id, 'add_task_step task_step id must be existed'
        return task_step.id

    def add_task_step_from_dict(self, task_step_dict: dict) -> str:
        """ 从字典类型数据导入 task_step & task_action

        格式:
            {
                "title": "",
                task_actions: [
                    { "action": xx },
                    { "action": xx }
                ]
            }
        注：自动保存redis
        """
        task_step = TaskStep(title=task_step_dict['title'], turn=self.run_turn)
        for task_action_dict in task_step_dict.get('task_actions', []):
            task_action = TaskAction(**task_action_dict)
            task_step.add_task_action_without_id(task_action)
        return self.add_task_step(task_step)

    def add_task_action_by_task(self, task_step_id: str, task_action: TaskAction) -> str:
        """ 自动保存redis
        """
        task_step = self.find_step_by_id(task_step_id)
        task_action_id = task_step.add_task_action(task_action)

        if self.is_enable_save():
            task_action = self.find_action_by_id(task_action_id)
            task_action.save()

        return task_action_id

    def add_task_action_from_dict(self, task_step_id: str, task_action_dict: dict) -> str:
        """ 自动保存redis
        """
        task_action_id = self.add_task_action_by_task(task_step_id, TaskAction(**task_action_dict))
        return task_action_id

    def append_task_step_with_order(self, new_task_step: TaskStep):
        """ 用于调整 cache 中不按顺序的问题
        """
        assert new_task_step.id, 'append_task_step_with_order new_task_step id cannot be null'
        for index, task_step in enumerate(self.task_steps):
            assert task_step.id, 'append_task_step_with_order task_step id cannot be null'
            if self.compare_task_step_id(task_step.id, new_task_step.id):
                self.task_steps.insert(index, new_task_step)
                return
        self.task_steps.append(new_task_step)

    def compare_task_step_id(self, one_id: str, two_id: str):
        return int(one_id) >= int(two_id)

    def modify_task_step(self, task_step_id: str, new_title: str) -> None:
        """ 自动保存redis
        """
        task_step = self.find_step_by_id(task_step_id)
        task_step.title = new_title

        if self.is_enable_save():
            task_step.save()

    def modify_task_action(self, task_action_id: str, action_info: dict) -> None:
        """ 自动保存redis
        """
        task_action = self.find_action_by_id(task_action_id)
        if 'action' in action_info:
            task_action.action = action_info['action']
        if 'action_object' in action_info:
            action_object_info: dict = action_info['action_object']
            action_object = task_action.action_object

            if isinstance(action_object, FileActionObject):
                fields = {'path', 'target', 'detailed_requirement', 'references'}
            elif isinstance(action_object, CommandActionObject):
                fields = {'command', 'lifetime'}

            fields_to_update = fields & action_object_info.keys()
            if not fields_to_update:
                raise AgentRunException('No task action fields to update')

            for field in fields_to_update:
                setattr(action_object, field, action_object_info[field])

        if self.is_enable_save():
            task_action.save()

    def delete_task_step(self, task_step_id: str) -> None:
        """ 自动删除redis
        """
        task_step = self.find_step_by_id(task_step_id)
        self.task_steps.remove(task_step)

        if self.is_enable_save():
            task_step.delete()

    def delete_task_action(self, task_action_id: str) -> None:
        """ 自动删除redis
        """
        task_step = self.find_step_by_action_id(task_action_id)
        task_action = self.find_action_by_id(task_action_id)
        task_step.task_actions.remove(task_action)

        if self.is_enable_save():
            task_action.delete()

    def cancel_all_actions(self) -> None:
        """ 自动保存redis
        """
        for task_step in self.task_steps:
            for task_action in task_step.task_actions:
                if task_action.status in [ActionStatus.IN_PROGRESS_STATUS, ActionStatus.INIT_STATUS]:
                    task_action.set_status_canceled()

    def get_next_runnable_action(self) -> TaskAction | None:
        for task_step in self.task_steps:
            if task_action := task_step.get_next_runnable_action():
                return task_action
        return None

    def find_step_by_id(self, step_id) -> TaskStep:
        task_step = next((x for x in self.task_steps if x.id == step_id), None)
        if not task_step:
            raise AgentRunException(f'Unable to find the task step corresponding to {step_id}')
        return task_step

    def find_action_by_id(self, action_id) -> TaskAction:
        for task_step in self.task_steps:
            try:
                return task_step.find_action_by_id(action_id)
            except:  # noqa
                pass
        raise AgentRunException(f'The specified task_action_id could not be found: {action_id}')

    def find_step_by_action_id(self, action_id) -> TaskStep:
        """ 通过 action 找到它的 step
        """
        for task_step in self.task_steps:
            try:
                if task_step.find_action_by_id(action_id):
                    return task_step
            except:  # noqa
                pass
        raise AgentRunException(f'The specified task_action_id could not be found: {action_id}')

    def _generate_new_task_step_id(self):
        if len(self.task_steps) == 0:
            return '1'
        last_step = self.task_steps[-1]
        if not last_step.id:  # pragma: no cover
            raise AgentRunException("generate new task step id error")
        return str(int(last_step.id) + 1)

    def set_id(self, new_id):
        self.id = new_id
        for task_step in self.task_steps:
            task_step.set_task_id(new_id)

    def enable_autosave(self, playground_id: str):
        if self.id:
            if self.id != playground_id:
                raise AgentRunException('Enable autosave failed, self id is already set and different with playground_id')
            else:
                return
        logger.debug(f"enable_autosave task: {playground_id}")
        self.set_id(playground_id)
        self.save(recursive=True)

    def is_enable_save(self) -> bool:
        return self.id is not None

    def save(self, *, recursive=False):
        """ task 保存到 redis

        :param recursive: 是否递归保存所有结构，默认为 False，仅保存自身结构
        """
        if not self.id:
            raise AgentRunException('Cannot save task without id')
        self.save_to_redis(self._generate_redis_key())
        if recursive:
            for task_step in self.task_steps:
                task_step.save(recursive=True)

    def delete(self):
        if not self.id:
            raise AgentRunException('Cannot delete task without id')

        self.delete_from_redis(self._generate_redis_key())

        for task_step in self.task_steps:
            task_step.delete()

    def _generate_redis_key(self):
        if not self.id:
            raise AgentRunException('Cannot generate redis key without id')
        return self._generate_redis_key_by_id(self.id)

    def dict(self, *, full=False, only_self=False, **kwargs):
        base_dict = super().dict()
        if only_self:
            del base_dict['task_steps']
        else:
            base_dict['task_steps'] = [step.dict(full=full) for step in self.task_steps]
        return base_dict

    def pretty_print(self):
        from jinja2 import Template
        from heracles.core.utils.prompt_builder_templates import TASK
        return Template(TASK).render(task=self)

    @classmethod
    def load_from_redis(cls, playground_id: str):
        redis_key = cls._generate_redis_key_by_id(playground_id)
        task_data = redis_cache.get(redis_key)
        if not task_data:
            return None

        task = cls(raw=True, **json.loads(task_data))

        task_step_match = cls._generate_task_steps_redis_key_by_id(playground_id)
        for task_step_key in redis_cache.scan_iter(match=task_step_match):
            task_step_dict = json.loads(redis_cache.get(task_step_key))
            task_step_id = task_step_dict['id']
            task_step = TaskStep(raw=True, **task_step_dict)

            task_action_match = cls._generate_task_actions_redis_key_by_id(playground_id, task_step_id)
            for task_action_key in redis_cache.scan_iter(match=task_action_match):
                task_action_dict = json.loads(redis_cache.get(task_action_key))
                task_action = TaskAction(raw=True, **task_action_dict)
                task_step.append_task_action_with_order(task_action)

            task.append_task_step_with_order(task_step)

        logger.debug(f'[cache] load_from_redis {playground_id} task={task}')
        return task

    @classmethod
    async def load_from_redis_async(cls, playground_id: str):
        return await asyncio.to_thread(cls.load_from_redis, playground_id)

    @classmethod
    def _generate_redis_key_by_id(cls, playground_id: str):
        return f'task:{playground_id}'

    @classmethod
    def _generate_redis_all_key_by_id(cls, playground_id: str):
        return f'task:{playground_id}*'

    @classmethod
    def _generate_task_steps_redis_key_by_id(cls, playground_id: str):
        return f'task:{playground_id}:task_step:*'

    @classmethod
    def _generate_task_actions_redis_key_by_id(cls, playground_id: str, task_step_id: str):
        return f'task:{playground_id}:task_step_by:{task_step_id}:task_action:*'

    @classmethod
    def _generate_task_delete_all_key(cls):
        return 'task:*'
