import re
from urllib.parse import urlparse, parse_qs


def camelize(string: str, uppercase_first_letter: bool = True) -> str:
    """
    Convert strings to CamelCase.

    Examples::

        >>> camelize("device_type")
        'DeviceType'
        >>> camelize("device_type", False)
        'deviceType'

    :func:`camelize` can be thought of as a inverse of :func:`underscore`,
    although there are some cases where that does not hold::

        >>> camelize(underscore("IOError"))
        'IoError'

    :param uppercase_first_letter: if set to `True` :func:`camelize` converts
        strings to UpperCamelCase. If set to `False` :func:`camelize` produces
        lowerCamelCase. Defaults to `True`.
    """
    if uppercase_first_letter:
        return re.sub(r'(?:^|_)(.)', lambda m: m.group(1).upper(), string)
    else:
        return string[0].lower() + camelize(string)[1:]


def underscore(word: str) -> str:
    """
    Make an underscored, lowercase form from the expression in the string.

    Example::

        >>> underscore("DeviceType")
        'device_type'

    As a rule of thumb you can think of :func:`underscore` as the inverse of
    :func:`camelize`, though there are cases where that does not hold::

        >>> camelize(underscore("IOError"))
        'IoError'

    """
    word = re.sub(r'([A-Z]+)([A-Z][a-z])', r'\1_\2', word)
    word = re.sub(r'([a-z\d])([A-Z])', r'\1_\2', word)
    word = word.replace('-', '_')
    return word.lower()


def remove_ansi_escape_sequences(input_string: str) -> str:
    # 定义ANSI转义序列的正则表达式模式
    ansi_escape_pattern = re.compile(r'(\x1B[@-_][0-?]*[ -/]*[@-~])|\x1B=|\x1B>')
    # 使用sub方法替换所有匹配的转义序列为空字符串
    cleaned_string = ansi_escape_pattern.sub('', input_string)
    return cleaned_string.strip('\r')


def split_url(url):
    # 解析 URL
    parsed_url = urlparse(url)

    # 提取协议头
    scheme = parsed_url.scheme

    # 提取域名
    domain = parsed_url.netloc

    # 提取路径
    path = (
        parsed_url.path
        + ('?' + parsed_url.query if parsed_url.query else '')
        + ('#' + parsed_url.fragment if parsed_url.fragment else '')
    )

    return f'{scheme}://{domain}', path

def extract_query_param(query_string: str, param_name: str) -> str | None:
    """ 解析 query_string，解出 param_name 对应内容（只取第一个）
    """
    if not query_string:
        return None
    return parse_qs(query_string).get(param_name, [None])[0]

def truncate(text: str, length: int = 20, suffix: str = '...') -> str:
    """Truncate text to specified length and add suffix if necessary"""
    return text[:length] + suffix if len(text) > length else text


def splitlines_cmd(cmd: str) -> list[str]:
    """分割字符串为多行并去掉每行的首尾空格

    Args:
        cmd: 要分割的字符串

    Returns:
        处理后的行列表，已去除每行首尾空格
    """
    return [line.strip() for line in cmd.splitlines() if line.strip()]
