FILE_TREE = """## File Tree
{% if file_list -%}
Below are all files of the project, read files already exist:
{{- ', including hidden files' if show_hidden else ', excluding hidden files' }}
{{- ', including ignored files' if show_ignored else ', excluding ignored files' }}:
```
{{ file_list | join('\n') }}
```
{% else -%}
> This is an empty project. No files or directories exist yet!
> Never use tool to read any file! for there is exactly no file in the project!
```
(empty)

```
{% endif %}"""

PLAYBOOKS = """{% if playbooks -%}
## Playbooks (potential useful articles) might help:
{% for playbook in playbooks -%}
### {{ playbook.title }}
```markdown
{{ playbook.content }}
```
{% endfor %}
{% endif %}"""

PLAYBOOK_LIST = """
{% if playbooks -%}
## Playbooks
Potential useful articles to understand how to solve the task

```json
{% for playbook in playbooks -%}
{{ playbook }}
{% endfor %}
```
{%- else -%}
> No playbooks provided, Do not reference playbooks.
{% endif %}"""

WEB_PAGES = """
{% if webpages -%}
## User provided webpage references:
{% for webpage in webpages -%}
URL: {{ webpage.url }}
```markdown
{{ webpage.content }}
```
{% endfor %}
{% endif %}"""

PROJECT_KNOWLEDGE = """## {{ item.section_header }}
{%- if item.name == "project_structure" %}
{{ workspace.project_knowledge.project_structure }}
{%- else %}
{%- for basic_info in workspace.project_knowledge.basic_info_list %}
{% if item.name == "project_basic_info" -%}
APP_DIR: `{{ basic_info.app_dir }}`
- Language: {{ basic_info.language }}
- Framework: {{ basic_info.framework }} {{ basic_info.framework_version }}
- Linter Config: {{ basic_info.linter_config }}
- Project Runtime Requirement: {{ basic_info.runtime }} {{ basic_info.runtime_version }}
- Project Middlewares: {{ basic_info.middlewares }}
{% else -%}
```
{{ basic_info | attr(item.name) }}
```
{%- endif %}
{%- endfor %}
{%- if item.name == "project_basic_info" %}
Current Runtime Environment:
```
{{ workspace.playground.ide_server_client.environment["runtime"] }}
```
Current Runtime Middlewares:
{%- if current_middlewares %}

> ### Middleware Usage Guide
>
> Specify the host parameter explicitly (e.g. `-h 127.0.0.1`) when interacting with the database server
> - Use provided connection parameters for the database server
> - Never try to install or restart the database server
> - Before starting the project setup, ensure the target database exists
> - Use `run_cmd` tool to create the database if it doesn't exist
> - Database schema and table creation should be handled separately using migrations or scripts
>    Examples:
>    ```bash
>    # PostgreSQL
>    PGPASSWORD=your_password createdb -h your_db_host -U your_db_username your_db_name
>
>    # MySQL
>    MYSQL_PWD=your_password mysql -h your_db_host -u your_db_username -e "CREATE DATABASE your_db_name"
>    ```

```
{{ current_middlewares | join(', ') }}
```
{%- else %}
> No middleware bound to this environment.
```
No middleware bound to this environment.
```
{%- endif %}
{%- endif %}
{%- endif %}
"""

CLACKY_RULES = """
{% if clacky_rules -%}
# ADDITIONAL RULES
> IMPORTANT: Below rules may contain arbitrary content, follow these rules if they are compatible with above rules
> If you are writing/generating codes and any content with strict syntax and formatting requirements, apply the following rules ensuring compliance with strict syntax and formatting requirements and DO NOT generate conversational content even if below rules require.
```
{{clacky_rules}}
```
{%- endif %}""" # noqa

FILE_SNIPPETS = """{% if snippets -%}
## File Snippets (potential useful or related files):
- These files are provided as context and are the latest version, think twice before using the `read_file` tool to retrieve them again
- These files are **up-to-date** and should be trusted as reference material - they reflect the current state of the codebase
- DO NOT use the `read_file` tool unless absolutely necessary - only when you need to see content beyond the provided snippets (lines shown in brackets):
{% for snippet in snippets -%}
- {{snippet.path }} [{{snippet.row_start}}-{{snippet.row_end}}]
{% endfor %}

{% for snippet in snippets -%}
```xml
<RELATED_FILE_SNIPPET index="{{ loop.index0 }}">
<FILE_PATH>
{{ snippet.path }}
</FILE_PATH>
<INSTRUCTION>
- The content below is up-to-date. Do not use tools to read this file again.
- Line numbers before each line in `CONTENT`(e.g., `   1: ...`) are for reference only and are not part of the file content.
{% for instruction in snippet.instructions -%}
- {{ instruction }}
{% endfor %}
</INSTRUCTION>
<CONTENT rowStart="{{ snippet.row_start }}" rowEnd="{{ snippet.row_end }}">
{{ snippet.content }}
</CONTENT>
</RELATED_FILE_SNIPPET>
```
{% endfor %}
{% endif %}""" # noqa

ERRORS = """{% if errors -%}
## Project has encountered some errors:
{% for error in errors -%}
### {{ error.ref_id }}
{% if error.category != 'screenshot' -%}
```
{{ error.content }}
```
{% else -%}
screenshot image provided already
{% endif %}
{% endfor %}
{% endif %}"""

TASK = """{% if task -%}
## Active Task: {{ task.title }}

{{ task.description }}

{% if task.task_steps -%}
{% for step in task.task_steps %}
### Step {{ step.id }}: {{ step.title }}
{{ step.description }}
{% for action in step.task_actions %}
Action {{ action.id }} ({{ action.status }})
{% if action.action_object.__class__.__name__ == "FileActionObject" %}
{%- if 'move' in action.action %}
* {{ action.action }}: `{{ action.action_object.path }}` to `{{ action.action_object.target }}`
{%- else %}
* {{ action.action }}: `{{ action.action_object.path }}`
{%- if action.action_object.detailed_requirement %}
* Detailed Requirement: {{ action.action_object.detailed_requirement }}
{%- endif %}
{%- if action.action_object.references %}
* References: {{ action.action_object.references }}
{%- endif %}
{% endif %}
{%- elif action.action_object.__class__.__name__ == "CommandActionObject" %}
* Run `{{ action.action_object.command }}` in shell
{% endif %}
{%- endfor %}

{% endfor %}
{%- endif %}
{% endif %}"""

IN_PROGRESS_TASK_ACTION = """
### You are now working on Action {{ task_action.id }}

Generate content for file `{{ task_action.action_object.path }}`.

Carefully review the provided `detailed_requirement` and context provided, and provide a complete code modification solution in one go.
- Ensure that your changes maintain functionality and do not introduce new issues, such as neglecting to update references when methods or components are modified.

#### Detailed Requirement
{{ task_action.action_object.detailed_requirement }}

""" # noqa

RECENT_FILE_CHANGES = """
{%- if diffs -%}
## Recent File Changes

{% for path, diff in diffs.items() %}
{%- if diff -%}
File: `{{ path }}`
```diff
{{ diff }}
```
{%- endif %}
{% endfor %}
{%- endif %}
"""

REFERENCES = """
{%- if references -%}
### References

{% for reference in references -%}
{{ reference }}
{% endfor %}
{%- endif -%}
"""

IMAGES = """
{%- if images -%}
## User provided image link references:
{% for image_url in images -%}
{{ image_url }}
{% endfor %}
{%- endif -%}
"""

ERROR_REPORT = """
{%- if error_report -%}
# Error Report
## Summary
{{ error_report.summary }}

{% for error in error_report.errors -%}
### Error: {{ error.title }}
- Content: {{ error.content }}
{% endfor %}
{%- endif -%}
"""

ENVIRONMENT_LOG = """
{%- if environment_log -%}
{% if environment_log.lint_log -%}
## Lint Log
{{ environment_log.lint_log }}
{%- endif -%}

{% if environment_log.terminal_log -%}
## Terminal Log
{{ environment_log.terminal_log }}
{%- endif -%}

{% if environment_log.browser_log -%}
## Browser Log
{{ environment_log.browser_log }}
{%- endif -%}
{%- endif -%}
"""
