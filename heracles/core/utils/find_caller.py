import inspect


def find_caller():
    ignore_func_names = ["aask"]
    frame = inspect.currentframe()
    try:
        # 跳过当前函数
        frame = frame.f_back # type: ignore

        while frame:
            func_name = frame.f_code.co_name
            if not any(func_name.startswith(name) for name in ignore_func_names):
                return func_name
            frame = frame.f_back

        # 如果没有找到非aask开头的调用者，返回None
        return None  # pragma: no cover
    finally:
        # 确保引用被清理，防止循环引用
        del frame
