import json
from datetime import datetime

from json_repair import repair_json  # type: ignore

from heracles.core.exceptions import AgentLLMJSONEx<PERSON>


def my_default_encoder(obj):
    """Custom JSON encoder that handles datetime"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    return json.JSONEncoder().default(obj)


def dumps(obj, **kwargs):
    """Serialize an object to str format"""
    return json.dumps(obj, default=my_default_encoder, **kwargs)


def loads(json_str, **kwargs):
    """Create a JSON object from str"""
    try:
        return json.loads(json_str, **kwargs)
    except json.JSONDecodeError:
        pass
    depth = 0
    start = -1
    for i, char in enumerate(json_str):
        if char == '{':
            if depth == 0:
                start = i
            depth += 1
        elif char == '}':
            depth -= 1
            if depth == 0 and start != -1:
                response = json_str[start : i + 1]
                try:
                    json_str = repair_json(response)
                    return json.loads(json_str, **kwargs)
                except (json.J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ValueError, TypeError) as e:  # pragma: no cover
                    raise AgentLLMJSONException(
                        'Invalid JSON in response. Please make sure the response is a valid JSON object.'
                    ) from e
    raise AgentLLMJSONException('No valid JSON object found in response.')
