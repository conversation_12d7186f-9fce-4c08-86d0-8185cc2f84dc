import litellm
from qdrant_client import models
from heracles.core.schema import AdminPlaybookModel, AdminPlaybookStatus
from heracles.server.admin.config import get_settings, get_sparse_model


async def create_dense_embedding(text: str):
    settings = get_settings()
    response = litellm.embedding(
        model=settings.dense_embedding_model,
        input=[text],
        api_base=settings.llm_base_url,
        api_key=settings.llm_api_key,
    )
    embedding = response.data[0]['embedding']
    return embedding


async def create_sparse_embedding(text: str):
    sparse_model = get_sparse_model()
    embedding = next(sparse_model.embed(text))
    return embedding


def record_to_playbook(record):
    data = record.payload
    data['id'] = record.id
    playbook = AdminPlaybookModel.model_validate(data)
    return playbook


async def playbook_to_point(playbook: AdminPlaybookModel):
    settings = get_settings()
    embedding_text = playbook.title + '\n\n' + playbook.description + '\n\n' + playbook.original_content
    payload = playbook.model_dump(mode='json')
    playbook_id = payload.pop('id')
    if playbook.status == AdminPlaybookStatus.ready:
        dense_embedding = await create_dense_embedding(embedding_text)
        sparse_embedding = await create_sparse_embedding(embedding_text)
        point = models.PointStruct(
            id=playbook_id,
            payload=payload,
            vector={
                settings.vector_db_dense_vector_name: dense_embedding,
                settings.vector_db_sparse_vector_name: sparse_embedding.as_object(),
            },
        )
    else:
        point = models.PointStruct(id=playbook_id, payload=payload, vector={})
    return point
