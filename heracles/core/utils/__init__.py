import asyncio
import traceback
from typing import Awaitable, Callable, Optional, Any
from heracles.core.exceptions import AgentRunException
from heracles.core.schema import ErrorHandlerMessage, ErrorHandlerMessageType
from pydantic import ValidationError

def add_asyncio_handler(
    obj,
    handler_name,
    future_task: Awaitable,
    error_trigger_callback: Callable[[ErrorHandlerMessage], Awaitable],
    error_type: ErrorHandlerMessageType,
    logger,
    result_callback: Optional[Callable[[Any], Awaitable]] = None,
):
    """ 为指定 obj 添加正确处理好异常版本的异步任务事件
    :param obj: 实例对象
    :param handler_name: 字符串, 被赋值的异步任务变量, 要提前声明, 否则会报异常
    :param future_task: 具体任务, 必须要可被 await
    :param error_trigger_callback: 出现异常时调用方法
    :param error_type: 出现异常时的错误类型，调用 callback 时使用
    :param logger: 出现异常时打印日志所使用日志对象, 确保在指定 scope 中打印日志
    :param result_callback: 任务执行成功后调用方法

    举例: add_asyncio_handler(agent_controller, '_asyncio_message', trigger_error, 'message', logger)
    """
    human_name = f'{obj.__class__.__name__}.{handler_name}'
    if not hasattr(obj, handler_name):
        raise AgentRunException(f'add_asyncio_handler error: `{human_name}` is None')

    if getattr(obj, handler_name):
        raise AgentRunException(f'Another task `{human_name}` is running, please wait and try again later.')

    async def wrapper(future_task):
        try:
            result = await future_task
            if result_callback is not None:
                await result_callback(result)
            return result
        except Exception as e:
            if isinstance(e, ValidationError):
                # Format validation errors in a cleaner way
                error_details = []
                for error in e.errors():
                    field = ".".join(str(x) for x in error["loc"])
                    error_details.append(f"- {field}: {error['msg']}")
                error_msg = "Validation Error:\n" + "\n".join(error_details)
            else:
                error_msg = str(e)
                logger.error(f"Asyncio `{human_name}` error\nTraceback: {traceback.format_exc()}")

            error_handler_content = f"asyncio_handler `{handler_name}` error, {error_msg}"
            error_handler_message = ErrorHandlerMessage(error_type=error_type, content=error_handler_content)
            await error_trigger_callback(error_handler_message)
        finally:
            setattr(obj, handler_name, None)

    setattr(obj, handler_name, asyncio.create_task(wrapper(future_task)))

def cancel_asyncio_handler(obj, handler_name):
    if task := getattr(obj, handler_name, None):
        task.cancel()
        setattr(obj, handler_name, None)

async def wait_for(cond_callback: Callable, timeout=2, interval=0.1):
    loop_count = timeout / interval
    loop_time = 0
    while not cond_callback():
        if loop_time >= loop_count:
            break
        await asyncio.sleep(interval)
        loop_time += 1
    if not cond_callback():  # pragma: no cover
        raise AgentRunException(f'wait_for `{cond_callback.__name__}` timeout({timeout}s)')

def delayed_debouncer(delay, task_handle_name: str, error_callback_name: str):
    """ 延迟 x 秒后执行任务，延迟内重复触发只会执行最后一次，防止重复执行。
    装饰器方法示例：@delayed_debouncer(3, 'debounce_task')

    :param delay: 延迟时间
    :param task_handle_name: 用于标记延迟任务的名称
    """
    def decorator(task):
        async def wrapper(self, *args, **kwargs):
            human_name = f'{self.__class__.__name__}.{task_handle_name}'
            if not hasattr(self, task_handle_name):
                raise AgentRunException(f'Delayed Debouncer error: `{human_name}` does not exist')
            debounce_task = getattr(self, task_handle_name, None)

            if debounce_task:
                debounce_task.cancel()

            async def delayed_task():
                await asyncio.sleep(delay)
                try:
                    await task(self, *args, **kwargs)
                except Exception as e:
                    error_callback = getattr(self, error_callback_name, None)
                    if error_callback:
                        await error_callback(f"Delayed debouncer `{human_name}` error: {e}")

            setattr(self, task_handle_name, asyncio.create_task(delayed_task()))

        return wrapper
    return decorator

# 加快测试 100 倍
def mock_sleep(mocker):
    original_sleep = asyncio.sleep
    async def shorter_sleep(time):
        return await original_sleep(time * 0.01)
    mocker.patch('asyncio.sleep', side_effect=shorter_sleep)

# 加快测试 100 倍
def mock_asyncio_wait_for(mocker):
    original_wait_for = asyncio.wait_for
    async def shorter_wait_for(future, timeout):
        if not timeout:
            timeout = None
        else:
            timeout = timeout * 0.001
        return await original_wait_for(future, timeout)
    mocker.patch('asyncio.wait_for', side_effect=shorter_wait_for)

class DotDict:
    def __init__(self, dict_data):
        for key, value in dict_data.items():
            if isinstance(value, dict):
                value = DotDict(value)
            elif isinstance(value, list):
                value = [DotDict(item) if isinstance(item, dict) else item for item in value]
            setattr(self, key, value)
