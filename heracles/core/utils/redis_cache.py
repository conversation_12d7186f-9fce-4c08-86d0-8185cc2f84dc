import re
import redis
import random

from redis import Redis
from typing import Union

from heracles.core.config import get_env_var
from heracles.core.logger import heracles_logger as logger

class CacheNone:
    def __init__(self):
        self.cache_dict = {}

    def get(self, key):
        return self.cache_dict.get(key)

    def set(self, key, value):
        self.cache_dict[key] = value

    def delete(self, key):
        if key in self.cache_dict:
            del self.cache_dict[key]

    def keys(self, pattern_str: str):
        pattern = re.compile(pattern_str.replace("*", ".*").replace("?", "."))
        return [key for key in self.cache_dict.keys() if pattern.match(key)]

    def scan_iter(self, *, match, count=None):
        keys = self.keys(match)
        random.shuffle(keys)
        for key in keys:
            yield key

def connect_to_redis():
    redis_cache: Union[Redis, CacheNone]
    if host := get_env_var('CACHE_REDIS_HOST_OPTIONAL'):
        logger.info('[cache] feature is enabled, now loading port and db config')
        port = get_env_var('CACHE_REDIS_PORT_OPTIONAL', must=True)
        db = get_env_var('CACHE_REDIS_DB_OPTIONAL', must=True)
        redis_cache = redis.Redis(host=host, port=port, db=db, decode_responses=True)
        redis_cache.ping()
    else:
        logger.info('[cache] feature is disabled')
        redis_cache = CacheNone()
    return redis_cache

redis_cache = connect_to_redis()
