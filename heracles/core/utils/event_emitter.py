import asyncio

from heracles.core.exceptions import AgentException


class EventEmitter:
    def __init__(self, bubbling=False):
        self._events = {}
        self.bubbling = bubbling

    def on(self, event, listener):
        """注册事件监听器"""
        if event not in self._events:
            self._events[event] = []
        self._events[event].append(listener)

    def off(self, event, listener):
        """移除指定事件监听器"""
        if event not in self._events:
            raise AgentException(f'off error: no such event {event}')
        self._events[event].remove(listener)
        # 清理完如果没有值了 则删除整个键
        if not self._events[event]:
            del self._events[event]

    def off_all(self, event):
        """移除该事件所有监听器"""
        if event not in self._events:
            raise AgentException(f'off error: no such event {event}')
        del self._events[event]

    async def emit(self, event, *args, **kwargs):
        """触发事件"""
        if event not in self._events:
            raise AgentException(f'emit error: no such event {event}')
        if self.bubbling:
            for listener in self._events[event]:
                if asyncio.iscoroutinefunction(listener):
                    await listener(*args, **kwargs)
                else:
                    listener(*args, **kwargs)
        else:
            listener = self._events[event][-1]
            if asyncio.iscoroutinefunction(listener):
                return await listener(*args, **kwargs)
            else:
                return listener(*args, **kwargs)

    def has_listener(self, event):
        """判断事件是否有被注册"""
        return event in self._events and bool(self._events[event])
