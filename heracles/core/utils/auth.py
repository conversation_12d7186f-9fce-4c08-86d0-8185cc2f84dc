import jwt
from typing import Optional
from heracles.core.config import get_env_var
from heracles.core.logger import get_logger

logger = get_logger()

def verify_jwt_token(token: Optional[str]) -> bool:
    """
    验证JWT token的有效性

    Args:
        token (Optional[str]): 要验证的JWT token，可以是字符串或None

    Returns:
        bool: 如果token有效返回True，否则返回False

    Note:
        如果环境变量IDE_SERVER_DEBUG_OPTIONAL为True，则跳过验证直接返回True
    """
    # 检查是否处于调试模式
    debug_mode = get_env_var('IDE_SERVER_DEBUG_OPTIONAL', 'False').lower() == 'true'
    if debug_mode:
        logger.debug("Debug mode enabled, skipping JWT validation")
        return True

    # 检查token是否为None
    if token is None:
        logger.warning("Token is None, validation failed")
        return False

    try:
        jwt_key = get_env_var('AUTH_JWT_KEY_OPTIONAL')
        jwt_secret = get_env_var('AUTH_JWT_SECRET_OPTIONAL')

        if not jwt_key or not jwt_secret:
            logger.warning("JWT key or secret not set, cannot verify token")
            return False

        # 解码并验证token
        jwt.decode(
            token,
            jwt_secret,
            algorithms=["HS256"],
            options={"verify_signature": True}
        )

        return True
    except jwt.ExpiredSignatureError:
        logger.warning("Token has expired")
        return False
    except jwt.InvalidTokenError:
        logger.warning("Invalid token")
        return False
    except Exception as e:
        logger.error(f"Error verifying token: {str(e)}")
        return False
