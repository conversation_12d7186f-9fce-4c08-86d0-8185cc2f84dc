import copy
import litellm
from typing import List, Dict, Any, Optional
from litellm.utils import token_counter

from heracles.core.logger import logger


def shorten_text_to_fit_limit(text: str, tokens_needed: int, model: str, max_iterations: int = 300, min_content_length: int = 20) -> str:
    """
    Shorten a text to fit within a token limit by removing characters from the middle.
    Uses a gradual approach to find the maximum possible text length that fits within token limit.

    Args:
        text: Text to shorten
        tokens_needed: Target token count to fit within
        model: Model name for token counting
        max_iterations: Maximum number of shortening attempts (default: 300)
        min_content_length: Minimum content length to maintain (default: 20)

    Returns:
        Shortened text
    """
    if not text:
        return text

    current_tokens = token_counter(model=model, messages=del_unsupported_key_in_messages([{'content': text, 'role': 'user'}]))
    if current_tokens <= tokens_needed:
        return text

    current_text = text
    best_result = text

    ratio = tokens_needed / current_tokens

    for _ in range(max_iterations):
        total_length = len(current_text)
        keep_from_start = int(total_length * ratio * 2 / 3)
        keep_from_end = int(total_length * ratio * 1 / 3)

        keep_from_start = max(min_content_length, keep_from_start)
        keep_from_end = max(min_content_length, keep_from_end)

        new_text = current_text[:keep_from_start] + '...' + current_text[-keep_from_end:]

        current_tokens = token_counter(model=model, messages=del_unsupported_key_in_messages([{'content': new_text, 'role': 'user'}]))

        logger.info(
            f'{_} -- len: {total_length}, token: {tokens_needed}, current: {current_tokens},ratio: {ratio}, start: {keep_from_start}, end: {keep_from_end}'  # noqa
        )

        if current_tokens <= tokens_needed + 50:
            best_result = new_text
            break
        else:
            ratio = ratio * 0.95

        current_text = new_text

        if len(current_text) <= min_content_length * 2 + 3 + 10:
            break

    return best_result


def shorten_message_to_fit_limit(
    message: Dict[str, Any], tokens_needed: int, model: str, max_iterations: int = 300, min_content_length: int = 10
) -> Dict[str, Any]:
    """
    Shorten a message to fit within a token limit by removing characters from the middle.

    Args:
        message: Message dictionary containing content
        tokens_needed: Target token count to fit within
        model: Model name for token counting
        max_iterations: Maximum number of shortening attempts (default: 300)
        min_content_length: Minimum content length to maintain (default: 10)

    Returns:
        Shortened message dictionary
    """
    # For OpenAI models, even blank messages cost 7 tokens
    if tokens_needed <= 10:
        return message

    content = message.get('content', '')
    if not content:
        return message

    # Handle content as list of dictionaries
    if isinstance(content, list):
        # If content is a list, we'll try to shorten each text item
        for item in content:
            if isinstance(item, dict) and 'text' in item:
                item['text'] = shorten_text_to_fit_limit(item['text'], tokens_needed, model, max_iterations, min_content_length)
        return message

    # Handle content as string
    message['content'] = shorten_text_to_fit_limit(content, tokens_needed, model, max_iterations, min_content_length)

    return message


def trim_messages(
    messages: List[Dict[str, Any]], model: str, max_tokens: Optional[int] = None, trim_ratio: float = 0.75
) -> List[Dict[str, Any]]:
    """
    Trim messages to fit within the model's token limit.
    trim逻辑：
        1. 获取模型真实名称，获取模型最大输入max_input_token
        2. 取max_token=max_input_token*ratio(0.75)，确保最终返回的消息token数不会超过max_token
        3. 将message分为system_messages和non_system_messages
        4. 如果system_messages的token数超过max_token，则缩短system_messages到最大max_token*0.3
        5. 将第一条user_message加入到messages中，控制其最大token数为max_token*0.4
        6. 从后往前遍历non_system_messages
            - 如果当前messages的token数未超过max_token，则将当前message加入到messages中
            - 如果当前messages的token数超过max_token，则缩短当前message到max_token-当前messages的token数, 再加入到messages中并退出循环
        7. 确保tool_call的结果和调用一定成双成对
        8. 确保sys之后一定跟一条user消息
        9. 返回最终的messages - 由于67步的后处理,最后返回的messages token可能略大于max_token, 但由于ratio的设置, 这个误差可以忽略不计
    Args:
        messages: List of message dictionaries
        model: Model name to use for token counting
        max_tokens: Optional maximum tokens limit. If not provided, will use model's default
        trim_ratio: Ratio of max_tokens to use as target (default: 0.75)

    Returns:
        Trimmed list of messages
    """
    if not messages:
        return messages

    # Get model info
    model_info = litellm.model_cost.get(model, {})
    if not model_info:
        logger.warning(f'Model {model} not found in model_cost, using default max_tokens')
        max_tokens = max_tokens or 200000
    else:
        max_tokens = max_tokens or model_info.get('max_input_tokens', 200000)

    if len(str(messages)) < max_tokens * 4:
        return messages

    target_tokens = int(max_tokens * trim_ratio)
    total_tokens = token_counter(model=model, messages=del_unsupported_key_in_messages(messages))

    if total_tokens <= target_tokens:
        return messages

    system_messages = [m for m in messages if m.get('role') == 'system']
    non_system_messages = [m for m in messages if m.get('role') != 'system']
    tool_call_id_2_item = {}
    for m in non_system_messages:
        if m.get('tool_calls'):
            for t in m.get('tool_calls', []):
                tool_call_id_2_item[t.get('id')] = t

    system_tokens = token_counter(model=model, messages=del_unsupported_key_in_messages(system_messages))

    if system_tokens > target_tokens:
        for i in range(len(system_messages)):
            current_system_messages = system_messages[i:]
            current_system_tokens = token_counter(model=model, messages=del_unsupported_key_in_messages(current_system_messages))
            if current_system_tokens <= target_tokens:
                system_messages = current_system_messages
                break
        else:
            # If we can't fit any system messages, keep only the last one and shorten it
            if system_messages:
                shortened_message = shorten_message_to_fit_limit(copy.deepcopy(system_messages[-1]), int(target_tokens * 0.3), model)
                system_messages = [shortened_message]

    system_tokens = token_counter(model=model, messages=del_unsupported_key_in_messages(system_messages))
    remaining_tokens = target_tokens - system_tokens

    if remaining_tokens < 0:
        logger.warning('System messages exceed target tokens. Trimming to single shortened system message.')
        shortened_message = shorten_message_to_fit_limit(copy.deepcopy(system_messages[0]), target_tokens, model)
        return [shortened_message, {'role': 'user', 'content': '...'}]

    trimmed_messages = copy.deepcopy(system_messages)
    current_tokens = system_tokens

    first_user_message = None
    for message in non_system_messages:
        if message.get('role') == 'user':
            first_user_message = message
            break

    if first_user_message:
        shortened_message = shorten_message_to_fit_limit(copy.deepcopy(first_user_message), int(target_tokens * 0.4), model)
        trimmed_messages.insert(len(system_messages), shortened_message)
        current_tokens += token_counter(model=model, messages=del_unsupported_key_in_messages([shortened_message]))
    else:
        logger.warning('No user message found in non_system_messages')

    for message in reversed(non_system_messages):
        if message.get('role') == 'user' and first_user_message and message.get('content') == first_user_message.get('content'):
            continue

        message_tokens = token_counter(model=model, messages=del_unsupported_key_in_messages([message]))

        if current_tokens + message_tokens > target_tokens:
            shortened_message = shorten_message_to_fit_limit(copy.deepcopy(message), target_tokens - current_tokens, model)
            trimmed_messages.insert(len(system_messages), shortened_message)
            break
        else:
            trimmed_messages.insert(len(system_messages), message)
            current_tokens += message_tokens

    # 确保tool_call成双成对
    tool_call_items = []
    for m in trimmed_messages[len(system_messages) :]:
        if m.get('role') == 'tool':
            tool_call_items.append(tool_call_id_2_item[m.get('tool_call_id')])
        else:
            break
    if tool_call_items:
        trimmed_messages.insert(len(system_messages), {'role': 'assistant', 'content': '', 'tool_calls': list(reversed(tool_call_items))})

    # 确保sys之后一定跟一条user消息
    if len(trimmed_messages) > len(system_messages) and trimmed_messages[len(system_messages)].get('role') != 'user':
        for msg in non_system_messages:
            if msg.get('role') == 'user':
                shortened_user_msg = shorten_message_to_fit_limit(copy.deepcopy(msg), 100, model)
                trimmed_messages.insert(len(system_messages), shortened_user_msg)
                break
        else:
            trimmed_messages.insert(len(system_messages), {'role': 'user', 'content': '...'})
            logger.error('trim messages - no user message found')

    final_tokens = token_counter(model=model, messages=del_unsupported_key_in_messages(trimmed_messages))
    logger.info(f'trim messages - target_tokens: {target_tokens}, original_tokens: {total_tokens}, current_tokens: {final_tokens}')
    return trimmed_messages


def del_unsupported_key_in_messages(messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    删除token_counter不支持的key
    """
    keys = ['cache_control']
    new_messages = copy.deepcopy(messages)
    for m in new_messages:
        for k in keys:
            if k in m:
                del m[k]
            if isinstance(m.get('content'), list):
                for item in m.get('content', []):
                    if k in item:
                        del item[k]
    return new_messages
