import re
from typing import List, Any
from heracles.core.schema import FileChangeItem
from heracles.core.exceptions import AgentRunException

def parse_file_list_message_to_list(message: str) -> List[dict]:
    """将 git message 转为文件变更列表字典"""
    file_list = _parse_git_file_change_message_no_rename(message)
    return list(map(lambda x: x.dict(), file_list))

def _parse_git_file_change_message_no_rename(input_data: str) -> List[FileChangeItem]:
    """将 git status 和 git diff 输出的message解析为 List[FileChangeItem]"""
    # 验证输入
    if not input_data:
        return []

    lines = input_data.split('\n')
    file_changes = []

    for line in lines:
        if not line.strip():
            continue

        pattern = r'^(M|A|D|R|C|U|\?|!| )(M|A|D|R|C|U|\?|!| )?[\t ]+(.+)$'
        # 验证每行的格式
        match = re.match(pattern, line)
        if not match:
            raise AgentRunException(f'Parse file change error, wrong line format: {line}')
        status_index, status_worktree, path = match.groups()
        path = path.strip().strip('"')

        try:
            path = path.encode('latin1').decode('utf-8')
        except UnicodeDecodeError:
            pass
        except UnicodeEncodeError:
            pass

        ai = False
        changes: List[Any] = []

        if status_worktree == ' ':
            status_worktree = status_index

        status = status_worktree or status_index

        # 将 "?" 改为 "U"
        if status == '?':
            status = 'U'

        file_change = FileChangeItem(
            path=path,
            status=status,
            ai=ai,
            changes=changes,
        )
        file_changes.append(file_change)

    return file_changes
