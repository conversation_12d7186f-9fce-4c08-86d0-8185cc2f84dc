import json
import litellm
from typing import Callable, get_origin
from collections.abc import Iterable as CollectionsIterable


def llm_function_to_json(function: Callable):
    function_info = litellm.utils.function_to_dict(function)
    return {'type': 'function', 'function': function_info}

def response_model_to_json_schema(base_model_cls):
    if get_origin(base_model_cls) == CollectionsIterable:
        item_type = base_model_cls.__args__[0]
        return item_type.model_json_schema()
    return base_model_cls.model_json_schema()

def response_model_to_llm_tool_schema(base_model_cls):
    if get_origin(base_model_cls) == CollectionsIterable:
        item_type = base_model_cls.__args__[0]

        # For Iterable types, we need to manually build the schema
        schema: dict = {
            'type': 'object',
            'properties': {
                'items': {
                    'type': 'array',
                    'items': {'type': 'object', 'properties': {}}
                }
            }
        }

        items_schema = response_model_to_json_schema(item_type)
        if isinstance(items_schema, dict):
            schema['properties']['items']['items'] = items_schema

        return {'type': 'function', 'function': {
            'name': f'Iterable{getattr(item_type, "__name__", "Unknown")}',
            'parameters': schema
        }}

    # For non-Iterable types that have model_json_schema
    return {'type': 'function', 'function': {
        'name': getattr(base_model_cls, '__name__', 'Unknown'),
        'parameters': response_model_to_json_schema(base_model_cls)
    }}

def tool_call_to_argument_pair(tool_call):
    func_name = tool_call.function.name
    tool_name = func_name.replace('_', ' ').title()
    arguments = json.loads(tool_call.function.arguments)
    if not arguments:
        return tool_name, ""
    key = next(
        (k for k in ['name', 'keyword', 'path'] if k in arguments),
        list(arguments.keys())[0]
    )
    value = arguments[key]
    if func_name == "read_file":    # FIXME hard_code
        if (arguments.get("start_line") and arguments.get("end_line")) and not arguments.get("should_read_entire_file"):
            value = f"{arguments['path']}:L{arguments['start_line']}-{arguments['end_line']}"
    return tool_name, value
