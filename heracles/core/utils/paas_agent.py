from heracles.core.exceptions import PaasAgentException
import aiohttp

async def paas_request(method: str, url: str, data=None, timeout=10):
    timeout = aiohttp.ClientTimeout(total=timeout)

    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.request(method, url, json=data) as response:
            if response.status != 200:
                raise PaasAgentException(f"Request error, status code: {response.status}")
            resp = await response.json()
            if resp["code"] != 200:
                raise PaasAgentException(resp["msg"])

            return resp.get("data")
