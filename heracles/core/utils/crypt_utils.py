import json
import base64
import async<PERSON>
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
from heracles.core.config import get_env_var
from heracles.core.exceptions import AgentRunException

# 从环境变量获取支付密钥
payment_auth_key = get_env_var('PAYMENT_KEY_OPTIONAL')


def encrypt_gcm(
    plaintext: str,
    key: str,
) -> str:
    """
    使用指定的密钥对数据进行AES-GCM加密

    Args:
        plaintext (str): 加密明文
        key (str): base64格式的AES加密密钥

    Returns:
        str: 加密密文
    """
    nonce = get_random_bytes(12)
    data_bytes = plaintext.encode('utf-8')
    key_bytes = key.encode('utf-8')

    cipher = AES.new(key_bytes, AES.MODE_GCM, nonce)
    ciphertext, tag = cipher.encrypt_and_digest(data_bytes)
    json_key = ['nonce', 'ciphertext', 'tag']
    json_value = [base64.b64encode(x).decode('utf-8') for x in (nonce, ciphertext, tag)]
    result = json.dumps(dict(zip(json_key, json_value)), sort_keys=True)
    return result


def decrypt_gcm(encrypted_content: str, key: str) -> str:
    """
    使用指定的密钥对数据进行AES-GCM解密

    Args:
        encrypted_content (str): 字符串格式的待解密数据
        key (str): base64格式的AES加密密钥

    Returns:
        str | None: 解密后的数据, None表示解密失败
    """

    try:
        json_dict = json.loads(encrypted_content)
        json_key = ['nonce', 'ciphertext', 'tag']
        decoded_json_dict = {k: base64.b64decode(json_dict[k]) for k in json_key}
        key_bytes = key.encode('utf-8')

        cipher = AES.new(key_bytes, AES.MODE_GCM, nonce=decoded_json_dict['nonce'])
        decrypted = cipher.decrypt_and_verify(decoded_json_dict['ciphertext'], decoded_json_dict['tag']).decode('utf-8')
        return decrypted

    except (ValueError, KeyError) as e:
        raise AgentRunException('Data integrity check failed') from e


async def async_encrypt_content(
    plaintext: str,
    key: str,
) -> str:
    return await asyncio.to_thread(encrypt_gcm, plaintext, key)


async def async_decrypt_content(encrypted_content: str, key: str) -> str:
    return await asyncio.to_thread(decrypt_gcm, encrypted_content, key)
