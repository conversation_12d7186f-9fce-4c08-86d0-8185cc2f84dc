import sys
import json
import logging
from datetime import datetime

from loguru import logger

from .config import get_env_var


def get_log_level():
    log_level_str = get_env_var('LOG_LEVEL_OPTIONAL', 'DEBUG').upper()

    log_levels = {
        'CRITICAL': logging.CRITICAL,
        'ERROR': logging.ERROR,
        'WARNING': logging.WARNING,
        'INFO': logging.INFO,
        'DEBUG': logging.DEBUG,
        'NOTSET': logging.NOTSET,
    }

    level = log_levels.get(log_level_str)
    if not level:
        print('Error log level config, using DEBUG instead')
        level = logging.DEBUG

    return level


class InterceptHandler(logging.Handler):
    def emit(self, record):
        # 获取对应的 Loguru 级别
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # 找到调用来源
        frame, depth = sys._getframe(6), 6
        while frame and frame.f_code.co_filename == logging.__file__:
            if frame.f_back is None:
                break

            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def get_max_message_length() -> int:
    return int(get_env_var("LOG_RECODE_MAX_LENGTH_OPTIONAL", 256))


def serialize(record):
    # 将时间转换为 ISO 8601 格式
    timestamp = datetime.fromtimestamp(record["time"].timestamp()).isoformat()
    file = f"{record['file'].path}:{record['line']}"

    max_length = get_max_message_length()

    message = record["message"]
    if len(message) > max_length:
        message = message[:max_length] + f"... (truncated, original length: {len(message)})"

    # 创建基础日志结构
    subset = {
        "timestamp": timestamp,  # ISO 8601 格式时间
        "level": record["level"].name,
        "service": "ai-agent",
        "file": file,
        "message": message,
    }

    # 合并额外字段
    if 'extra' in record:
        subset.update(record['extra'])

    return json.dumps(subset, ensure_ascii=False)


def patching(record):
    record["extra"]["serialized"] = serialize(record)


def setup_logger(logger):
    # 移除所有默认处理器
    logger.remove()

    # 配置logger
    logger = logger.patch(patching)

    log_file = get_env_var("LOG_FILE_OPTIONAL", None)
    if log_file:
        logger.add(log_file, rotation="200 MB", compression="zip", retention=5, encoding="utf-8",
                   enqueue=True,
                   format="{extra[serialized]}", level=get_log_level())
    else:
        logger.add(sys.stdout,
                   colorize=True,
                   format="<green>{time:MM-DD HH:mm:ss.SSS}</green> <level>{level} {file}:{line} -> {message}</level>",
                   level=get_log_level())

    # 使用环境变量控制是否接管标准库日志，默认不接管
    intercept_stdlib = get_env_var("LOG_INTERCEPT_STDLIB_OPTIONAL", False)

    if intercept_stdlib:
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        root_logger.setLevel(get_log_level())
        root_logger.addHandler(InterceptHandler())

    return logger


# 安装日志
logger = setup_logger(logger)


def get_logger():
    return logger


def get_playground_logger(playground_id):
    return get_logger().bind(playground_id=playground_id)


heracles_logger = get_logger()
heracles_logger.info("AIAgent is starting, current log_level is " + logging.getLevelName(get_log_level()))
