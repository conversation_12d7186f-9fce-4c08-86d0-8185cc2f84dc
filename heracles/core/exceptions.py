# === 用户自身操作引发的异常树
class UserException(Exception):
    """ Root Exception: 用户本身触发错误
    """
    def __init__(self, message):
        super().__init__(message)


class UserEventArgumentException(UserException):
    """ 请求参数错误
    """
    def __init__(self, message):
        super().__init__(message)


class UserTaskStatusException(UserException):
    """ 任务执行时, 状态错误
    """
    def __init__(self, message):
        super().__init__(message)

class UserTaskAppendLimitException(UserException):
    """ 任务执行时, 追加任务达到上限
    """
    def __init__(self, message):
        super().__init__(message)


# === end of UserException ===

# === begin of AgentException ===

class AgentException(Exception):
    """ Root Exception: Agent 运行过程的异常
    """
    def __init__(self, message):
        super().__init__(message)


class AgentRunException(AgentException):
    """ Agent runtime 错误, Agent 通用错误类型
    """
    def __init__(self, message):
        super().__init__(message)


class AgentLLMException(AgentException):
    """ 大模型错误
    """
    def __init__(self, message):
        super().__init__(message)


class AgentLLMConfigException(AgentException):
    """ 配置错误
    """
    def __init__(self, message):
        super().__init__(message)


class AgentLLMCallParamsException(AgentException):
    """ 调用参数错误
    """
    def __init__(self, message):
        super().__init__(message)

class AgentLLMCallResultException(AgentException):
    """ 调用结果错误
    """
    def __init__(self, message):
        super().__init__(message)

class AgentLLMJSONException(AgentLLMException):
    """ 大模型参数解析错误
    """
    def __init__(self, message):
        super().__init__(message)

# === end of AgentException ===

class IDEServerException(Exception):
    """ Root Exception: 与 IDEServer 操作相关的错误
    """
    def __init__(self, message):
        super().__init__(message)


class IDEServerConnectException(IDEServerException):
    """ 连接异常
    """
    def __init__(self, message):
        super().__init__(message)


class IDEServerFunCallException(IDEServerException):
    """ func_call error
    """
    def __init__(self, message):
        super().__init__(message)

class IDEServerFunCallTimeoutException(IDEServerFunCallException):
    """ func_call timeout error
    """
    def __init__(self, message):
        super().__init__(message)

class IDEServerFunCallFailureException(IDEServerFunCallException):
    """ func_call 命令运行失败
    """
    def __init__(self, message):
        super().__init__(message)

class IDEServerFileNotFoundError(IDEServerException):
    """ 路径文件/目录不存在
    """
    def __init__(self, message):
        super().__init__(message)

class IDEServerFileBinaryError(IDEServerException):
    """ 路径文件为二进制文件或过大
    """
    def __init__(self, message):
        super().__init__(message)

class PaasAgentException(AgentException):
    """ Root Exception: 与 PaasAgent 操作相关的错误
    """
    def __init__(self, message):
        super().__init__(message)

class LintException(PaasAgentException):
    def __init__(self, message):
        super().__init__(message)


class BrowserException(PaasAgentException):
    """ Root Exception: 与 Browser 操作相关的错误
    """
    def __init__(self, message):
        super().__init__(message)
