'use server';

import { z } from 'zod';

import { v4 } from "uuid";
import { PlaybookFormState } from '@/app/utils/definitions'
import { redirect } from 'next/navigation';
import { revalidatePath } from 'next/cache';

const playbook_backend_url = process.env['PLAYBOOK_BACKEND_URL']

const PlaybookFormSchema = z.object({
  id: z.string(),
  title: z.string({
    invalid_type_error: 'Please input title.',
  }).trim().min(3),
  description: z.optional(z.string()),
  content: z.string({ invalid_type_error: 'Please input content.' }).trim().min(3),
  tags: z.optional(z.array(z.string())),
  status: z.enum(["inited", "processing", "not_applicable", "ready", "pending"])
});
const UpdatePlaybook = PlaybookFormSchema.omit({ id: true });

const ITEMS_PER_PAGE = 50;


export async function fetchCollections() {
  const response = await fetch(playbook_backend_url + '/collections', {
    method: 'GET',
  });
  const result = await response.json();
  const collectionNames = result.data?.collections?.map((collection: any) => collection.name);
  return collectionNames;
}

export async function fetchPlaybooksPages() {
  const response = await fetch(playbook_backend_url + '/playbooks/count', {
    method: 'GET',
  });
  const result = await response.json();
  const count = result?.data?.count;
  const totalPages = Math.ceil(count / ITEMS_PER_PAGE);
  return totalPages;
}

export async function fetchPlaybooks(
  { query,
    currentPage }: {
      query: string
      currentPage: number
    }
) {
  const offset = (currentPage - 1) * ITEMS_PER_PAGE;
  const params = { page_count: String(ITEMS_PER_PAGE), page: String(currentPage) };
  const queryString = new URLSearchParams(params).toString();
  const response = await fetch(playbook_backend_url + '/playbooks' + `?${queryString}`, {
    method: 'GET',
  });
  const result = await response.json();
  const { playbooks, next_page_offset } = result.data
  return playbooks;
}

export async function fetchPlaybookById(id: string) {
  const response = await fetch(playbook_backend_url + '/playbooks' + `/${id}`, {
    method: 'GET',
  });
  const result = await response.json();
  const playbook = result.data
  return playbook;
}

export async function updatePlaybook(
  id: string | number,
  prevState: PlaybookFormState,
  playbookFormData: FormData
) {
  const validatedFields = UpdatePlaybook.safeParse({
    title: playbookFormData.get('title'),
    content: playbookFormData.get('content'),
    description: playbookFormData.get('description'),
    tags: playbookFormData.getAll('tags'),
    status: playbookFormData.get('status'),
  });

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: 'Missing Fields. Failed to Update Playbook.',
    };
  }
  const { title, content, description, tags, status } = validatedFields.data;

  const playbook = {
    "id": id,
    "title": title,
    "original_content": content,
    "description": description ? description : "",
    "tags": tags ? tags : [],
    "source": "user",
    "status": status ? status : "pending"
  }
  const response = await fetch(playbook_backend_url + '/playbooks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(playbook)
  });
  const result = await response.json();
  revalidatePath('/playbooks');
  redirect('/playbooks');
}

export async function createPlaybook(
  prevState: PlaybookFormState,
  playbookFormData: FormData
) {
  const id = v4();
  return updatePlaybook(id, prevState, playbookFormData);
}

export async function deletePlaybook(id: string | number) {
  const response = await fetch(playbook_backend_url + '/playbooks' + `/${id}`, {
    method: 'DELETE',
  });
  const result = await response.json();
  revalidatePath('/playbooks');
}
