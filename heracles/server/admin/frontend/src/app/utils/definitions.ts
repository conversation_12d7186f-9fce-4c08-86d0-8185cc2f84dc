import { Schemas } from "@qdrant/js-client-rest"


export type Playbook = {
  id: string | number;
  title: string;
  original_content: string;
  description: string;
  tags: string[];
  source: string;
  status: "ready" | "pending";
};

export type PlaybookFormState = {
  errors?: {
    title?: string[];
    content?: string[];
    description?: string[];
    tags?: string[];
    status?: string[];
  };
  message?: string | null;
};


export type Point = Schemas["PointStruct"];
