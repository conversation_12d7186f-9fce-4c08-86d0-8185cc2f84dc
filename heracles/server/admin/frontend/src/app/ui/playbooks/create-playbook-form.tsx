'use client';

import { PlaybookFormState } from '@/app/utils/definitions';
import Link from 'next/link';
import { useActionState } from 'react';
import { createPlaybook } from '@/app/utils/data';
import { Button } from '@/app/ui/button';
import { useState, KeyboardEvent } from 'react';



export default function CreatePlaybookForm() {
  const initialState: PlaybookFormState = { message: null, errors: {} };
  const updatePlaybookWithId = createPlaybook.bind(null);
  const [state, formAction] = useActionState(updatePlaybookWithId, initialState);

  const [title, setTitle] = useState<string>("");
  const [content, setContent] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [tags, setTags] = useState<string[]>([]);
  const [status, set_status] = useState<string>("");

  function handleKeyDownTag(event: KeyboardEvent) {
    if (event.key !== 'Enter') return
    const target = event.target as HTMLInputElement
    const value = target.value
    if (!value.trim()) return
    setTags([...tags, value])
    target.value = ''
  }

  function removeTag(index: number) {
    setTags(tags.filter((el, i) => i !== index))
  }

  return (
    <form action={formAction} onKeyDown={(e) => { e.key === 'Enter' && e.preventDefault(); }}>
      <div className="rounded-md bg-gray-50 p-4 md:p-6">
        <div className="mb-4">
          <label htmlFor="title" className="mb-2 block text-sm font-medium">
            title
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="title"
                name="title"
                type="string"
                value={title}
                onChange={(e) => { setTitle(e.target.value); }}
                className="peer block w-full rounded-md border border-gray-200 py-2 pl-10 text-sm outline-2 placeholder:text-gray-500"
              />
            </div>
          </div>
          <div id="title-error" aria-live="polite" aria-atomic="true">
            {state.errors?.title &&
              state.errors.title.map((error: string) => (
                <p className="mt-2 text-sm text-red-500" key={error}>
                  {error}
                </p>
              ))}
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="description" className="mb-2 block text-sm font-medium">
            description
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <input
                id="description"
                name="description"
                type="string"
                value={description}
                onChange={(e) => { setDescription(e.target.value) }}
                className="peer block w-full rounded-md border border-gray-200 py-2 pl-10 text-sm outline-2 placeholder:text-gray-500"
              />
            </div>
          </div>
          <div id="description-error" aria-live="polite" aria-atomic="true">
            {state.errors?.description &&
              state.errors.description.map((error: string) => (
                <p className="mt-2 text-sm text-red-500" key={error}>
                  {error}
                </p>
              ))}
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="content" className="mb-2 block text-sm font-medium">
            content
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <textarea
                id="content"
                name="content"
                // type="string"
                value={content}
                onChange={(e) => { setContent(e.target.value) }}
                className="peer block w-full rounded-md border border-gray-200 py-2 pl-10 text-sm outline-2 placeholder:text-gray-500"
              />
            </div>
          </div>
          <div id="content-error" aria-live="polite" aria-atomic="true">
            {state.errors?.content &&
              state.errors.content.map((error: string) => (
                <p className="mt-2 text-sm text-red-500" key={error}>
                  {error}
                </p>
              ))}
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="tags" className="mb-2 block text-sm font-medium">
            tags
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <div className="flex flex-wrap items-center gap-2">
                {tags.map((tag, index) => (
                  <div className="bg-slate-300 inline-block rounded px-2" key={index}>
                    <span className="text">{tag}</span>
                    <span className="ml-2 bg-black text-white inline-flex justify-center rounded-full items-center cursor-pointer" onClick={() => removeTag(index)}>&times;</span>
                    <input type="hidden" id="tags" name="tags" value={tag} ></input>
                  </div>
                ))}
                <input className="peer block w-full rounded-md border border-gray-200 py-2 pl-10 text-sm outline-2" type="text" onKeyDown={handleKeyDownTag} placeholder="Input somthing and press enter" />
              </div>
            </div>
          </div>
          <div id="tags-error" aria-live="polite" aria-atomic="true">
            {state.errors?.tags &&
              state.errors.tags.map((error: string) => (
                <p className="mt-2 text-sm text-red-500" key={error}>
                  {error}
                </p>
              ))}
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="status" className="mb-2 block text-sm font-medium">
            status
          </label>
          <div className="relative mt-2 rounded-md">
            <div className="relative">
              <select
                id="status"
                name="status"
                value={status}
                onChange={(e) => { set_status(e.target.value); }}
                className="peer block w-full rounded-md border border-gray-200 py-2 pl-10 text-sm outline-2 placeholder:text-gray-500"
              >
                <option value="ready">ready</option>
                <option value="pending">pending</option>
              </select>
            </div>
          </div>
          <div id="status-error" aria-live="polite" aria-atomic="true">
            {state.errors?.status &&
              state.errors.status.map((error: string) => (
                <p className="mt-2 text-sm text-red-500" key={error}>
                  {error}
                </p>
              ))}
          </div>
        </div>

      </div>
      <div className="mt-6 flex justify-end gap-4">
        <Link
          href="/playbooks"
          className="flex h-10 items-center rounded-lg bg-gray-100 px-4 text-sm font-medium text-gray-600 transition-colors hover:bg-gray-200"
        >
          Cancel
        </Link>
        <Button type="submit">Summit</Button>
      </div>
    </form>
  );
}
