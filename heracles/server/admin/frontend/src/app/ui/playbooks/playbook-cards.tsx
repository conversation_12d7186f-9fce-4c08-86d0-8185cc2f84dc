import { fetchPlaybooks } from "@/app/utils/data";
import { Playbook } from "@/app/utils/definitions";
import { UpdatePlaybook, DeletePlaybook } from "@/app/ui/playbooks/button"



export async function PlaybookCards({
  query,
  currentPage,
}: {
  query: string;
  currentPage: number;
}) {
  const playbooks: Playbook[] = await fetchPlaybooks({ query: query, currentPage: currentPage });
  return (
    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
      {playbooks?.map((playbook, index) => (
        <PlaybookCard key={index} playbook={playbook}></PlaybookCard>
      ))}
    </div>
  );
}


export async function PlaybookCard({ playbook }: { playbook: Playbook }) {
  return (
    <div className="block h-auto w-50 h-50 rounded-lg p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700">
      <h1 className="truncate text-left mb-2 text-sm font-bold tracking-tight text-gray-900 dark:text-white">
        {playbook.title.length > 30 ? playbook.title.substring(0, 27) + '...' : playbook.title}
      </h1>
      <p className="truncate text-left text-xs text-gray-00 dark:text-gray-400">
        {playbook.original_content.length > 100 ? playbook.original_content.substring(0, 97) + '...' : playbook.original_content}
      </p>
      <UpdatePlaybook id={playbook.id}></UpdatePlaybook>
      <DeletePlaybook id={playbook.id}></DeletePlaybook>
    </div>
  );
}
