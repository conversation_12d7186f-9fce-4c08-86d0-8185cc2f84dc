import { PlaybookCards } from "@/app/ui/playbooks/playbook-cards";
import Pagination from "@/app/ui/playbooks/pagination"
import { fetchPlaybooksPages } from "@/app/utils/data";
import { CreatePlaybook } from "@/app/ui/playbooks/button"


export default async function Page(props: {
  searchParams?: Promise<{
    query?: string;
    page?: string;
  }>;
}) {
  const searchParams = await props.searchParams;
  const query = searchParams?.query || '';
  const currentPage = Number(searchParams?.page) || 1;
  const totalPages = await fetchPlaybooksPages();

  return (
    <div>
      <h1 className="mb-4 text-xl md:text-2xl">
        Playbooks
      </h1>
      <div className="mt-4 flex items-center justify-between gap-2 md:mt-8">
        <CreatePlaybook />
      </div>
      <PlaybookCards query={query} currentPage={currentPage}></PlaybookCards>
      <div className="mt-5 flex w-full justify-center">
        <Pagination totalPages={totalPages} />
      </div>
    </div>
  );
}
