import EditPlaybookForm from '@/app/ui/playbooks/edit-playbook-form';
import Breadcrumbs from '@/app/ui/playbooks/breadcrumbs';
import { fetchPlaybookById } from '@/app/utils/data';
import { notFound } from 'next/navigation';


export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const id = params.id;
  const playbook = await fetchPlaybookById(id);

  if (!playbook) {
    notFound();
  }

  return (
    <main>
      <Breadcrumbs
        breadcrumbs={[
          { label: 'Playbooks', href: '/playbooks' },
          {
            label: 'Edit Playbook',
            href: `/playbooks/${id}/edit`,
            active: true,
          },
        ]}
      />
      <EditPlaybookForm playbook={playbook} />
    </main>
  );
}
