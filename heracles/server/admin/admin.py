import os
from fastapi import <PERSON><PERSON><PERSON>
from qdrant_client import models
from heracles.core.logger import heracles_logger as logger
from heracles.core.schema import AdminPlaybookModel, AdminPlaybookQuery, AdminDataResponse
from heracles.core.utils.admin_utils import (
    record_to_playbook,
    playbook_to_point,
    create_dense_embedding,
    create_sparse_embedding,
)
from .config import get_settings, get_vector_db_client, get_sparse_model

settings = get_settings()
vector_db_client = get_vector_db_client()


async def load_model_and_db():
    get_sparse_model()
    existed = await vector_db_client.collection_exists(collection_name='playbooks')
    if existed:
        logger.info('Admin: playbook collection already existed, skip creating')
        return
    logger.info('Admin: playbook collection not exists, create playbook collection')
    collection_name = 'playbooks'
    await vector_db_client.recreate_collection(
        collection_name=collection_name,
        vectors_config={
            settings.vector_db_dense_vector_name: models.VectorParams(
                size=settings.vector_db_dense_dim,
                distance=models.Distance.COSINE,
            )
        },
        sparse_vectors_config={
            settings.vector_db_sparse_vector_name: models.SparseVectorParams(
                modifier=models.Modifier.IDF,
            )
        },
    )
    playbook_samples_path = 'heracles/server/admin/playbook_samples'
    if not os.path.exists(playbook_samples_path):
        logger.info('Admin: playbook samples folder not exists, skip loading')
        return
    logger.info('Admin: read playbooks from samples folder')
    points = []
    for file in os.listdir(playbook_samples_path):
        if not file.endswith('.json'):
            continue
        with open(os.path.join(playbook_samples_path, file), 'r') as f:
            playbook = AdminPlaybookModel.model_validate_json(f.read())
            points.append(await playbook_to_point(playbook))
    logger.info('Admin: upload sample playbook to database')
    await vector_db_client.upsert(collection_name, points=points)


app = FastAPI()


@app.get('/collections', response_model=AdminDataResponse)
async def fetch_collections():
    logger.info('Admin: get all collections')
    res = await vector_db_client.get_collections()
    return AdminDataResponse(status='ok', statusCode=200, error_msg='', data=res)


@app.get('/{collection_name}', response_model=AdminDataResponse)
async def fetch_playbooks(collection_name: str, page_count: int, page: int | None = None):
    logger.info(f'Admin: collection {collection_name} get playbooks at page {page}, page_count {page_count}')
    page = page or 1
    start_offset_idx = (page - 1) * page_count
    records, _ = await vector_db_client.scroll(
        collection_name=collection_name, limit=start_offset_idx + 1, with_payload=False, with_vectors=False
    )
    start_offset = records[-1].id
    records, next_page_offset = await vector_db_client.scroll(
        collection_name=collection_name,
        limit=page_count,
        offset=start_offset,
        with_payload=True,
        with_vectors=False,
    )
    playbooks = list(map(record_to_playbook, records))
    data = {'playbooks': playbooks, 'next_page_offset': next_page_offset}
    return AdminDataResponse(status='ok', statusCode=200, error_msg='', data=data)


@app.post('/{collection_name}', response_model=AdminDataResponse)
async def create_playbook(collection_name: str, playbook: AdminPlaybookModel):
    logger.info(f'Admin: collection {collection_name} create playbook {playbook}')
    point = await playbook_to_point(playbook)
    await vector_db_client.upsert(collection_name, points=[point])
    return AdminDataResponse(status='ok', statusCode=200, error_msg='')


@app.delete('/{collection_name}/{playbook_id}')
async def delete_playbook(collection_name: str, playbook_id: str):
    logger.info(f'Admin: collection {collection_name} delete playbook {playbook_id}')
    await vector_db_client.delete(collection_name, points_selector=[playbook_id])
    return AdminDataResponse(status='ok', statusCode=200, error_msg='')


@app.get('/{collection_name}/count')
async def get_collection_count(collection_name: str):
    logger.info(f'Admin: get collection {collection_name} count')
    result = await vector_db_client.count(collection_name)
    return AdminDataResponse(status='ok', statusCode=200, error_msg='', data=result)


@app.post('/{collection_name}/query')
async def query_playbook(collection_name: str, query: AdminPlaybookQuery):
    logger.info(f'Admin: collection {collection_name} query {query}')
    dense_embedding = await create_dense_embedding(query.text)
    sparse_embedding = await create_sparse_embedding(query.text)
    sparse_vector_query_limit = 20
    dense_vector_query_limit = 20
    result_limit = 10
    must_condition: list[models.Condition] = []
    if query.tags:
        must_condition.append(
            models.FieldCondition(
                key='tags',
                match=models.MatchAny(any=query.tags),
            ),
        )
    must_condition.append(
        models.FieldCondition(
            key='status',
            match=models.MatchValue(value=query.status),
        )
    )
    filter = models.Filter(must=must_condition)
    res = await vector_db_client.query_points(
        collection_name=collection_name,
        prefetch=[
            models.Prefetch(
                query=sparse_embedding.as_object(),
                using=settings.vector_db_sparse_vector_name,
                limit=sparse_vector_query_limit,
            ),
            models.Prefetch(
                query=dense_embedding,
                using=settings.vector_db_dense_vector_name,
                limit=dense_vector_query_limit,
            ),
        ],
        query_filter=filter,
        query=models.FusionQuery(fusion=models.Fusion.RRF),
        limit=result_limit,
    )
    playbooks = []
    for point in res.points:
        if point.score < 0.75:
            continue
        payload = point.payload
        payload['id'] = point.id
        playbooks.append(AdminPlaybookModel.model_validate(payload))
    return AdminDataResponse(status='ok', statusCode=200, error_msg='', data=playbooks)


@app.get('/{collection_name}/{playbook_id}', response_model=AdminDataResponse)
async def get_playbook(collection_name: str, playbook_id: str):
    logger.info(f'Admin: collection {collection_name} get playbook {playbook_id}')
    records = await vector_db_client.retrieve(collection_name, ids=[playbook_id])
    playbook = record_to_playbook(records[0])
    return AdminDataResponse(status='ok', statusCode=200, error_msg='', data=playbook)
