import os
from functools import lru_cache
from qdrant_client import Async<PERSON><PERSON><PERSON><PERSON><PERSON>
from fastembed import SparseTextEmbedding
from heracles.core.config import get_env_var


class Settings:
    app_name: str = 'Clacky Admin'
    vector_db_host: str = get_env_var('ADMIN_VECTOR_DB_HOST_OPTIONAL')
    vector_db_port: int = get_env_var('ADMIN_VECTOR_DB_PORT_OPTIONAL')
    vector_db_sparse_vector_name: str = get_env_var('ADMIN_VECTOR_DB_SPARSE_VECTOR_NAME_OPTIONAL', 'bm25')
    vector_db_dense_vector_name: str = get_env_var('ADMIN_VECTOR_DB_DENSE_VECTOR_NAME_OPTIONAL', 'text-embedding-3-small')
    vector_db_dense_dim: int = get_env_var('ADMIN_VECTOR_DB_DENSE_DIM_OPTIONAL', 1536)
    sparse_embedding_model: str = get_env_var('ADMIN_SPARSE_EMBEDDING_MODEL_OPTIONAL', 'Qdrant/bm25')
    dense_embedding_model: str = get_env_var('ADMIN_DENSE_EMBEDDING_MODEL_OPTIONAL', 'text-embedding-3-small')
    vector_db_api_key: str = get_env_var('ADMIN_VECTOR_DB_API_KEY_OPTIONAL')
    llm_base_url: str = get_env_var('LLM_BASE_URL')
    llm_api_key: str = get_env_var('LLM_API_KEY')


@lru_cache
def get_settings():
    return Settings()


@lru_cache
def get_sparse_model():
    cache_dir = './fastembed_model'
    if os.path.isdir(cache_dir):
        return SparseTextEmbedding('Qdrant/bm25', cache_dir=cache_dir, local_files_only=True)
    else:
        return SparseTextEmbedding('Qdrant/bm25', cache_dir=cache_dir, local_files_only=False)


@lru_cache
def get_vector_db_client():
    settings = get_settings()
    return AsyncQdrantClient(url=settings.vector_db_host, port=settings.vector_db_port, api_key=settings.vector_db_api_key)
