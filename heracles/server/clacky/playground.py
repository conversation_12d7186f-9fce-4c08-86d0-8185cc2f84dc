import asyncio
import time
import json

from heracles.core.config import get_env_var
from heracles.core.utils import wait_for
from heracles.core.logger import get_playground_logger, heracles_logger as sys_logger
from heracles.core.schema import PlaygroundStatusType, ErrorHandlerMessage
from heracles.core.schema.task import Task
from heracles.core.schema.task_state import TaskState
from heracles.core.utils.redis_cache import redis_cache
from heracles.agent_controller.agent_role_controller import Agent<PERSON><PERSON><PERSON><PERSON>roller
from heracles.core.exceptions import (
    AgentRunException,
    IDEServerConnectException,
    IDEServerException,
    IDEServerFunCallException,
    UserEventArgumentException,
)
from .ide_server_client import IdeServerClient


class Playground:

    def __init__(self, playground_id, socketio_server, project_id=None, is_root=False):
        self.project_id = project_id
        self.playground_id = playground_id
        self.socketio_server = socketio_server
        self.is_root = is_root
        self.room_name = f'room_{playground_id}'
        self.ide_server_client = IdeServerClient(self)
        self.agent_controller = AgentRoleController(self)
        self.logger = get_playground_logger(playground_id)
        self.playground_status: PlaygroundStatusType = PlaygroundStatusType.DRAFTED
        self.playground_reason = ''
        self.channel_online_count = 0
        self.channel_start_time = None
        self.channel_leave_time = None
        self.channel_create_time = time.time()
        self._lock = asyncio.Lock()

    async def on_user_event(self, event_name, *args, **kwargs):
        target_method = getattr(self.agent_controller, f'on_{event_name}', None)
        if not target_method:
            raise UserEventArgumentException(f'on_user_event fail: no such event `{event_name}` in agent_controller')
        return await target_method(*args, **kwargs)

    def is_accepted_message(self):
        return self.agent_controller.waiting_user_input

    async def trigger_workspace_analyze_items_updated(self, workspace_analyze_items):
        """ 事件触发: 更新 workspace 状态
        :param workspace_analyze_item: 待补充
        """
        await self.broadcast_all('workspaceAnalyzeItemsUpdated', workspace_analyze_items)

    async def trigger_add_task_step_confirmation(self, task_step_dict: dict, auto_merge: bool = False):
        """ 事件触发: AI 生成新的任务步骤(json格式), 不会直接加入 Task, 需要用户确认添加
        """
        if auto_merge:
            task_step_dict['auto_merge'] = True
        await self.broadcast_all('addTaskStepConfirmation', task_step_dict)

    async def trigger_task_action_updated(self, task_action):
        """ 事件触发: 更新 task_action 状态
        """
        await self.broadcast_all('taskActionUpdated', task_action)

    async def trigger_task_updated(self, task):
        """ 事件触发: 更新完整 task 状态
        """
        await self.broadcast_all('taskUpdated', task)

    async def trigger_plan_updated(self, plan_step_dict):
        """ 事件触发: 更新 plan 状态

        :param plan_step_dict: 计划推进步骤的情况
            key(think/retrieve/generate): 三种情况
                status: 状态, 跟 ActionStatus 一致
                result: 该步骤的推进结果
        """
        await self.broadcast_all('planUpdated', plan_step_dict)

    async def trigger_task_state_updated(self, task_state_type: str):
        await self.broadcast_all('taskStateUpdated', task_state_type)

    async def trigger_task_planned(self, task):
        """ 事件触发: 计划制定完成, 返回 task
        :param task: 输出 task
        """
        await self.broadcast_all('taskPlanned', task)

    async def trigger_analyze_project_environment_progress(self, progress):
        """ 事件触发: 工具调用
        """
        await self.broadcast_all('analyzeProjectEnvironmentProgress', progress)

    async def trigger_spec_updated(self, spec):
        """ 事件触发: 输出 Spec 规格
        :param spec: Spec
        """
        await self.broadcast_all('specUpdated', spec)

    async def trigger_project_environment_result_updated(self, res):
        """ 事件触发: 更新项目环境分析结果
        """
        await self.broadcast_all('projectEnvironmentResultUpdated', res)

    async def trigger_tool_call_status_updated(self, tool_update_info: dict):
        """ 事件触发: 更新 tool_call 状态
        """
        await self.broadcast_all('toolCallStatusUpdated', tool_update_info)

    async def trigger_error_handler(self, error_handler_message: ErrorHandlerMessage):
        """ 事件触发: 系统遇到未预料错误, 发给客户端便于排查问题
        """
        message_dict = error_handler_message.dict()
        await self.broadcast_all('errorHandler', message_dict)

    async def trigger_chunk_message(self, word: str):
        """ 事件触发: Chunk 消息处理, 快速将消息提前发送
        """
        await self.broadcast_all('chunkMessage', word)

    async def trigger_message(self, message: str):
        """ 事件触发: 消息通道
        """
        await self.broadcast_all('message', message)

    async def trigger_smart_detect_status_updated(self, status: dict):
        """ 事件触发: 智能检测状态更新
        """
        await self.broadcast_all('smartDetectStatusUpdated', status)

    async def trigger_message_suggestion(self, message: str):
        """ 事件触发: 给到用户的消息建议，用户点击后前端直接发出 message
        """
        await self.broadcast_all('messageSuggestion', message)

    async def trigger_smart_detect_errors_cleared(self):
        """ 事件触发: 智能检测错误清除
        """
        await self.broadcast_all('smartDetectErrorsCleared', {})

    async def trigger_auto_fix_status_updated(self, data: dict):
        """事件触发: 自动修复状态更新"""
        await self.broadcast_all('autoFixStatusUpdated', data)

    async def trigger_project_errors_message(self, data: dict):
        """ 事件触发: 项目错误消息格式
        """
        await self.broadcast_all('projectErrorsMessage', data)

    async def trigger_agent_run_project(self, message: str):
        """ 事件触发: 任务运行
        """
        await self.broadcast_all('agentRunProject', {'message': message})

    async def trigger_project_draft_updated(self, project_draft: str):
        """ 事件触发: projectDraftUpdated
        """
        await self.broadcast_all('projectDraftUpdated', project_draft)

    def set_playground_status(self, new_status: PlaygroundStatusType, reason=None):
        """设定 playground 的状态, 这是一个核心函数, 只有 playground_status 为 ok 才是正常可用状态
        """
        self.logger.debug(f'set new status: {new_status}, {reason}')
        self.playground_status = new_status
        if reason:
            self.playground_reason = reason
        else:
            self.playground_reason = ''

    def get_playground_info(self):
        """ 拉取 playground 状态, 每次初始化链接时调用
        """
        res = {
            'playgroundStatus': self.playground_status,
            'isRoot': self.is_root,
            'workspaceStatus': self.agent_controller.workspace.detail_status,
            'taskState': self.agent_controller.task_state.state,
            'runStatus': self.ide_server_client.run_status,
            'task': self.get_task_dict()
        }
        if self.playground_status != PlaygroundStatusType.OK:  # pragma: no cover
            res['playgroundReason'] = self.playground_reason
        return res

    def get_task_dict(self, full=False):
        """ 获取 task 字典
        :param full: 是否是全部属性, 默认只取为前端准备的内容
        """
        task = self.agent_controller.task
        if task:
            task = task.dict(full=full)
        return task

    async def wait_for_ok(self, *, timeout=15, reason=''):
        """ 等待 playground 状态变为 ok
        :param timeout: 超时时间(s)
        """
        self.logger.debug(f'wait_for_ok called, {reason}')

        def cond_callback():
            return self.playground_status == PlaygroundStatusType.OK
        try:
            await wait_for(cond_callback, timeout)
        except AgentRunException:  # pragma: no cover
            raise IDEServerConnectException(f'wait_for_ok timeout({timeout}s): current status is `{self.playground_status}`') from None

    async def start(self):
        async with self._lock:
            if self.playground_status == PlaygroundStatusType.OK:
                self.logger.info('playground is connected')
                return

            await self.connect_ide_server()

            # 如果是中途退出的 task，我们自动重起任务
            await self.recovery_task()

    async def connect_ide_server(self):
        try:
            if self.playground_status != PlaygroundStatusType.CONNECTING:
                self.set_playground_status(PlaygroundStatusType.CONNECTING)
                start_time = time.time()
                await self.ide_server_client.connect()
                execution_time = time.time() - start_time
                self.logger.info(f"connect_ide_server cost time is `{execution_time:.2f}s`")
            else:
                await self.wait_for_ok(reason='connect_ide_server')
        except IDEServerException as e:
            self.set_playground_status(PlaygroundStatusType.CONNECT_FAILED, str(e))
            raise e
        except Exception as e:
            self.set_playground_status(PlaygroundStatusType.CONNECT_FAILED, str(e))
            raise e

    async def disconnect_ide_server(self):
        """清理和释放使用
        """
        await self.ide_server_client.disconnect()

    def on_ide_event(self, event_name, handler):
        self.ide_server_client.event_emitter.on(event_name, handler)

    def off_ide_event(self, event_name, handler):
        self.ide_server_client.event_emitter.off(event_name, handler)

    async def func_call(self, func_name, *args, **kwargs):
        """代理所有的 func_calls 到 IDEServer 中调用
        查看 ``ide_server_client.py`` 中关于 func_call 注释的所有函数
        """
        if not func_name.startswith('agent_'):
            raise IDEServerFunCallException(f'func_call fail: `{func_name}` must begin with agent_xx')
        await self.wait_for_ok()
        if self.playground_status != PlaygroundStatusType.OK:
            raise IDEServerFunCallException(f'func_call fail: `{func_name}`, playground_status is not ok')
        target_method = getattr(self.ide_server_client, func_name, None)
        if not target_method:
            raise IDEServerFunCallException(f'func_call fail: `{func_name}` no such func_call, maybe need agent_xx')
        res = await target_method(*args, **kwargs)
        return res

    async def broadcast_all(self, event_name, data, skip_sid=None):
        if event_name not in ['chunkMessage']:
            self.logger.info(f'u<<all: {event_name}, {data}, skip_sid={skip_sid}')
        await self.socketio_server.emit(event_name, data, to=self.room_name, skip_sid=skip_sid)

    def add_channel_online_count(self):
        self.channel_online_count += 1
        self.channel_leave_time = None
        if self.channel_online_count == 1 and not self.channel_start_time:
            self.channel_start_time = time.time()
            self.logger.debug(f'[cache] save playground {self.playground_id}')
            self.save()

    def reduce_channel_online_count(self):
        self.channel_online_count -= 1
        if self.channel_online_count == 0:
            self.channel_leave_time = time.time()

    def is_channel_online(self):
        """ 判断 playground 上还有用户接入没, 是否可以响应用户消息
        """
        return self.channel_online_count >= 1

    def is_channel_leave_enough_time(self, leave_time=60):
        if self.is_channel_online():  # pragma: no cover
            return False
        if self.channel_leave_time and time.time() - self.channel_leave_time > leave_time:
            return True
        else:
            # 注: 没有上下线的这种( 一般用于测试 ) channel_start_time, channel_leave_time 为空, 返回 True
            if self.channel_start_time is None and self.channel_leave_time is None:
                return True
            else:  # pragma: no cover
                return False

    def to_dict(self):
        return {
            "playground_status": str(self.playground_status),
            'task_state': self.agent_controller.task_state.state,
            "channel_online_count": self.channel_online_count,
            "can_clear_playground": self.can_clear_playground(),
            "can_delete_playground": self.can_delete_playground(),
            "channel_start_time": self.channel_start_time,
            "is_root": self.is_root
        }

    def can_clear_playground(self):
        """ 判断是否可以清理逻辑 """
        if time.time() - self.channel_create_time < 60:
            return False
        if not self.is_channel_leave_enough_time():  # pragma: no cover
            return False
        if self.agent_controller.task_state.working.is_active:
            return False
        return True

    def can_delete_playground(self):
        days = int(get_env_var('CACHE_REDIS_DAY_OPTIONAL', 30))
        leave_time = days * 24 * 60 * 60  # 单位天
        if self.channel_start_time and time.time() - self.channel_start_time > leave_time:
            return True
        return False

    async def clear_playground(self):
        """ 清理逻辑 """
        self.logger.info("clear_playground called")
        self.agent_controller.stop_tasks()
        await self.disconnect_ide_server()

    def save(self):
        """ 保存 playground 到 redis
        """
        redis_key = self._generate_redis_key()

        data = {
            "channel_create_time": self.channel_create_time,
            "channel_start_time": self.channel_start_time
        }
        redis_cache.set(redis_key, json.dumps(data))

    def delete(self):
        redis_key = self._generate_redis_key()
        redis_cache.delete(redis_key)
        # 同时清理掉相应的 task 和 task_state
        self.agent_controller.reset_task()

    def _generate_redis_key(self):
        return self._generate_redis_key_by_id(self.playground_id)

    async def recovery_task(self):
        if self.agent_controller.task_state.working.is_active:
            self.agent_controller.run_task_with_background()

    @classmethod
    async def load_from_cache(cls, playground_id, socketio_server):
        redis_key = cls._generate_redis_key_by_id(playground_id)
        data = redis_cache.get(redis_key)
        if not data:
            return None
        data = json.loads(data)

        sys_logger.debug(f'[cache] load playground {playground_id} from cache')
        playground = Playground(playground_id, socketio_server)
        playground.channel_create_time = data['channel_create_time']
        playground.channel_start_time = data['channel_start_time']

        task = await Task.load_from_redis_async(playground_id)
        if task:
            playground.agent_controller.workspace.set_task(task)

        task_state_dict = TaskState.load_dict_from_redis(playground_id)
        if task_state_dict:
            playground.agent_controller.task_state.set_state(task_state_dict['state'])
            playground.agent_controller.task_state.set_appended_count(task_state_dict['appended_count'])
            playground.agent_controller.task_state.set_appended_expansion_turn(task_state_dict['appended_expansion_turn'])

        return playground

    @classmethod
    def clear_all(cls):
        for key in redis_cache.scan_iter(match=cls._generate_playground_delete_all_key()):
            sys_logger.debug(f'[cache] clear {key}')
            redis_cache.delete(key)

        for key in redis_cache.scan_iter(match=Task._generate_task_delete_all_key()):
            sys_logger.debug(f'[cache] clear {key}')
            redis_cache.delete(key)

        for key in redis_cache.scan_iter(match=TaskState._generate_task_state_delete_all_key()):
            sys_logger.debug(f'[cache] clear {key}')
            redis_cache.delete(key)

    @classmethod
    def show_cache(cls, playground_id: str):
        cache_dict = {}

        key = cls._generate_redis_key_by_id(playground_id)
        cache_dict[key] = redis_cache.get(key)

        for key in redis_cache.scan_iter(match=Task._generate_redis_all_key_by_id(playground_id)):
            cache_dict[key] = redis_cache.get(key)

        key = TaskState._generate_redis_key_by_id(playground_id)
        cache_dict[key] = redis_cache.get(key)

        return cache_dict

    @classmethod
    def _generate_redis_key_by_id(cls, playground_id: str):
        return f'playground:{playground_id}'

    @classmethod
    def _generate_playground_delete_all_key(cls):
        return 'playground:*'
