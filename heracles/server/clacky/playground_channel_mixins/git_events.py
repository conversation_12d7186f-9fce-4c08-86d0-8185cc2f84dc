# mypy: ignore-errors

class GitEventsMixin:
    # user git event list
    # 原则: 全部以 u_动作_目标 作为命名规范
    # 实际事件再转化为驼峰名字, 例如 u_start_task 转为 uStartTask

    # Git 相关操作事件

    async def on_u_make_commit_file_list(self):
        """ event: 获取 commit file list

        see: "docs/pr-design.md"

        """
        return await self.current_playground.on_user_event('u_make_commit_file_list')

    async def on_u_make_commit_message(self):
        """ event: 获取 commit message

        see: "docs/pr-design.md"
        """
        return await self.current_playground.on_user_event('u_make_commit_message')

    async def on_u_make_pr_file_list(self, git_upstream_branch: str):
        """ event: 获取 pr file list
        """
        return await self.current_playground.on_user_event('u_make_pr_file_list', git_upstream_branch)

    async def on_u_make_pr_message(self, git_upstream_branch: str):
        """ event: 获取 pr message
        """
        return await self.current_playground.on_user_event('u_make_pr_message', git_upstream_branch)
