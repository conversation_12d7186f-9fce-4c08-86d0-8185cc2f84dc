# mypy: ignore-errors
from typing import List
from heracles.core.utils import add_asyncio_handler
from heracles.core.utils.llm_json import loads
from heracles.core.schema import ErrorHandlerMessageType

class UserEventsMixin:
    # user event list
    # 原则: 全部以 u_动作_目标 作为命名规范
    # 实际事件再转化为驼峰名字, 例如 u_start_task 转为 uStartTask
    # 用户侧主动发起的事件处理入口

    async def on_u_make_spec(self, goal: str, goal_detail: str = ''):
        """ event: 需求分析
        """
        return await self.current_playground.on_user_event('u_make_spec', goal, goal_detail)

    async def on_u_make_plan(self, goal: str, goal_detail: str = '', proposed_list: List[str] = None):
        """ event: 制定计划并生成 Task
        """
        # 处理demo传入的问题
        if isinstance(proposed_list, str): # pragma: no cover
            proposed_list = [proposed_list]
        return await self.current_playground.on_user_event('u_make_plan', goal, goal_detail, proposed_list)

    async def on_u_init_empty_task(self, title: str, description: str = ''):
        """ event: 创建空任务
        """
        return await self.current_playground.on_user_event('u_init_empty_task', title, description)

    async def on_u_start_task(self, delay_time=3):
        """ event: 开始任务
        """
        return await self.current_playground.on_user_event('u_start_task', int(delay_time))

    async def on_u_pause_task(self):
        """ event: 暂停任务
        """
        return await self.current_playground.on_user_event('u_pause_task')

    async def on_u_resume_task(self, delay_time=3):
        """ event: 恢复暂停中的任务
        """
        return await self.current_playground.on_user_event('u_resume_task', int(delay_time))

    async def on_u_cancel_task(self):
        """ event: 终止执行中的任务
        """
        return await self.current_playground.on_user_event('u_cancel_task')

    async def on_u_reset_task(self):
        """ event: 重置计划
        """
        return await self.current_playground.on_user_event('u_reset_task')

    async def on_u_add_step(self, step_info):
        """ event: 追加步骤
        :param step_info: 步骤内容
            title: string
        """
        if isinstance(step_info, str): # pragma: no cover
            step_info = loads(step_info)
        return await self.current_playground.on_user_event('u_add_step', step_info)

    async def on_u_add_action(self, task_step_id, action_info):
        """ event: 追加动作
        :param task_step_id: 要操作的 task_step id
        :param action_info: 操作内容
            title: string
            path: string
            action: string
        """
        if isinstance(action_info, str): # pragma: no cover
            action_info = loads(action_info)
        return await self.current_playground.on_user_event('u_add_action', task_step_id, action_info)

    async def on_u_modify_step(self, task_step_id, new_title):
        """ event: 更新步骤
        """
        return await self.current_playground.on_user_event('u_modify_step', task_step_id, new_title)

    async def on_u_modify_action(self, task_action_id, action_info):
        """ event: 更新动作
        """
        if isinstance(action_info, str): # pragma: no cover
            action_info = loads(action_info)
        return await self.current_playground.on_user_event('u_modify_action', task_action_id, action_info)

    async def on_u_toggle_revert_action(self, task_action_id):
        """ event: 标记回滚/恢复action(仅变更状态)
        """
        return await self.current_playground.on_user_event('u_toggle_revert_action', task_action_id)

    async def on_u_delete_step(self, task_step_id):
        """ event: 删除步骤
        """
        return await self.current_playground.on_user_event('u_delete_step', task_step_id)

    async def on_u_delete_action(self, task_action_id):
        """ event: 删除动作
        """
        return await self.current_playground.on_user_event('u_delete_action', task_action_id)

    async def on_u_rerun_task_action(self, task_action_id):
        """ event: 重新执行失败的动作
        """
        return await self.current_playground.on_user_event('u_rerun_task_action', task_action_id)

    async def on_u_enable_smart_detect(self):
        """ event: 启用智能检测
        """
        return await self.current_playground.on_user_event('u_enable_smart_detect')

    async def on_u_disable_smart_detect(self):
        """ event: 停止智能检测
        """
        return await self.current_playground.on_user_event('u_disable_smart_detect')

    async def on_u_cmd_k(self, file_path, start_line, end_line, prompt):
        """ event: cmd_k 操作
        """
        cmd_k_task = await self.current_playground.on_user_event(
            'u_cmd_k',
            file_path,
            int(start_line),
            int(end_line),
            prompt,
            self.trigger_cmd_k,
        )
        add_asyncio_handler(
            self,
            '_asyncio_cmd_k',
            cmd_k_task,
            self.current_playground.trigger_error_handler,
            ErrorHandlerMessageType.CMDK,
            self.logger,
        )
        return

    async def on_u_code_completion(self, file_path, code_snippet):
        """ event: 代码补全
        """
        return await self.current_playground.on_user_event('u_code_completion', file_path, code_snippet)

    async def on_u_build_workspace(self, analyze_item_name='full'):
        """ 构建工作区
        """
        return await self.current_playground.on_user_event('u_build_workspace', analyze_item_name)

    async def on_u_analyze_project_environment(self):
        """ event: 分析项目环境
        """
        return await self.current_playground.on_user_event('u_analyze_project_environment')

    async def on_u_reset_message_session(self):
        """ event: 重置聊天会话
        """
        return await self.current_playground.on_user_event('u_reset_message_session')

    async def on_u_stop_message(self):
        """ event: 停止正在进行的聊天输出
        """
        return await self.current_playground.on_user_event('u_stop_message')

    async def on_u_run_cmd(self, cmd: str):
        """ event: 在终端执行命令
        """
        return await self.current_playground.on_user_event('u_run_cmd', cmd)

    async def on_u_expand_task_turn(self, sign_data=None):
        """ event: 任务步骤扩展
        """
        return await self.current_playground.on_user_event('u_expand_task_turn', sign_data)

    async def on_u_refine_prompt(self, prompt: str, scene: str):
        """ event: 润色提示词
        """
        return await self.current_playground.on_user_event('u_refine_prompt', prompt, scene)

    async def on_u_auto_fix_enable(self, enable=False):
        """ event: 启动或者关闭自动修复
        """
        return await self.current_playground.on_user_event('u_auto_fix_enable', enable)

    async def on_u_start_auto_fix(self):
        """ event: 启动自动修复
        """
        return await self.current_playground.on_user_event('u_start_auto_fix')

    async def on_u_stop_auto_fix(self):
        """ event: 停止自动修复
        """
        return await self.current_playground.on_user_event('u_stop_auto_fix')
