import asyncio

from heracles.core.utils import add_asyncio_handler, cancel_asyncio_handler
from heracles.core.logger import heracles_logger as logger
from heracles.core.exceptions import AgentRunException
from heracles.core.schema import ErrorHandlerMessageType

from .playground import Playground

class PlaygroundManager:
    def __init__(self):
        self.playground_dict = {}
        self._asyncio_cleanup_playground_task = None
        self._lock = asyncio.Lock()

    def add_playground(self, playground):
        playground_id = playground.playground_id

        if self.playground_dict.get(playground_id):
            raise AgentRunException(f"playground is already exist: {playground_id}")

        self.playground_dict[playground_id] = playground

    def remove_playground(self, playground):
        playground_id = playground.playground_id
        if self.playground_dict.get(playground_id):
            del self.playground_dict[playground_id]

    def find_by_playground_id(self, playground_id):
        return self.playground_dict.get(playground_id)

    async def find_or_load_cache_by(self, playground_id, socketio_server):
        async with self._lock:
            playground = self.find_by_playground_id(playground_id)
            if not playground:
                playground = await Playground.load_from_cache(playground_id, socketio_server)
                if playground:
                    self.add_playground(playground)
            return playground

    def to_dict(self, clear_flag=False):
        if clear_flag:
            return {id: playground.to_dict() for id, playground in self.playground_dict.items() if playground.can_clear_playground()}
        else:
            return {id: playground.to_dict() for id, playground in self.playground_dict.items()}

    async def loop_cleanup_playground_task(self, error_raise, times, interval_time):
        i = 0
        logger.info("[cleanup] loop_cleanup_playground_task start")
        while True:
            # 超出次数停止
            if times > 0 and i >= times:
                logger.info(f"[cleanup] loop_cleanup_playground_task stop when reached max times: {i}")
                break
            try:
                await self.delete_outdated_playground()
                await self.cleanup_playground_task()
            except Exception as e:
                if error_raise:
                    raise e
                else:  # pragma: no cover
                    logger.error(f'[cleanup] exception found(only log here): {e}')
            await asyncio.sleep(interval_time)
            i += 1

    async def delete_outdated_playground(self):
        processed_keys = [
            (playground_id, playground)
            for playground_id, playground in self.playground_dict.items()
            if playground.can_delete_playground()
        ]
        logger.info("[cleanup] delete_outdated_playground begin")
        for playground_id, playground in processed_keys:
            logger.info(f"[cleanup] delete playground id: {playground_id}")
            playground.delete()
            del self.playground_dict[playground_id]
        logger.info("[cleanup] delete_outdated_playground end")

    async def cleanup_playground_task(self, chunk_size=5):
        """ 异步运行清理任务, 每次运行处理 5 个, 保证占用合理的资源 """
        processed_keys = []
        keys = [
            playground_id
            for playground_id, playground in self.playground_dict.items()
            if playground.can_clear_playground()
        ][:chunk_size]
        num_keys_to_process = min(chunk_size, len(keys))

        logger.info("[cleanup] cleanup_playground task begin")
        for i in range(num_keys_to_process):
            key = keys[i]
            logger.info(f"[cleanup] cleanup playground id: {key}")
            playground = self.playground_dict[key]
            await playground.clear_playground()
            processed_keys.append(key)

        # 从当前 manager 中清理键值
        for key in processed_keys:
            del self.playground_dict[key]
        logger.info("[cleanup] cleanup_playground task done")

    def _add_asyncio_cleanup_playground_task(self, error_raise, times, interval_time):
        async def trigger_callback(message):
            if error_raise:
                raise AgentRunException(f'[cleanup] error found: {message}')
        add_asyncio_handler(
            self,
            '_asyncio_cleanup_playground_task',
            self.loop_cleanup_playground_task(error_raise, times, interval_time),
            trigger_callback,
            ErrorHandlerMessageType.CLEANUP,
            logger,
        )

    def start_cleanup_playground_task(self, error_raise=False, times=0, interval_time=60):
        """ 启动清理任务
        :param error_raise: 发生异常的处理方式
        :param times: 执行多少次停止, 测试时有用
        """
        if not self._asyncio_cleanup_playground_task:
            self._add_asyncio_cleanup_playground_task(error_raise, times, interval_time)

    def cancel_cleanup_playground_task(self):
        cancel_asyncio_handler(self, '_asyncio_cleanup_playground_task')

    def show_playground_cache(self, playground_id: str):
        return Playground.show_cache(playground_id)

    def clear_all_playgrounds(self):
        Playground.clear_all()
        self.playground_dict = {}

    # Just for pytest
    def _reset(self):
        self.clear_all_playgrounds()
