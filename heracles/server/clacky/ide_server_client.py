import asyncio
from concurrent.futures import Future
import json
import traceback
import random
import time
import re
from typing import Any, Callable, List, Optional
from uuid import uuid4

import aiohttp
import socketio

from heracles.core.schema import PlaygroundStatusType, FocusComponentType, LimitedStack
from heracles.core.config import get_env_var
from heracles.core.exceptions import IDEServerConnectException, IDEServerFunCallException, IDEServerFunCallFailureException, \
    IDEServerFunCallTimeoutException, IDEServerFileNotFoundError, IDEServerFileBinaryError, AgentRunException
from heracles.core.logger import get_playground_logger
from heracles.core.utils import wait_for
from heracles.core.utils.event_emitter import EventEmitter
from heracles.core.utils.string import camelize, remove_ansi_escape_sequences, splitlines_cmd, truncate
from urllib.parse import urlparse

from .ide_server_client_mixins import FocusAreaMixin


class IdeServerClient(FocusAreaMixin):
    """实现了 IDEServer 的 Client 功能, 封装各种 func_call 接口"""

    def __init__(self, playground):
        super().__init__()
        self.playground = playground
        self.logger = get_playground_logger(self.playground.playground_id).bind(life_id=str(uuid4()))
        self.event_emitter = EventEmitter()
        self._wait_task = None
        self._heartbeat_task = None
        self._consecutive_timeouts = 0  # 添加连续超时计数器
        self._last_timeout_time = 0  # 添加最后一次超时时间
        self.init_client()
        self.init_variables()

    def init_client(self):

        # 先清空历史连接和背景任务，再创建新的请求
        self.reset_client()

        # 经验证 python-socketio 5.11.3 在处理自动重连有卡住的情况, 可能跟它底层的 aiohttp 有关
        # 这里取消它的重连机制, 由我们 on_disconnect 自行处理
        # 同时开启 connect 本身的重连能力, 正好满足需求
        # 更多: https://python-socketio.readthedocs.io/en/stable/api.html#socketio.AsyncClient
        # 调试可加参数 logger=logger, engineio_logger=logger
        # socket_logger = self.logger.bind(module="socket-io")
        # engine_logger = self.logger.bind(module="engine-io")

        self.client = socketio.AsyncClient(
            reconnection=True,
            ssl_verify=False,
            # logger = socket_logger, engineio_logger = engine_logger
        )
        # on 事件处理: 例如 on_terminal 等
        self.client.on("disconnect", self.on_disconnect)
        self.client.on("connect_error", self.on_connect_error)  # disconnect事件只在第一次断连时触发, 之后发生连接错误会触发connect_error
        self.client.on("*", self.on_any)

    def reset_client(self):
        """ 清空旧的 client """
        if hasattr(self, 'client') and self.client:
            self.client.handlers['/'].clear()
        self.stop_wait_task()
        self.stop_heartbeat_task()

    def init_variables(self):
        # 容器是否在激活状态: ACTIVE, INACTIVE
        # 不在激活的话 terminal, run 操作不可用
        self.status = None
        self.active_retry_count = 0
        # 语言环境, 特别地，blank 为待定环境
        self.language = None
        # 终端的状态: LOADING, RUNNING, SHUTDOWN
        self.agent_terminal_id = None
        self.agent_terminal_type = "aiAgent"
        # 终端的类型: normal, aiAgent, goAgent
        self.opened_terminal_info = {}
        # rag的状态: RagInit, RagReIndex, RagIndexFinish
        self.rag_status = None
        # run 的状态: RUNNING, STOPPED
        self.run_status = None
        # http 端口: [{'url': '3000-xxx-web.develop.clackypaas.com', 'host': 'localhost', 'port': 3000}]
        self.http_ports = None
        # 环境信息
        self.environment = None
        # 同一时间只能一个 terminal 操作
        self._terminal_lock = False
        self._terminal_prompt_future = None
        # connect 过程一些缓存
        self._sdk_ticket = None
        self._ide_server_url = None

        # 容器内网地址
        self.agentServerInternalAddr = None
        self.agentServerUrl = None

        self.vncSupport = False

    def init_futures(self):
        loop = asyncio.get_running_loop()
        self._sync_playground_future = loop.create_future()
        self._auth_ack_future = loop.create_future()
        self._active_future = loop.create_future()

    async def _get_sdk_ticket(self):
        if self._sdk_ticket:
            return self._sdk_ticket
        paas_sdk_ticket_url = get_env_var("PAAS_DOMAIN_URL") + "/api/v1/sdk/ticket"
        params = {
            "playgroundId": self.playground.playground_id,
            "tillTime": 0,
            "userInfo": {
                "name": "Clacky",
                "userId": "clacky",
            },
        }
        headers = {"tenantCode": get_env_var("PAAS_TENANT_CODE")}
        async with aiohttp.ClientSession() as session:
            async with session.post(
                paas_sdk_ticket_url, json=params, headers=headers
            ) as response:
                res = await response.json()
        if not res["status"] == "success":
            self.logger.error(res)
            raise IDEServerConnectException(
                "wrong request to manager ticket, maybe url or tenantCode is wrong"
            )
        self._sdk_ticket = res["data"]["ticket"]
        return self._sdk_ticket

    async def _get_ide_server_url(self, sdk_ticket):
        if self._ide_server_url:
            return self._ide_server_url
        paas_js_ticket_url = get_env_var("PAAS_DOMAIN_URL") + "/api/v1/jssdk/ticket"
        params = {"packageVersion": "0.9.0", "serverCode": "", "ticket": sdk_ticket}
        async with aiohttp.ClientSession() as session:
            async with session.post(paas_js_ticket_url, json=params) as response:
                res = await response.json()
        if not res["status"] == "success":
            self.logger.error(res)
            raise IDEServerConnectException(
                "wrong request to ideserver ticket, maybe your playground is not match agent's tenantCode"
            )
        self._ide_server_url = res["data"]["url"]

        if custom_domain := get_env_var("IDE_SERVER_URL_OPTIONAL"):
            try:
                parsed_original = urlparse(self._ide_server_url)
                parsed_custom = urlparse(custom_domain) if "://" in custom_domain else urlparse(f"ws://{custom_domain}")
                original_prefix = f"{parsed_original.scheme}://{parsed_original.netloc}"
                custom_prefix = f"{parsed_custom.scheme}://{parsed_custom.netloc}"
                self._ide_server_url = self._ide_server_url.replace(original_prefix, custom_prefix)
                self.logger.info(f"Replace ide_server_url with internal domain: {custom_domain}")
            except Exception as e:
                self.logger.error(f"Failed to replace original ide_server_url: {self._ide_server_url}, error: {str(e)}")

        return self._ide_server_url

    async def connect(self):
        sdk_ticket = await self._get_sdk_ticket()
        ide_server_url = await self._get_ide_server_url(sdk_ticket)

        self.logger.info(
            (
                f"Ready for connecting to IdeServer: {ide_server_url},"
                f"tenantCode: {get_env_var('PAAS_TENANT_CODE')}, paasDomain: {get_env_var('PAAS_DOMAIN_URL')}"
            )
        )

        auth = {
            "tenantId": get_env_var("PAAS_TENANT_CODE"),
            "paasDomain": get_env_var("PAAS_DOMAIN_URL"),
            "ticket": sdk_ticket,
            "agentUserId": "clacky",
            "userInfo": {
                "username": "Clacky",
                "avatarUrl": (
                    "https://assetscdn.clacky.ai/clacky/marvin_avatar_common.svg"
                ),
                "userId": "clacky",
            }
        }

        self.init_futures()

        # 某些特殊情况会导致 client 已经初始化过，这里先处理掉异常问题再连接
        self.logger.debug(f"client connected = `{self.client.connected}`")
        if self.client.connected:
            self.logger.debug("client already connected, disconnect it before connecting")
            await self.disconnect()
            self.logger.debug(f"client connected = `{self.client.connected}`")
        # 在测试过程出现过 disconnect 后仍然是 connected 的情况，
        # 这种一般发生在隔了很久再断开时，可能是 python-socketio 库的问题，我们在这里进行重新初始化
        self.init_client()

        self.client.on("authAck", self.on_auth_ack)
        self.client.on("syncPlaygroundInfo", self.on_sync_playground_info)
        self.client.on("active", self.on_active)
        self.client.on("multiTerminal", self.on_multi_terminal)
        self.client.on("terminalStatus", self.on_multi_terminal_status)
        self.client.on("multiTerminalCmdReply", self.on_multi_terminal_cmd_reply)
        self.client.on("runStatus", self.on_run_status)
        self.client.on("ragStatus", self.on_rag_status)
        self.client.on("ports", self.on_http_ports)

        if 'playgroundId' not in ide_server_url:
            ide_server_url = ide_server_url + f'?playgroundId={self.playground.playground_id}'
        if 'localhost' in ide_server_url:  # pragma: no cover
            ide_server_url = ide_server_url.replace('localhost', '127.0.0.1')
        self.logger.info(f"connect to ide_server_url: {ide_server_url}")

        await self.client.connect(ide_server_url, auth=auth, transports=['websocket'], retry=True)

        await self.wait_for_auth_ack()

        sync_timeout = int(get_env_var("IDE_SERVER_SYNC_PLAYGROUND_INFO_TIMEOUT_OPTIONAL", 7))
        await self.wait_for_sync_playground(timeout=sync_timeout)

        await self.start_heartbeat_task()

        self.logger.info('connect ide server successfully')

    async def wait_for_auth_ack(self, timeout=3):
        try:
            auth_res = await asyncio.wait_for(self._auth_ack_future, timeout)
            if not auth_res:
                await self._raise_connect_exception("auth fail, status error")
        except asyncio.TimeoutError:  # pragma: no cover
            await self._raise_connect_exception(f"auth fail: Timeout({timeout}s)")

    async def wait_for_sync_playground(self, timeout=7):
        try:
            sync_res = await asyncio.wait_for(self._sync_playground_future, timeout)
            if not sync_res:
                await self._raise_connect_exception("sync playground info fail: status error")

        except asyncio.TimeoutError:  # pragma: no cover
            await self._raise_connect_exception(f"sync playground info fail: Timeout({timeout}s)")

    async def _raise_connect_exception(self, message):  # pragma: no cover
        await self.disconnect()
        raise IDEServerConnectException(message)

    async def get_docker_url(self) -> str | None:
        await self.wait_for_active()

        if not self.vncSupport:
            return None

        is_debug_mode = get_env_var("IDE_SERVER_DEBUG_OPTIONAL", False)
        if is_debug_mode:
            if self.agentServerUrl:
                return "https://" + self.agentServerUrl

        if self.agentServerInternalAddr:
            return "http://" + self.agentServerInternalAddr

        return None

    async def wait_for_active(self, timeout=20):
        if self._agent_is_active():
            return

        try:
            # 在 sync_playground 阶段已经发出激活请求, 这里可以直接等待即可
            await wait_for(self._agent_is_active, timeout = timeout)
        except AgentRunException:
            raise IDEServerFunCallException(f'Active fail: Timeout({timeout}s)') from None

    def _get_terminal_status(self):
        if (self.agent_terminal_id
            and self.agent_terminal_id in self.opened_terminal_info
            and self.opened_terminal_info[self.agent_terminal_id]
        ):
            return self.opened_terminal_info[self.agent_terminal_id].get('status', '').upper()

        return ''

    async def wait_for_terminal_running(self, timeout=15):
        def is_running():
            return self._get_terminal_status() == 'RUNNING'

        if is_running():
            return

        try:
            # 尝试激活终端
            await self._agent_open_terminal(self.agent_terminal_id)

            await wait_for(is_running, timeout=timeout)
        except AgentRunException:  # pragma: no cover
            raise IDEServerFunCallException(
                f"Failed to wait_for terminal status RUNNING({timeout}s), now the status is `{self._get_terminal_status()}`"
            ) from None

    async def on_auth_ack(self, data: dict[str, Any]):
        self.logger.bind(event_name="authAck").debug(str(data))
        if not self._auth_ack_future.done():
            self._auth_ack_future.set_result(data["result"])
            self.logger.info("auth ack successfully")

    async def disconnect(self):
        """主动调用关闭连接
        用于清理 playground 时释放 socketio 链接
        """
        self.logger.info("disconnect ideserver client called")

        try:
            await self.client.shutdown()
            self.stop_heartbeat_task()
            self.playground.set_playground_status(PlaygroundStatusType.CONNECT_TERMINATED, 'ideserverclient disconnect called')
            self.init_variables()
        except Exception as e:
            self.logger.warning(f'disconnect error: {e}\nTraceback: {traceback.format_exc()}')

    async def on_disconnect(self):  # pragma: no cover
        self.logger.bind(event_name="on_disconnect").debug("")
        self.playground.set_playground_status(PlaygroundStatusType.CONNECT_FAILED)

    async def on_connect_error(self, reason):
        self.logger.bind(event_name="on_connect_error").warning(str(reason))
        self.playground.set_playground_status(PlaygroundStatusType.CONNECT_FAILED)

    def stop_wait_task(self):
        if hasattr(self, '_wait_task') and self._wait_task:  # pragma: no cover
            self._wait_task.cancel()
            self._wait_task = None

    async def start_heartbeat_task(self):
        async def loop_heartbeat():
            ping_timeout, ping_interval = self._get_ping_config()

            while True:
                self.logger.debug(">>heartbeat")
                await self.client.emit("heartbeat")

                # heartbeat 必须大于ws server的（ping发送间隔+发送超时)
                sleep_time = ping_timeout + ping_interval + random.randint(1, 5)
                await asyncio.sleep(sleep_time)

        self.stop_heartbeat_task()
        self._heartbeat_task = asyncio.create_task(loop_heartbeat())

    def _get_ping_config(self):
        ping_timeout = int(get_env_var("IDE_SERVER_PING_TIMEOUT_OPTIONAL", 100))
        ping_interval = int(get_env_var("IDE_SERVER_PING_INTERVAL_OPTIONAL", 25))

        if eio := getattr(self.client, "eio", None):
            if eio_ping_timeout := getattr(eio, "ping_timeout", None):
                ping_timeout = eio_ping_timeout

            if eio_ping_interval := getattr(eio, "ping_interval", None):
                ping_interval = eio_ping_interval

        return ping_timeout, ping_interval


    def stop_heartbeat_task(self):
        if hasattr(self, '_heartbeat_task') and self._heartbeat_task:
            self._heartbeat_task.cancel()
            self._heartbeat_task = None

    # 这里是连接成功后, 后续可能收到 syncPlaygroundInfo 事件的处理逻辑
    # 一般收到的原因来自于容器失活, 所以自动激活
    async def on_sync_playground_info(self, data: dict):
        # 过滤掉包含 fileTree 键的项
        filter_keys = ['fileTree', 'terminalHistory', 'multiTerminalHistory', 'consoleHistory']
        filtered_data = {k: v for k, v in data.items() if k not in filter_keys}
        self.logger.bind(event_name="syncPlaygroundInfo").info(f"playground_info: {filtered_data}")

        playground_ide_server_status = data["playgroundIDEServerStatus"]
        status = data['status']
        self.logger.info(
            f"playgroundIDEServerStatus: {playground_ide_server_status}, status: {status}"
        )

        if status == 'EMPTY':
            self.logger.warning('status: got EMPTY status, ignore it this time')
            return

        if playground_ide_server_status != "SYNCED":
            reason = f"on_sync_playground_info error: {playground_ide_server_status}, \
                reason is {data.get('playgroundIDEServerStatusReason')}"
            self.logger.error(reason)
            self.playground.set_playground_status(PlaygroundStatusType.CONNECT_FAILED, reason)
            if not self._sync_playground_future.done():  # pragma: no cover
                self._sync_playground_future.set_result(False)
        else:
            self.playground.set_playground_status(PlaygroundStatusType.OK)
            if not self._sync_playground_future.done():
                self._sync_playground_future.set_result(True)
                self.logger.info("sync playground successfully")

        self.opened_terminal_info = {}
        if opened_terminal_list := data.get("openedTerminalList"):
            for terminal_data in opened_terminal_list:
                terminal_id = terminal_data['terminalId']
                terminal_type = terminal_data['terminalType']
                self.opened_terminal_info[terminal_id] = {}
                self.opened_terminal_info[terminal_id]['type'] = terminal_type
                self.opened_terminal_info[terminal_id]['status'] = 'running'
                if terminal_type == 'aiAgent':
                    self.agent_terminal_id = terminal_id

        if multi_terminal_status := data.get("multiTerminalStatus"):
            for terminal_data in multi_terminal_status:
                terminal_id = terminal_data['terminalId']
                terminal_type = terminal_data['terminalType']
                status = terminal_data['value']
                if terminal_id in self.opened_terminal_info:
                    self.opened_terminal_info[terminal_id]['status'] = status
                elif status.upper() == 'RUNNING':
                    self.opened_terminal_info[terminal_id] = {}
                    self.opened_terminal_info[terminal_id]['type'] = terminal_type
                    self.opened_terminal_info[terminal_id]['status'] = status

        if multi_terminal_history := data.get("multiTerminalHistory"):
            for terminal_data in multi_terminal_history:
                terminal_id = terminal_data['terminalId']
                value = terminal_data['value']
                if terminal_id in self.opened_terminal_info:
                    self.opened_terminal_info[terminal_id]['history'] = value

        if rag_status := data.get("ragStatus"):
            self.rag_status = rag_status

        if run_status := data.get("runStatus"):
            self.run_status = run_status

        if environment := data.get("environmentVersion"):
            self.environment = environment

        self.language = data.get('language')
        self.status = data.get('status')
        if self._agent_need_active():
            await self._agent_active()

        if vncSupport := data.get('vncSupport'):
            self.vncSupport = vncSupport

        if agentServerUrl := data.get('agentServerUrl'):
            self.agentServerUrl = agentServerUrl

    async def on_active(self, data: dict):
        self.logger.bind(event_name="active").debug(str(data))
        if not data["success"]:
            reason = data['reason']
            env_unready = ('DOCKER_IMAGE_NOT_READY' in reason
                           or 'RESOURCE_NOT_ENOUGH' in reason
                           or 'DOCKER_INFO_LOST' in reason)

            if env_unready and self.active_retry_count < 3:
                self.logger.warning(f"on_active error: {reason}, will retry after 20s")
                await asyncio.sleep(20)
                await self._agent_active()
                self.active_retry_count += 1
                return
            self.logger.error(f"on_active error unhandled: {reason}")
            self.status = "INACTIVE"
            if not self._active_future.done():  # pragma: no cover
                self._active_future.set_result(False)
                self.playground.set_playground_status(PlaygroundStatusType.CONNECT_FAILED)
            self.active_retry_count = 0
        else:
            self.status = "ACTIVE"
            if not self._active_future.done():  # pragma: no cover
                self.logger.info("active successfully")
                self._active_future.set_result(True)
            self.active_retry_count = 0
            if agentServerInternalAddr := data.get('agentServerInternalAddr'):
                self.agentServerInternalAddr = agentServerInternalAddr

    async def on_run_status(self, data: dict[str, Any]):
        """ 事件: run 状态变化 """
        self.logger.bind(event_name="run_status").debug(str(data))
        if run_status := data['status']:
            self.logger.debug(f"set run_status={run_status}")
            self.run_status = run_status
        # 如果启动失败不会出现在 terminal 输出，如果注册了 terminal 事件, 则将 run 的失败输出从 terminal 发送出去
        if (self.event_emitter.has_listener("terminal") and  # pragma: no cover
            data.get('internalRunInfo', {}).get('run', {}).get('err')):
            self.logger.warning(f'run failed {data["internalRunInfo"]["run"]["err"]}, emit to terminal output')
            await self.event_emitter.emit("terminal", data['internalRunInfo']['run']['output'], self.agent_terminal_id, "aiAgent")
            await self.event_emitter.emit("terminal", data['internalRunInfo']['run']['err'], self.agent_terminal_id, "aiAgent")

    async def on_rag_status(self, data: dict):
        """ 事件: rag 状态变化 """
        self.logger.bind(event_name="rag_status").debug(str(data))
        if rag_status := data['value']:
            self.logger.debug(f"set rag_status={rag_status}")
            self.rag_status = rag_status

    async def on_http_ports(self, data: str):
        # FIXME 没覆盖测试
        self.logger.bind(event_name="ports").info(str(data))
        if http_ports := data:
            self.logger.info(f"set http_ports={http_ports}")
            self.http_ports = json.loads(http_ports).get('ports', [])

    async def on_multi_terminal(self, data: dict[str, Any]):
        """事件: 接收到 multiTerminal 消息

        注册自己的处理事件:
        ide_server_client.event_emitter.on("multiTerminal", callback)
        """
        self.logger.bind(event_name="multiTerminal").debug(str(data))
        value = data["data"]["value"]
        if self.event_emitter.has_listener("terminal"):
            await self.event_emitter.emit("terminal", value, data["data"]["terminalId"], data["data"].get("terminalType"))

    async def on_multi_terminal_cmd_reply(self, data: dict[str, Any]):
        """事件: 接收到 multiTerminalCmdReply 消息"""
        self.logger.bind(event_name="multiTerminalCmdReply").debug(str(data))
        terminal_id = data["terminalId"]
        value = data["cmd"]
        terminal_type = data['terminalType']
        if value == 'open':
            if terminal_id not in self.opened_terminal_info:
                self.opened_terminal_info[terminal_id] = {}
            self.opened_terminal_info[terminal_id]['type'] = terminal_type
            if terminal_type == 'aiAgent':
                self.agent_terminal_id = terminal_id
            self.opened_terminal_info[terminal_id]['status'] = 'running'
        else:
            self.opened_terminal_info.pop(terminal_id, None)
            if self.agent_terminal_id == terminal_id:
                self.agent_terminal_id = None

    async def on_multi_terminal_status(self, data: dict[str, Any]):
        """事件: 接收到 multiTerminalStatus 消息"""
        self.logger.bind(event_name="multiTerminalStatus").debug(str(data))
        terminal_id = data["terminalId"]
        status = data["value"]
        if terminal_id not in self.opened_terminal_info:
            self.opened_terminal_info[terminal_id] = {}
        self.opened_terminal_info[terminal_id]['status'] = status

        self.logger.info(f'change terminal `{terminal_id}` status to `{status}`')

    async def on_any(self, event_name, *args, **kwargs):
        white_list = ["multiTerminal", "pullOTUpdates",
            "usersUpdated", "file", "resourceMonitoring", "heartbeat", "fileChange"
        ]
        if event_name in white_list:  # pragma: no cover
            # ignore white list
            return
        self.logger.bind(event_name=event_name).debug(str(args))

    # func_call list
    # 命名规范, 全部以 agent_动作 作为标准命令, 意思是由 agent 发送到 IDEServer 的指令

    # func_call: 读取文件内容
    @FocusAreaMixin.focus(FocusComponentType.EDITOR)
    async def agent_read_file(self, path, silent=False, load_type="default"):
        """
        silent: silent为True且用户为跟随状态下，CDE会有打开文件的行为
        """
        data = {"path": path, "timestamp": int(time.time()), "loadType": load_type, "readOnly": silent}
        try:
            res = await self._agent_func_call("file", **data)
            return str(res)
        except IDEServerFunCallException as e:  # pragma: no cover
            if "ENOENT" in str(e):
                raise IDEServerFileNotFoundError(f'File not found: {path}') from None
            elif "Binary or big file" in str(e):
                raise IDEServerFileBinaryError(f'File is binary or too big: {path}') from None
            elif "ENOTDIR" in str(e):
                raise IDEServerFileNotFoundError(f'Directory in path not found: {path}') from None
            raise e

    @FocusAreaMixin.focus(FocusComponentType.TREE)
    async def agent_close_file(self):
        data = {"timestamp": int(time.time())}
        return await self._agent_func_call("closeFile", **data)

    @FocusAreaMixin.focus(FocusComponentType.EDITOR)
    async def agent_write_file(self, path, content, open_file_after_write=False):
        return await self._agent_func_call(
            "agent_write_file", path=path, content=content, shouldOpenFileAfterWrite=open_file_after_write
        )

    @FocusAreaMixin.focus(FocusComponentType.EDITOR)
    async def agent_append_file(self, path, text):
        return await self._agent_func_call(
            "agent_append_file", path=path, text=text
        )

    @FocusAreaMixin.focus(FocusComponentType.TREE)
    async def agent_create_file(self, path):
        data = {"action": "CREATE", "files": [{"type": "FILE", "name": path}]}
        return await self._agent_func_call("fileTree", **data)

    @FocusAreaMixin.focus(FocusComponentType.TREE)
    async def agent_move_file(self, path, new_path):
        data = {
            "action": "MOVE",
            "files": [{"type": "FILE", "name": path}],
            "destination": new_path,
        }
        return await self._agent_func_call("fileTree", **data)

    @FocusAreaMixin.focus(FocusComponentType.TREE)
    async def agent_delete_file(self, path):
        data = {"action": "DELETE", "files": [{"type": "FILE", "name": path}]}
        return await self._agent_func_call("fileTree", **data)

    @FocusAreaMixin.focus(FocusComponentType.TREE)
    async def agent_create_directory(self, path):
        data = {"action": "CREATE", "files": [{"type": "DIRECTORY", "name": path}]}
        return await self._agent_func_call("fileTree", **data)

    @FocusAreaMixin.focus(FocusComponentType.TREE)
    async def agent_move_directory(self, path, new_path):
        data = {
            "action": "MOVE",
            "files": [{"type": "DIRECTORY", "name": path}],
            "destination": new_path,
        }
        return await self._agent_func_call("fileTree", **data)

    @FocusAreaMixin.focus(FocusComponentType.TREE)
    async def agent_delete_directory(self, path):
        data = {"action": "DELETE", "files": [{"type": "DIRECTORY", "name": path}]}
        result = await self._agent_func_call("fileTree", **data)
        return result

    @FocusAreaMixin.focus(FocusComponentType.TREE)
    async def agent_file_tree(self):
        """func_call: 获取 fileTree 的别名版本"""
        data = {}  # type: ignore
        return await self._agent_func_call("agent_file_tree", **data)

    @FocusAreaMixin.focus(FocusComponentType.TERMINAL)
    async def agent_fetch_terminal_history(self, terminal_id, lines):
        data = {"terminalId": terminal_id, "lines": lines}
        return await self._agent_func_call("fetchTerminalHistory", **data)

    async def agent_rag_search(self, queries):
        """
        func_call: Get relevant snippets from go agent by queries
        """
        await self.wait_for_active()

        timeout = 5
        try:
            await wait_for(self._agent_rag_is_finish, timeout)
        except AgentRunException:
            raise IDEServerFunCallException(
                f"Failed to wait_for rag status RagIndexFinish ({timeout}s), now the status is `{self.rag_status}`"
            ) from None

        data = {"query": queries}
        return await self._agent_func_call("agent_rag_search", **data)

    async def agent_file_content_search(
        self, keyword: str, regex: bool = False, whole_word_matching: bool = False, case_sensitive: bool = False
    ):
        """func_call: 根据关键词搜索文件
        keyword: 搜索用的关键词
        regex: 是否是正则表达式
        whole_word_matching: 是否要匹配整个词
        case_sensitive: 是否要匹配大小写
        """
        await self.wait_for_active()

        return await self._agent_func_call(
            'file_content_search', keyword=keyword, regex=regex, whole_word_matching=whole_word_matching, case_sensitive=case_sensitive
        )

    @FocusAreaMixin.focus(FocusComponentType.TERMINAL)
    async def agent_run(self):
        """func_call: 运行项目"""
        await self.wait_for_active()

        data = {}  # type: ignore
        return await self._agent_func_call("run", **data)

    @FocusAreaMixin.focus(FocusComponentType.TERMINAL)
    async def agent_stop(self):
        """ func_call: 停止项目 """
        data = {}  # type: ignore
        return await self._agent_func_call("stop", **data)

    @FocusAreaMixin.focus(FocusComponentType.TERMINAL)
    async def agent_terminal(self, cmd):
        """func_call: 在终端执行命令
        成功返回说明命令已经发送到 IDEServer, 并不意味命令成功
        如需返回结果版本请查看 ``agent_terminal_with_result`` 接口

        :param cmd: 执行的命令
        """
        if self._terminal_lock:
            raise IDEServerFunCallException(
                "already exist another terminal func_call, please try later"
            )
        self._terminal_lock = True
        try:
            return await self._agent_execute_in_terminal(cmd)
        finally:
            self._terminal_lock = False

    @FocusAreaMixin.focus(FocusComponentType.TERMINAL)
    async def _agent_open_terminal(self, terminal_id=None):
        """func_call: 打开终端
        打开指定terminal_id的终端
        terminal_id 为空则由服务端新建并生成终端id
        发出后multiTerminalCmdReply消息返回结果

        :param terminal_id: 终端id
        """

        data = {}
        data['terminalType'] = 'aiAgent'
        if terminal_id:
            data['terminalId'] = terminal_id
        return await self._agent_func_call('openTerminal', **data)

    @FocusAreaMixin.focus(FocusComponentType.TERMINAL)
    async def agent_close_terminal(self, terminal_id):
        """func_call: 关闭终端
        关闭指定terminal_id的终端
        terminal_id 不能为空
        发出后multiTerminalCmdReply消息返回结果

        :param terminal_id: 终端id
        """
        if not terminal_id:
            return

        await self.wait_for_active()

        data = {'terminalId': terminal_id}
        return await self._agent_func_call('closeTerminal', **data)

    @FocusAreaMixin.focus(FocusComponentType.TERMINAL)
    async def agent_active_terminal(self, terminal_id):
        """func_call: 使ui切换到指定终端
        terminal_id 不能为空

        :param terminal_id: 终端id
        """
        if not terminal_id:
            return

        await self.wait_for_active()

        data = {'value': terminal_id}
        return await self._agent_func_call('activeTerminal', **data)

    async def _agent_execute_in_terminal(self, cmd):
        """terminal 原始版本, 仅内部调用"""
        await self.wait_for_active()

        await self.wait_for_terminal_running()

        if not cmd.endswith("\n"):
            cmd += "\n"
        await self.agent_active_terminal(self.agent_terminal_id)
        data = {"terminalId": self.agent_terminal_id, "value": cmd}
        return await self._agent_func_call("multiTerminal", **data)

    @FocusAreaMixin.focus(FocusComponentType.TERMINAL)
    async def agent_terminal_with_result(
        self,
        cmd: str,
        *,
        soft_timeout: int = 10,
        hard_timeout: Optional[int] = None,
        soft_callback: Optional[Callable[[Future], None]] = None
    ):
        """ 在终端执行命令并返回执行结果
        这个版本实现了将 `cmd` 传入终端, 执行完并返回结果的功能

        :param cmd: 命令内容
        :param soft_timeout: 软超时等待时间，必选，默认为 10（0 为不超时）, 所谓软是指只要在等待过程命令行仍有返回信息就会重新计算超时时间
        :param hard_timeout: 硬超时等待时间，可选, 无论如何，超过这个设定就会超时，不设定的话强制为 soft_timeout*2，最长不超过10*60s

        Return 命令执行结果内容, 过长会截断, 取最近 200 行, 用 `\n` 分开

        该函数会尝试先发出一个中断指令（例如ctrl+c、回车、q 等）去检查或清理持续运行的命令, 再真正执行命令
        异常: IDEServerFunCallException, 调用失败; IDEServerFunCallTimeoutException, 在指定时间内未执行完成
        """

        if not isinstance(soft_timeout, int) or soft_timeout > 120:
            raise IDEServerFunCallException(
                "soft_timeout argument error, must be integer, max value should less than 120"
            )

        if not hard_timeout:
            hard_timeout = soft_timeout * 2

        if hard_timeout == 0:
            hard_timeout = 10 * 60

        if hard_timeout < soft_timeout:
            raise IDEServerFunCallException(
                "hard_timeout argument must be greater than soft_timeout"
            )

        if self._terminal_lock:
            raise IDEServerFunCallException(
                "Already exist another terminal func_call, please try later"
            )
        try:
            self._terminal_lock = True
            # step1: 找到提示符
            prompt = await self._fetch_terminal_prompt(prompt_timeout=5, prompt_retry=3)

            # step2: 执行命令并等待发现提示符, 返回结果
            result = await self._fetch_terminal_result(
                cmd=cmd,
                prompt=prompt,
                soft_timeout=soft_timeout,
                hard_timeout=hard_timeout,
                soft_callback=soft_callback
            )
            return result
        finally:
            self._terminal_lock = False
            self.client.on("multiTerminal", self.on_multi_terminal)

    def _is_terminal_prompt(self, text):
        """ 是否匹配"""
        # e.g: [0;31m➜ [00mapp[32m (feature/update-prompts-to-english)[00m $
        pattern = r'^.*➜.*[$#]\s*$'
        return bool(re.match(pattern, text))

    async def _fetch_terminal_prompt(self, *, prompt_timeout, prompt_retry):
        """ step1: 找到提示符 """
        retry_time = 0
        retry_keys = ["\n", "\x03", "q"]

        try:
            while True:
                loop = asyncio.get_running_loop()
                self._terminal_prompt_future = loop.create_future()

                try:
                    retry_key = retry_keys[retry_time]
                except IndexError:  # pragma no cover
                    retry_key = "\x03"

                async def on_terminal_prompt(data: dict[str, Any]):
                    values = data["data"]["value"]
                    if self.event_emitter.has_listener("terminal"):
                        await self.event_emitter.emit("terminal", values, data["data"]["terminalId"], data["data"].get("terminalType"))
                    if not self._terminal_prompt_future:  # pragma: no cover
                        raise IDEServerFunCallException("Assert failed: _terminal_prompt_future can not be null")
                    for value, _ in self._deal_terminal_result_iterator(values):
                        if self._terminal_prompt_future.done():
                            return
                        if self._is_terminal_prompt(value):
                            self.logger.debug(f"terminal_prompt_future set result {repr(value)}")
                            self._terminal_prompt_future.set_result(value)
                            return
                self.client.on("multiTerminal", on_terminal_prompt)
                await self._agent_execute_in_terminal(retry_key)
                try:
                    self.logger.debug(f"wait_for prompt, retry_time is {retry_time}")
                    prompt = await asyncio.wait_for(self._terminal_prompt_future, timeout=prompt_timeout)
                    self.logger.debug(f"Found terminal prompt: {repr(prompt)}")
                    return prompt
                except asyncio.TimeoutError:  # pragma: no cover
                    self.logger.debug(f"wait_for prompt timeout, retry_time is {retry_time}")
                    if retry_time >= prompt_retry - 1:
                        raise IDEServerFunCallException(
                            f"Terminal session inactive, maybe CDE is currently unstable. "
                            f"Please ensure terminal is idle and try again later (wait: {prompt_timeout}x{prompt_retry}s)"
                        ) from None
                self.client.handlers["/"].pop("multiTerminal")  # pragma: no cover
                retry_time += 1  # pragma: no cover
        finally:
            self.client.on("multiTerminal", self.on_multi_terminal)

    async def _fetch_terminal_result(
            self,
            *,
            cmd,
            prompt,
            soft_timeout,
            hard_timeout,
            soft_callback
            ):
        """ step2: 执行命令并等待发现提示符, 返回结果 """
        cmd += " #AI"

        loop = asyncio.get_running_loop()
        future: Any = loop.create_future()
        future._cmd = cmd
        future._terminal_result = LimitedStack(500)
        future._next_append_or_push_flag = 'push'
        future._terminal_last_activity = time.time()
        future._time_start = time.time()
        future._check_soft_time_task = None
        future._check_hard_time_task = None

        multi_command = splitlines_cmd(cmd)

        def is_same_cmd(value, cmd):
            return value.strip() == cmd.strip()

        async def on_terminal_result(data: dict[str, Any]):
            values = data["data"]["value"]
            if self.event_emitter.has_listener("terminal"):
                await self.event_emitter.emit("terminal", values, data["data"]["terminalId"], data["data"].get("terminalType"))

            future._terminal_last_activity = time.time()
            for value, origin_value in self._deal_terminal_result_iterator(values):
                # 已经找到了终止符(默认是 prompt), 我们认为命令执行完成, 返回即可
                if len(multi_command) == 0 and self._is_terminal_prompt(value):
                    if not future.done():
                        # 发现红色控制符的话, 认为是命令执行失败了
                        if '\x1b[0;31m' in origin_value:
                            self.logger.debug('future set_result False')
                            future.set_result(False)
                        else:
                            self.logger.debug('future set_result True')
                            future.set_result(True)
                    return

                # 一直读到 `匹配 cmd` 之后, 再处理后续的返回值
                if len(multi_command) > 0:
                    if not is_same_cmd(value, multi_command[0]):
                        self.logger.warning(f"not consistent value={repr(value)}, cmd={repr(cmd)}")
                    else:
                        multi_command.pop(0)

                    continue
                else:
                    # 按照上一次约定 append 或者 push
                    self.logger.debug(f'on_terminal_result: value={value}, origin_value={repr(origin_value)}')
                    if future._next_append_or_push_flag == 'append':
                        self.logger.debug('on_terminal_result: append')
                        future._terminal_result.append_to_last_one(value)
                    else:
                        future._terminal_result.push(value)
                    # 计划下一次约定
                    if origin_value.endswith('\n'):
                        future._next_append_or_push_flag = 'push'
                    else:
                        future._next_append_or_push_flag = 'append'

        async def check_soft_timeout():
            nonlocal hard_timeout, soft_timeout
            try:
                self.logger.debug("check_soft_timeout task start")
                while not future.done():
                    time_since_last_activity = time.time() - future._terminal_last_activity
                    if time_since_last_activity > soft_timeout:
                        self.logger.debug("check_soft_timeout task if")
                        if not future.done():
                            self.logger.debug("check_soft_timeout task if set_exception")
                            if soft_callback is not None:
                                remain_hard_timeout = hard_timeout - (time.time() - future._time_start)
                                relay_input = await soft_callback(future)
                                if relay_input is not None:
                                    await self._agent_execute_in_terminal(relay_input)
                                else:
                                    soft_timeout = soft_timeout * 2
                                if hard_timeout > 0 and remain_hard_timeout > 0:
                                    await set_hard_timeout(remain_hard_timeout)
                            else:
                                future.set_exception(
                                    IDEServerFunCallTimeoutException(
                                        f"Command execution timed out. Please retry or execute manually(wait_soft: {soft_timeout}s), terminal output: {future._terminal_result.to_string()}" # noqa
                                    )
                                )
                    if future.done():
                        break
                    await asyncio.sleep(1)  # 每秒检查一次
            except Exception as e:  # pragma: no cover
                self.logger.warning(f"check_soft_timeout task error: {str(e)} \nTraceback: {traceback.format_exc()}")
            self.logger.debug("check_soft_timeout task end")
            future._check_soft_time_task = None

        async def set_hard_timeout(hard_timeout):
            assert hard_timeout > 0

            async def check_hard_timeout():
                nonlocal hard_timeout, soft_timeout
                try:
                    self.logger.debug("check_hard_timeout task start")
                    while not future.done():
                        if time.time() - future._time_start > hard_timeout:
                            self.logger.debug("check_hard_timeout task if")
                            future.set_exception(
                                IDEServerFunCallTimeoutException(
                                    f"Command execution timed out. Please retry or execute manually(wait_hard: {hard_timeout}s), terminal output: {future._terminal_result.to_string()}" # noqa
                                )
                            )
                            return
                        await asyncio.sleep(1)  # 每秒检查一次
                except Exception as e:  # pragma: no cover
                    self.logger.warning(f"check_hard_timeout task error: {str(e)} \nTraceback: {traceback.format_exc()}")
                self.logger.debug("check_hard_timeout task end")
                future._check_hard_time_task = None
            if future._check_hard_time_task is not None:
                future._check_hard_time_task.cancel()
            future._check_hard_time_task = asyncio.create_task(check_hard_timeout())

        try:
            self.client.on("multiTerminal", on_terminal_result)
            await self._agent_execute_in_terminal(cmd)
            if soft_timeout > 0:
                future._check_soft_time_task = asyncio.create_task(check_soft_timeout())
            else:
                self.logger.debug("soft_timeout is zero, skip check")
            if hard_timeout > 0:
                self.logger.debug("hard wait_for begin")
                await set_hard_timeout(hard_timeout)
                self.logger.debug("hard wait_for end")
            res = await asyncio.wait_for(future, None)
        except asyncio.TimeoutError:  # pragma: no cover
            # 可能是命令一直未返回, 或者命令没能成功发送到终端运行, 我们停止后抛出异常
            try:
                self.logger.warning("Cmd timeout, stop it first")
                await self._stop_terminal_cmd(prompt=prompt, prompt_timeout=5, prompt_retry=2)
                self.logger.info("Cmd timeout, stop it done")
            except Exception as e:
                self.logger.warning(f"Cmd timeout and we stop it timeout again, ignore it: #{e}")
                pass
            raise IDEServerFunCallTimeoutException(
                f"Command execution timed out. Please retry or execute manually(wait_hard: {hard_timeout}s)"
            ) from None
        finally:
            self.client.on("multiTerminal", self.on_multi_terminal)
            if future._check_soft_time_task:  # pragma: no cover
                future._check_soft_time_task.cancel()
        if future._terminal_result is True:  # pragma: no cover
            raise IDEServerFunCallException('Assert failed: return a boolean value')
        if not res:
            raise IDEServerFunCallFailureException(f'Command execution failed, res: `{future._terminal_result.to_string()}`')
        return future._terminal_result.to_string()

    async def _stop_terminal_cmd(self, *, prompt, prompt_timeout, prompt_retry):
        """ 用 ctrl+c 停止 cmd """
        retry_time = 0
        retry_keys = ["\x03", "\x1c", "q"]

        self.logger.debug('stop terminal cmd called')

        def on_terminal_prompt(data: dict[str, Any]):
            values = data["data"]["value"]
            if not self._terminal_prompt_future:  # pragma: no cover
                raise IDEServerFunCallException("Assert failed: _terminal_prompt_future can not be null")
            for value, _ in self._deal_terminal_result_iterator(values):
                if self._terminal_prompt_future.done():
                    return
                if self._is_terminal_prompt(value):
                    self._terminal_prompt_future.set_result(value)
                    return

        try:
            while True:
                loop = asyncio.get_running_loop()
                self._terminal_prompt_future = loop.create_future()
                self._terminal_prompt = prompt

                try:
                    retry_key = retry_keys[retry_time]
                except IndexError:  # pragma no cover
                    retry_key = "\x03"

                self.client.on("multiTerminal", on_terminal_prompt)
                await self._agent_execute_in_terminal(retry_key)
                try:
                    self.logger.debug(f"wait_for prompt: retry={retry_time}")
                    prompt = await asyncio.wait_for(self._terminal_prompt_future, timeout=prompt_timeout)
                    self.logger.debug(f"Found terminal prompt: {repr(prompt)}")
                    return
                except asyncio.TimeoutError:
                    self.logger.debug(f"wait_for prompt timeout: retry={retry_time}")
                    if retry_time >= prompt_retry - 1:
                        raise IDEServerFunCallException(
                            f"Command failed to stop, may CDE is unstable at now, please try later(wait: {prompt_timeout}x{prompt_retry}s)"
                        ) from None
                self.client.handlers["/"].pop("multiTerminal")
                retry_time += 1
        finally:
            self.client.on("multiTerminal", self.on_multi_terminal)

    def _deal_terminal_result_iterator(self, values):  # pragma: no cover
        self.logger.debug(f"deal new values: {repr(values)}")
        """内部使用: 处理终端返回结果"""
        parts = re.split(r'\r\n|\n', values)
        if parts and parts[-1] == '':
            parts.pop()
        for i, line in enumerate(parts):
            is_last = (i == len(parts) - 1)
            if not is_last or values.endswith('\r\n') or values.endswith('\n'):
                origin_line = line + '\n'
            else:
                origin_line = line
            parsed_line: List[str] = []
            skip_next = False
            if not origin_line:  # 空字符跳过
                continue
            real_line = remove_ansi_escape_sequences(line)
            for i, char in enumerate(real_line):
                if skip_next:
                    skip_next = False
                    continue
                if i != 0 and char == '\r':
                    # 跳过非行首的 \r 后面的一个字符
                    skip_next = True
                else:
                    parsed_line.append(char)
            value = ''.join(parsed_line)
            real_value = remove_ansi_escape_sequences(value)
            if origin_line != '\n' and real_value in ['\r', '']:
                # 跳过已经空掉的字符
                continue
            self.logger.debug(f"line->real value: {repr(line)}->{repr(real_value)}")
            yield real_value, origin_line

    async def agent_snapshot_file(self, path, content):
        """func_call: 自定义帧, 插入文件快照"""
        uuid = str(uuid4())
        data = {'action': 'snapshot_file', 'value': {'path': path, 'content': content}, 'uuid': uuid}
        await self._agent_func_call("appendCustomizeFrameData", **data)
        return uuid

    async def agent_query_snapshot_file(self, path, uuid=None):
        """func_call: 获取文件快照, 测试用"""
        if uuid:
            data = {'action': 'snapshot_file', 'uuid': uuid, 'value': {'path': path}}
        else:
            data = {'action': 'snapshot_file', 'value': {'path': path}}
        return await self._agent_func_call("queryCustomizeFrameData", **data)

    async def _agent_active(self):
        """仅供内部使用, 激活容器"""
        data = {}  # type: ignore
        return await self._agent_func_call("active", **data)

    def _agent_is_active(self):
        return self.status == "ACTIVE"

    def _agent_need_active(self):
        return (not self._agent_is_active()) and self.language != "blank"

    def _agent_terminal_is_running(self):
        return (
            self.agent_terminal_id
            and self.agent_terminal_id in self.opened_terminal_info
            and self.opened_terminal_info[self.agent_terminal_id].get('status', '').upper() == 'RUNNING'
        )

    def _agent_rag_is_finish(self):
        return self.rag_status == "RagReIndex" or self.rag_status == "RagIndexFinish"

    def _deal_func_call_result(self, result, func_call_name):
        """处理 func_call 返回结果"""
        if not isinstance(result, dict):
            return result
        match result.get("status"):
            case "ok":
                return result.get("data")
            case _:
                raise IDEServerFunCallException(
                    f"Func call `{func_call_name}` {result.get('status')}: {result.get('message')}"
                )

    async def following_focus_component(self, area: FocusComponentType):
        """聚焦组件"""
        await self._agent_func_call('followingFocusComponent', focus=area)


    async def _agent_func_call(self, func_call_name, **kwargs):
        """func_call 的底层实现, 这里确保返回时一定发送到了 IDEServer
        但不保证 IDEServer 一定正确处理了( 这依赖 IDEServer 的处理逻辑 )
        同时, 考虑到异步处理和性能要求, 也不一定要等处理结果

        注: 底层接口, 不需要主动调
        """
        current_time = int(time.time())

        # 检查是否需要重置连续超时计数
        if current_time - self._last_timeout_time > 60:  # 如果超过1分钟没有超时，重置计数
            self._consecutive_timeouts = 0

        loop = asyncio.get_running_loop()
        future = loop.create_future()

        async def callback(res):
            self.logger.debug(f"<<IDEserver: `{func_call_name}` callback called")
            if not future.done():
                future.set_result(res)

        event_name = camelize(func_call_name, uppercase_first_letter=False)

        self.logger.debug(f">>IDEserver: `{event_name}`, args=`{truncate(str(kwargs), 500)}`")

        await self.client.emit(event_name, data=kwargs, callback=callback)

        try:
            timeout = int(get_env_var('IDE_SERVER_FUNC_CALL_TIMEOUT_OPTIONAL', 7))
            res = await asyncio.wait_for(future, timeout=timeout)
            res = self._deal_func_call_result(res, func_call_name)
            # 成功执行后重置连续超时计数
            self._consecutive_timeouts = 0
        except asyncio.TimeoutError:
            self._consecutive_timeouts += 1
            self._last_timeout_time = current_time

            # 如果连续超时次数达到3次，尝试重建连接
            if self._consecutive_timeouts >= 3:
                self.logger.warning(f"Consecutive timeouts reached {self._consecutive_timeouts}, attempting to reset connection")
                await self.disconnect()

            raise IDEServerFunCallTimeoutException(f"{func_call_name} call timeout({timeout}s)") from None
        return res
