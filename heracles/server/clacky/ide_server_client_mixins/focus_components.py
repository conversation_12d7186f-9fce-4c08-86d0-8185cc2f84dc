from typing import Protocol

from functools import wraps
from typing import Optional

from heracles.core.schema import FocusComponentType

class FollowFocusInterface(Protocol):
    async def following_focus_component(self, component: FocusComponentType):
        pass

class FocusAreaMixin:
    def __init__(self):
        self._focus_area: Optional[FocusComponentType] = None
        # 测试时设为 False
        self._focus_enabled = True

    @property
    def focus_area(self):
        return self._focus_area

    @focus_area.setter
    def focus_area(self, value: FocusComponentType):
        self._focus_area = value

    @staticmethod
    def focus(area: FocusComponentType):
        def decorator(func):
            @wraps(func)
            async def wrapper(self, *args, **kwargs):
                if self._focus_enabled and self.focus_area != area:
                    await self.following_focus_component(area)
                    self.focus_area = area
                res = await func(self, *args, **kwargs)
                return res
            return wrapper
        return decorator
