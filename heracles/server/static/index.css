body {
  font-family: Arial, Helvetica, sans-serif;
  background-color: #FAFAFA;
  padding: 0;
  margin: 0;
}

p {
  margin: 0;
}

* {
  box-sizing: border-box;
}

pre {
  padding: 0.25rem 0.5rem;
  background-color: #444444;
  color: #FAFAFA;
  display: inline-block;
  white-space: pre-wrap;
  line-height: 1.5;
  font-size: 11px;
  border-radius: 0.5rem;
  width: 100%;
}

code {
  padding: 0.5rem;
  background-color: #6e6e6e;
  color: #FAFAFA;
  display: inline-block;
  border-radius: 0.25rem;
}

h1 {
  margin: 0;
}

label {
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  border: 1px solid #E4E4E7;
  background-color: #FAFAFA;
  font-size: 0.75rem;
}

label:hover {
  background-color: #F4F4F5;
}

button {
  outline: none;
  border: none;
  background-color: #6366F1;
  color: #FAFAFA;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  cursor: pointer;
  font-weight: 500;
}

button:hover {
  background-color: #4E46E5;
}

button:active {
  background-color: #4438CA;
}

textarea {
  outline: none;
  border: 1px solid #E4E4E7;
  border-radius: 0.5rem;
  padding: 0.5rem;
  font-size: 1rem;
  resize: none;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;

}

.flex-row {
  flex-direction: row;
}

.flex-1 {
  flex: 1;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.hidden {
  display: none;
}

.w-full {
  width: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.agent-demo-index {
  width: 100%;
}

.agent-demo-index-wrapper {
  max-width: 50rem;
  width: 100%;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  min-height: 100vh;
  justify-content: center;
  align-items: center;
  margin: auto;
}

.playground-id-form {
  display: flex;
  gap: 1rem;
}

.playground-id-form input[type="text"] {
  border: 1px solid #E4E4E7;
  border-radius: 0.5rem;
  padding: 0.5rem;
  font-size: 0.75rem;
  width: 14rem;
}

.action-box {
  display: flex;
  gap: 1rem;
}

.chat-box {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 50rem;
  background: transparent;
}

.chat-box--textarea {
  width: 100%;
  height: 4rem;
  margin: auto;
}

.chat-box--btn {
  margin: auto;
}

.chat-history {
  border: 1px solid #E4E4E7;
  border-radius: 0.5rem;
  width: 100%;
  height: 50vh;
  max-height: 30rem;
  max-width: 50rem;
  padding: 0.5rem;
  overflow-y: auto;
  overflow-x: hidden;
}

.chat-item {
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  overflow: hidden;
}

.chat-item-info {
  display: flex;
  gap: 0.5rem;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

.chat-item-avatar {
  width: 1rem;
  height: 1rem;
  border-radius: 100%;
  background-color: greenyellow;
  flex-shrink: 0;
}

.chat-item-avatar.chat-item-avatar--me {
  background-color: #6366F1;
}

.chat-item-avatar.chat-item-avatar--agent {
  background-color: #ffbf36;
}

.chat-item-content--message {
  overflow: auto;
  flex: 1;
  white-space: pre-line;
}

.chat-item-content--time {
  font-size: 0.75rem;
  color: #A0A0A0;
  line-height: 1;
  width: 2rem;
  flex-shrink: 0;
  text-align: end;
}

.message-fail {
  display: block;
  color: #ff0000;
  font-size: 0.75rem;
  padding: 0.5rem;
  border: 2px solid #ff4949;
  border-radius: 0.5rem;
  width: 100%;
}

.button-group {
  display: flex;
  gap: 1rem;
}

.button-group button {
  flex: 1;
}
