'use strict';

import { onReceiveMessage } from "./chat.js";

let socket = null
let oldPlaygroundId = ''

const initAgentSocket = (playgroundId, isRoot) => {
  console.log('oldPlaygroundId',oldPlaygroundId);
  if (socket) {
    if (playgroundId === oldPlaygroundId) {
      if (socket.connected) {
        disconnectAgentSocket()
      } else {
        openAgentSocket()
      }
      return
    } else {
      closeAgentSocket()
    }
  }

  socket = io('/', {auth: { isRoot }, query: { playgroundId }, transports: ['websocket']});
  oldPlaygroundId = playgroundId

  const statusLabelEl = document.getElementById('status');
  const availableContentEl = document.getElementById('available-content');
  const connectBtn = document.getElementById('connect')

  registerAgentSocketEvent('connect', () => {
    console.log(`🚀 Agent Connect ${socket.id}`)
    statusLabelEl.textContent = `Connected ${playgroundId}`;
    connectBtn.textContent = 'Disconnect'
  })

  registerAgentSocketEvent('disconnect',() => {
    console.log(`🚀 Disconnect`);
    statusLabelEl.textContent = 'Disconnected';
    connectBtn.textContent = 'Connect'
  })

  registerAgentSocketEvent('message',(msg) => {
    onReceiveMessage('message',msg);
  })

  registerAgentSocketEvent('sync',(msg) => {
    availableContentEl.classList.remove('hidden');
    onReceiveMessage('sync',msg);
  })

  registerAgentSocketEvent('chunkMessage',(msg) => {
    onReceiveMessage('chunkMessage',msg,true);
  })

  registerAgentSocketEvent('connect_error',(error) => {
    console.error('socket connect error',error);
  })

  registerAgentSocketEvent('errorHandler',(error) => {
    console.error('errorHandler',error);
    onReceiveMessage('errorHandler',error);
  })

  const registeredEvents = new Set(['connect','disconnect','message','sync','chunkMessage','connect_error', 'errorHandler']);

  socket.onAny((event,...args) => {
    console.log(`${event} event: `,...args);
    if (registeredEvents.has(event)) return
    onReceiveMessage(event,...args);
  });
}

export default initAgentSocket

/**
 * 注册 Agent Socket 事件
 * 重新注册会覆盖之前的事件
 * @param eventName 事件名
 * @param callback 回调
 */
const registerAgentSocketEvent = (eventName,callback) => {
  if (!socket) {
    console.warn('Agent Socket is not initialized')
    return
  }
  socket.removeAllListeners(eventName)
  socket.on(eventName,callback)
}

/**
 * 移除 Agent Socket 事件
 * @param eventName 事件名
 */
const removeAgentSocketEvent = (eventName) => {
  if (!socket) {
    console.warn('Agent Socket is not initialized')
    return
  }
  if (eventName) socket.removeAllListeners(eventName)
  else socket.removeAllListeners()
}

/**
 * 关闭 Agent Socket
 * @returns
 */
const closeAgentSocket = () => {
  if (!socket) return
  socket.removeAllListeners()
  socket.close()
  socket = null
}

const openAgentSocket = () => {
  if (!socket) return
  socket.connect()
}

const disconnectAgentSocket = () => {
  if (!socket) return
  socket.disconnect()
}

/**
 * 发送 Agent Socket 事件信息
 * @param {string} eventName 事件名
 * @param {any[]} data 内容
 * @param {Function} callback 回调
 */
const emitAgentSocket = (eventName,data,callback) => {
  if (!socket) {
    console.warn('Agent Socket is not initialized')
    return
  }
  if (socket.connected) {
    socket.emit(eventName,...data,callback)
  } else {
    throw new Error('Agent Socket is not connected')
  }
}

/**
 * 获取 Agent Socket 状态
 * @param {Function} callback
 * @returns {boolean} 是否连接
 */
const getAgentSocketStatus = (callback) => {
  if (socket) {
    callback(socket.connected)
  }
}

export {
  initAgentSocket,
  registerAgentSocketEvent,
  removeAgentSocketEvent,
  closeAgentSocket,
  emitAgentSocket,
  getAgentSocketStatus,
  openAgentSocket,
  disconnectAgentSocket,
}
