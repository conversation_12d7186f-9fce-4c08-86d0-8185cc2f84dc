import { emitAgentSocket } from "./socket.js"
import { scrollToBottom } from "./utils.js"

const chatHistoryEl = document.getElementById('chat-history')

const authorAvatarClassMap = {
  me: 'chat-item-avatar--me',
  agent: 'chat-item-avatar--agent'
}
const _chatHistories = localStorage.getItem('_chatHistories') || ['clacky terminal_with_result ls -l']
try {
  window.chatHistory = JSON.parse(_chatHistories)
} catch (error) {
  window.chatHistory = ['clacky terminal_with_result ls']
}
let currentRole = null

const renderChatItem = (eventName,message,author,isChunk) => {
  const now = new Date()
  const time = `${now.getHours()}:${now.getMinutes()}`
  const avatarClass = authorAvatarClassMap[author] || ''
  if (eventName === 'message' && author == 'me') {
    if( window.chatHistory[window.chatHistory.length-1] !== message) {
      chatHistory.push(message)
      localStorage.setItem('_chatHistories', JSON.stringify(chatHistory))
    }
  }

  // 判断类型如果是 Object 或 array 则执行
  if (['[object Object]','[object Array]'].includes(Object.prototype.toString.call(message))) {
    if (message.status === 'fail') {
      message = `<span class="message-fail">${message.message}</span>`
    } else {
      message = '<pre>' + JSON.stringify(message, null, 2) + '</pre>'
    }
  }

  if (isChunk && currentRole === author) {
    // 如果是 chunk，插入到最后一个 chunk
    const lastChatItemEl = chatHistoryEl.lastElementChild
    const lastChatItemMessageEl = lastChatItemEl.querySelector('.chat-item-content--message')
    if (lastChatItemMessageEl) {
      lastChatItemMessageEl.textContent += message
      scrollToBottom()
      return
    }
  }

  currentRole = author

  const template = `
  <div class="chat-item-info">
    <div class="flex items-center gap-2">
      <div class="chat-item-avatar ${avatarClass}"></div>
      <div class="chat-item-content--author">${author}</div>
    </div>
    <label class="chat-item-content--eventName">${eventName}</label>
    <div class="chat-item-content--time">${time}</div>
  </div>
  <div class="chat-item-content--message">${marked.parse(String(message))}</div>
  `

  const chatItem = document.createElement('div')
  chatItem.classList.add('chat-item')
  chatItem.innerHTML = template
  chatHistoryEl.appendChild(chatItem)
  scrollToBottom()
}

const getEventName = (e) => {
  const selectEl = document.getElementById('chat-box--select')
  return selectEl ? selectEl.value : 'message'
}

export const onFormSubmit = (e,author) => {
  const textareaEl = e.target.id === 'chat-box--textarea' ? e.target : e.target.querySelector('#chat-box--textarea')

  if (!textareaEl) {
    console.error('Textarea not found');
    return '';
  }

  let message = textareaEl.value.trim()

  let eventName = getEventName(e)

  if (eventName === 'message' && !message) {
    console.error('Message is empty');
    return;
  }

  // message 事件就直接解包发出, 非 message 则用空格拆出多参数进行处理
  let args = [message];

  if (eventName !== 'message') {
    if (message === '') {
      args = []
    } else {
      args = message.split('|')
    }
  }

  // 测试重新生成消息
  if (eventName === 'remessage') {
    eventName = 'message'
    args = [message, true]
  }

  const callback = (response) => {
    // 聊天模式下只有成功message了再上屏
    if (eventName === 'message') {
      if (response === true) {
        renderChatItem(eventName,message,author);
        textareaEl.value = '';
      } else {
        console.error(`server reject the message ${message}`)
      }
      return
    }

    // 事件模式下正常处理
    renderChatItem(eventName,message,author);
    console.log(`u<< ${eventName}, response: ${JSON.stringify(response)}`);
    renderChatItem(eventName,response,'agent');
    textareaEl.value = '';
  }
  console.log('args',args);
  console.log(`u>> ${eventName}, ${args.join(', ')}`);
  emitAgentSocket(eventName,args,callback);
}

export const onReceiveMessage = (eventName,message,isChunk = false) => {
  renderChatItem(eventName,message,'agent',isChunk);
}
