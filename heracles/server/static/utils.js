// 防抖
export const debounce = (fn,delay) => {
  let timer = null;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn.apply(this,arguments);
    },delay);
  };
}

export function throttle(func, wait, options) {
  let timeout, context, args, result;
  let previous = 0;
  if (!options) options = {};

  const later = function() {
    previous = options.leading === false ? 0 : new Date().getTime();
    timeout = null;
    func.apply(context, args);
    if (!timeout) context = args = null;
  };

  return function() {
    const now = new Date().getTime();
    if (!previous && options.leading === false) previous = now;
    const remaining = wait - (now - previous);
    context = this;
    args = arguments;
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      func.apply(context, args);
      if (!timeout) context = args = null;
    } else if (!timeout && options.trailing !== false) {
      timeout = setTimeout(later, remaining);
    }
  };
}

let eventHandlers = []

export const installEventHandler = (element,type,handler) => {
  element.addEventListener(type,handler)
  eventHandlers.push({ element,type,handler })
}

export const uninstallEventHandlers = () => {
  eventHandlers.forEach(({ element,type,handler }) => {
    element.removeEventListener(type,handler)
  })
  eventHandlers = []
}

const _scrollToBottom = () => {
  const chatHistoryEl = document.getElementById('chat-history')
  chatHistoryEl.scrollTop = chatHistoryEl.scrollHeight
}

export const scrollToBottom = throttle(_scrollToBottom,100)
