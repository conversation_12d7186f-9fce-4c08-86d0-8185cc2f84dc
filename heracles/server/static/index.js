import { initAgentSocket } from './socket.js'
import { initActionHandlers } from './actions.js'
import { onFormSubmit } from './chat.js';
import { uninstallEventHandlers } from './utils.js';

var oldPlaygroundId = ''

window.onPlaygroundIdSubmit = (event) => {
  event.preventDefault()
  const playgroundId = document.getElementById('playground-id-input').value
  const isRoot = document.getElementById('is-root-checkbox').checked;

  // 建立 Socket 连接
  initAgentSocket(playgroundId, isRoot);
  localStorage.setItem('playground-id', playgroundId)

  oldPlaygroundId = playgroundId

  initActionHandlers()

  window.onFormSubmitClick = (event) => {
    event.preventDefault()
    onFormSubmit(event, 'me')
  }
}

const storagePlaygroundId = localStorage.getItem('playground-id')
if (storagePlaygroundId) {
  document.getElementById('playground-id-input').value = storagePlaygroundId
}

// 关闭时卸载事件 uninstallEventHandlers
window.onbeforeunload = () => {
  uninstallEventHandlers()
}

// 使用 data-event 属性为所有按钮添加统一的点击事件处理
const buttons = document.querySelectorAll('.button-group button[data-event]');
buttons.forEach(button => {
  button.addEventListener('click', () => {
    const eventType = button.getAttribute('data-event');
    simulateFormSubmit(eventType);
  });
});

// 定义模拟表单提交的函数
function simulateFormSubmit(eventType) {
  const textareaEl = document.getElementById('chat-box--textarea');
  const selectEl = document.getElementById('chat-box--select');

  // 保存原始选择值
  const originalValue = selectEl.value;

  // 临时设置事件类型
  selectEl.value = eventType;

  // 清空文本区域
  textareaEl.value = '';

  // 模拟表单提交
  const formEvent = new Event('submit', { cancelable: true, bubbles: true });
  const formEl = document.querySelector('.chat-box');
  formEl.dispatchEvent(formEvent);

  // 恢复原始选择值
  selectEl.value = originalValue;
}
