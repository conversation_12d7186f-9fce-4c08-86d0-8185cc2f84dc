'use strict';

import { onFormSubmit } from "./chat.js";
import { installEventHandler,uninstallEventHandlers,throttle } from "./utils.js";

let lastChatMessageIndex = -1

const onFormSubmitThrottle = throttle(onFormSubmit, 200, { leading: true, trailing: false })

const onKeyPressEnter = (e) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    onFormSubmitThrottle(e,'me')
    lastChatMessageIndex = -1
    return
  }

  if (e.key === 'ArrowUp') {
    e.preventDefault()
    const lastChatMessage = window.chatHistory.at(lastChatMessageIndex)
    const currentValue = e.target.value
    if (currentValue) {
      window.chatHistory.push(currentValue)
      lastChatMessageIndex -= 1
    }
    e.target.value = lastChatMessage
    lastChatMessageIndex = Math.max(lastChatMessageIndex - 1,-window.chatHistory.length)
  }

  if (e.key === 'ArrowDown') {
    e.preventDefault()
    const nextIndex = Math.min(lastChatMessageIndex + 1,window.chatHistory.length - 1)
    const lastChatMessage = window.chatHistory.at(lastChatMessageIndex + 1) || ''
    e.target.value = lastChatMessage
  }
}

export const initActionHandlers = () => {
  uninstallEventHandlers()
  const actionMap = {
    'chat-box--textarea': { event: 'keydown',handler: onKeyPressEnter },
  }

  Object.entries(actionMap).forEach(([action,{ event,handler }]) => {
    const el = document.getElementById(action)
    if (!el) {
      console.warn('Element not found',action);
      return
    }
    installEventHandler(el,event,handler)
  })
}
