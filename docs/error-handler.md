# 错误处理

当系统在 background task 运行过程发生错误时，为了更好的感知与处理，我们会用 `ErrorHandlerMessage` 对象往前端抛出

## 事件名称与返回值格式

名称：`error_handler`

返回值: ErrorHandlerMessag

    error_type: 'warn:spec'
    content: 'xxx'


error_type xxx:yyy 用冒号分隔，前半部分是处理建议, warn/retry/info, warn 推荐用 toast, retry 推荐重试，info 推荐用 console.log

    目前取值：

    DEFAULT = 'warn:default'
    SPEC = 'retry:spec'
    PLAN = 'retry:plan'
    TASK = 'retry:task'
    ANALYZE = 'warn:analyze'
    CHECK = 'warn:check'
    CLEANUP = 'warn:cleanup'
    CMDK = 'retry:cmdk'
    RERUN = 'retry:rerun'
    MESSAGE = 'retry:message'
