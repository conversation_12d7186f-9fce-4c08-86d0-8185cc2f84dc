## PR 相关接口设计

### 事件:

所有返回值以 { status: 'ok/fail/fatal', message: 'reason', data: xxx } 格式进行, 以下不再重复说明

#### uMakeCommitFileList: 生成 Git Commit 的变更( git status 信息)

参数: 无

返回值:
    [
        {"path": "xxx", "status": "M", ai: true, changes?: [{ start: 1, end: 3 }]},
        {"path": "yyy", "status": "D", ai: false, },
    ]

    path: 文件路径
    status: 状态
    changes: 可选, 是一个数组, 表示变更的修改内容, 开始行与结束行

    其中, status 可能有以下取值:

    M：已修改
    A：新增到暂存区
    D：从暂存区删除
    R：重命名
    C：复制
    U：更新但未合并
    ?：未跟踪的文件
    !：被忽略的文件 [1]


#### uMakeCommitMessage: 生成提交信息

参数: 无

返回值:
    suggested_message: 建议提交 commit 的信息
    file_list: [
        {"path": "xxx", "status": "M", changes?: [{ start: 1, end: 3 }]},
    ]

#### uMakePRFileList: 生成PR信息

对比最新 commit 与 origin/branch 之间的文件变更, 再合并 AI TaskAction 信息, 返回输出

参数: 无

返回值:
    [
        {"path": "xxx", "status": "M", ai: true, changes?: [{ start: 1, end: 3 }]},
        {"path": "yyy", "status": "D", ai: false},
    ]

    path: 文件路径
    status: 状态
    changes: 可选, 是一个数组, 表示变更的修改内容, 开始行与结束行

    其中, status 可能有以下取值:

    M：已修改
    A：新增到暂存区
    D：从暂存区删除
    R：重命名
    C：复制
    U：更新但未合并
    ?：未跟踪的文件
    !：被忽略的文件 [1]

    可能的错误: 分支不干净. 执行错误等


#### uMakePRMessage: 生成PR信息

参数: 无

返回值:
    suggested_title: 建议提交 commit 的信息
    suggested_description: 建议提交 commit 的信息
    file_list: [
        {"path": "xxx", "status": "M", changes?: [{ start: 1, end: 3 }]},
    ]
