general_settings:
  master_key: sk-1234
# litellm_settings:
#   context_window_fallbacks:
#   - bedrock/us.anthropic.claude-3-7-sonnet-********-v1:0:
#     - gemini-2.5-pro-preview
model_list:
- litellm_params:
    api_base: https://clacky.openai.azure.com/
    api_key: 9AUCYTtgaeX0o27EsLhWjKRC1s5ESEuP7woQeBtU4X5i6R0GDgOaJQQJ99ALACHYHv6XJ3w3AAABACOG3cLG
    api_version: "2023-05-15"
    model: azure/text-embedding-3-small
  model_info:
    mode: embedding
  model_name: text-embedding-3-small
- litellm_params:
    api_base: https://openrouter.ai/api/v1
    api_key: 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
    model: openrouter/anthropic/claude-3.7-sonnet:thinking
  model_name: claude-3.7-sonnet-think
- litellm_params:
    api_base: https://openrouter.ai/api/v1
    api_key: 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
    model: openrouter/anthropic/claude-3.7-sonnet
  model_name: claude-3.7-sonnet
- litellm_params:
    api_base: https://openrouter.ai/api/v1
    api_key: 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
    model: openrouter/google/gemini-2.5-pro-preview
  model_name: openrouter-gemini-2.5-pro-preview
- litellm_params:
    api_base: https://openrouter.ai/api/v1
    api_key: 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
    model: openrouter/google/gemini-2.5-flash-preview
  model_name: openrouter-gemini-2.5-flash-preview
- litellm_params:
    api_base: https://openrouter.ai/api/v1
    api_key: 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
    model: openrouter/google/gemini-2.5-flash-preview
  model_name: gemini-2.5-flash-preview
- litellm_params:
    api_base: https://openrouter.ai/api/v1
    api_key: 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
    model: openrouter/google/gemini-2.5-pro-preview
  model_name: gemini-2.5-pro-preview
- litellm_params:
    aws_access_key_id: ********************
    aws_region_name: us-west-2
    aws_secret_access_key: 0yu/P9qnYYGpfAZH5cM5oz5dlyTFcjkKF+diKRJ8
    model: bedrock/us.anthropic.claude-3-7-sonnet-********-v1:0
  model_info:
    supports_vision: true
  model_name: bedrock/us.anthropic.claude-3-7-sonnet-********-v1:0
- litellm_params:
    aws_access_key_id: ********************
    aws_region_name: us-west-2
    aws_secret_access_key: 0yu/P9qnYYGpfAZH5cM5oz5dlyTFcjkKF+diKRJ8
    model: bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0
  model_name: bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0
- litellm_params:
    aws_access_key_id: ********************
    aws_region_name: us-west-2
    aws_secret_access_key: 0yu/P9qnYYGpfAZH5cM5oz5dlyTFcjkKF+diKRJ8
    model: bedrock/us.anthropic.claude-opus-4-********-v1:0
  model_name: bedrock/us.anthropic.claude-opus-4-********-v1:0
- litellm_params:
    aws_access_key_id: ********************
    aws_region_name: us-west-2
    aws_secret_access_key: 0yu/P9qnYYGpfAZH5cM5oz5dlyTFcjkKF+diKRJ8
    model: bedrock/us.anthropic.claude-sonnet-4-********-v1:0
  model_name: bedrock/us.anthropic.claude-sonnet-4-********-v1:0
- litellm_params:
    model: vertex_ai/gemini-2.5-pro-preview-05-06
    vertex_credentials: /etc/litellm/vertex_ai_service_account.json
    vertex_location: us-central1
    vertex_project: clacky-456208
  model_name: vertex-gemini-2.5-pro-preview
- litellm_params:
    model: vertex_ai/gemini-2.5-flash-preview-04-17
    timeout: 120
    vertex_credentials: /etc/litellm/vertex_ai_service_account.json
    vertex_location: us-central1
    vertex_project: clacky-456208
  model_name: vertex-gemini-2.5-flash-preview
# router_settings:
  # fallbacks:
  # - bedrock/us.anthropic.claude-3-7-sonnet-********-v1:0:
  #   - gemini-2.5-pro-preview
  #   - claude-3.7-sonnet
  #   - vertex-gemini-2.5-pro-preview
  # - gemini-2.5-flash-preview:
  #   - vertex-gemini-2.5-flash-preview
  # - gemini-2.5-pro-preview:
  #   - claude-3.7-sonnet-think
  #   - vertex-gemini-2.5-pro-preview
  # timeout: 120
