fail_fast: true

exclude: (.idea|.deploy)

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: check-toml
      - id: check-yaml
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: debug-statements
      - id: detect-private-key

  - repo: https://github.com/tox-dev/pyproject-fmt
    rev: 2.2.4
    hooks:
      - id: pyproject-fmt

  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.20.2
    hooks:
      - id: validate-pyproject

  # - repo: https://github.com/executablebooks/mdformat
  #   rev: 0.7.17
  #   hooks:
  #     - id: mdformat
  #       additional_dependencies:
  #         - mdformat-mkdocs
  #         - mdformat-admon

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.6.8
    hooks:
      - id: ruff
        entry: ruff check
        types_or: [ python, pyi, jupyter ]
        args: [--fix, --exit-non-zero-on-fix]
      # - id: ruff-format
        # entry: ruff format
        # types_or: [ python, pyi, jupyter ]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.11.2
    hooks:
      - id: mypy
        additional_dependencies:
          [types-toml, types-pyyaml]
        entry: mypy .
        always_run: false
        pass_filenames: false
