[build-system]
build-backend = "poetry.core.masonry.api"
requires = [
  "poetry-core",
]

[tool.poetry]
name = "heracles"
version = "0.9.0"
description = "Clacky AI Agent"
authors = [
  "windy <<EMAIL>>",
]
license = "private"
readme = "README.md"
repository = "https://github.com/clacky-ai/clacky-ai-agent"

[tool.poetry.dependencies]
python = ">=3.11, <3.12"
python-dotenv = "^1.0.1"
fastapi = "^0.111.0"
aiohttp = "^3.10.5"
tenacity = "^9.0.0"
termcolor = "^2.4.0"
uvicorn = "^0.30.0"
PyJWT = "^2.10.1"
litellm = { git = "https://github.com/clacky-ai/litellm.git", branch = "fix/add_langfuse_cache_usage" }
jinja2 = "^3.1.4"
python-socketio = "5.11.4"
langfuse = "^2.36.1"
openai-function-calling = "2.2.0"
pydantic = "^2.8.2"
python-statemachine = "^2.3.4"
levenshtein = "^0.25.1"
redis = "^5.0.8"
pytest = "^8.2.2"
pytest-mock = "^3.14.0"
pytest-asyncio = "^0.23.7"
pytest-html = "^4.1.1"
pytest-cov = "^5.0.0"
pytest-xdist = "^3.6.1"
datasets = "^2.20.0"
qdrant-client = "^1.12.2"
fastembed = "^0.5.0"
pycryptodome = "^3.21.0"
json-repair = "^0.39.1"
numpydoc = "^1.8.0"
loguru = "^0.7.3"

[tool.poetry.group.dev.dependencies]
ruff = "^0.4.8"
pre-commit = "^3.7.1"
mypy = "^1.10.0"
deptry = "^0.21.1"
fire = "^0.7.0"
types-pyyaml = "^6.0.12.20241230"
pandas-stubs = "^2.2.3.241126"

[tool.coverage.report]
precision = 2
