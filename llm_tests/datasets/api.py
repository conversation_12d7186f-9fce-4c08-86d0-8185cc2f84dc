from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
from llm_tests.datasets.test_generator import TestGenerator

app = FastAPI(
    title="Test Generator API",
    description="API for generating test cases",
    version="1.0.0"
)

generator = TestGenerator()


class TestGenerationRequest(BaseModel):
    codezone_id: str
    test_category_index: int
    test_scenario_index: int
    test_identifier: str
    test_role_index: Optional[int] = None
    test_language: Optional[str] = None


class TestGenerationResponse(BaseModel):
    success: bool
    test_file: Optional[str] = None
    codezone_id: Optional[str] = None
    message: str


@app.get("/get-codezone-id")
async def get_codezone_id(request) -> str:
    """获取 CodeZone ID 的 API 端点"""
    codezone_id = await generator.get_codezone_id_from_playground(request.codezone_id)
    return codezone_id


@app.post("/generate-test", response_model=TestGenerationResponse)
async def generate_test(request: TestGenerationRequest) -> TestGenerationResponse:
    """生成测试用例的 API 端点"""
    result = await generator.generate_test(
        codezone_id=request.codezone_id,
        test_category_index=request.test_category_index,
        test_scenario_index=request.test_scenario_index,
        test_identifier=request.test_identifier,
        test_role_index=request.test_role_index,
        test_language=request.test_language
    )

    if not result['success']:
        raise HTTPException(status_code=400, detail=result['message'])

    return TestGenerationResponse(**result)


@app.get("/test-options")
async def get_test_options():
    """获取所有测试选项的 API 端点"""
    options = generator.options
    return {
        "test_categories": [
            {"index": i, "name": option["name"], "value": option["value"][0]}
            for i, option in enumerate(options.test_category_map)
        ],
        "test_scenarios": [
            {"index": i, "name": option["name"], "value": option["value"]}
            for i, option in enumerate(options.test_scenario_map)
        ],
        "test_roles": [
            {"index": i, "name": option["name"], "value": option["value"][0]}
            for i, option in enumerate(options.test_role_map)
        ],
        "test_languages": [
            {"index": i, "name": option["name"], "value": option["value"]}
            for i, option in enumerate(options.test_language_map)
        ]
    }
