import os
import random
import async<PERSON>
from datetime import datetime
from heracles.core.schema.test import MockSocketIOServer
from llm_tests.datasets import LLMTestDataset, CodeZone
from heracles.server.clacky.playground import Playground
from heracles.server.clacky.playground_channel import PlaygroundChannel
from heracles.server.clacky.playground_manager import PlaygroundManager
from heracles.core.logger import heracles_logger as logger
from heracles.core.config import get_env_var
from heracles.agent_workspace.paas_sdk.utils import (
    fork_codezone,
    create_codezone,
    bind_playground_info,
    import_repository_async,
    import_repository_task_check,
    upgrade_codezone_environment,
    ImportRepositoryInfo,
    get_playground_id
)
from llm_tests.datasets.test_templates import (
    SMOKE_TEMPLATE,
    SPEC_ROLE_TEMPLATE,
    CHAT_ROLE_TEMPLATE,
    PLAN_ROLE_TEMPLATE,
    CODE_ROLE_TEMPLATE,
    ANALYZE_ROLE_TEMPLATE
)
from typing import TypedDict, Optional


class SimpleOption(TypedDict):
    name: str
    value: str


class TupleValueOption(TypedDict):
    name: str
    value: tuple[str, str | None]


class TestOptions:
    """测试选项配置类"""
    def __init__(self):
        self.test_category_map: list[TupleValueOption] = [
            {
                'name': 'basic: 基础测试用例',
                'value': ('basic', None)
            },
            {
                'name': 'smoke: 冒烟测试用例',
                'value': ('smoke', SMOKE_TEMPLATE)
            }
        ]

        self.test_scenario_map: list[SimpleOption] = [
            {
                'name': 'init: 初始化环境',
                'value': 'init'
            },
            {
                'name': 'feat: 需求实现',
                'value': 'feat'
            },
            {
                'name': 'refactor: 重构',
                'value': 'refactor'
            },
            {
                'name': 'func: 基础功能操作',
                'value': 'func'
            }
        ]

        self.test_role_map: list[TupleValueOption] = [
            {
                'name': 'Chat Role',
                'value': ('chat_role', CHAT_ROLE_TEMPLATE)
            },
            {
                'name': 'Spec Role',
                'value': ('spec_role', SPEC_ROLE_TEMPLATE)
            },
            {
                'name': 'Plan Role',
                'value': ('plan_role', PLAN_ROLE_TEMPLATE)
            },
            {
                'name': 'Code Role',
                'value': ('code_role', CODE_ROLE_TEMPLATE)
            },
            {
                'name': 'Analyze Role',
                'value': ('analyze_role', ANALYZE_ROLE_TEMPLATE)
            }
        ]

        self.test_language_map: list[SimpleOption] = [
            {
                'name': 'Ruby',
                'value': 'ruby'
            },
            {
                'name': 'Python',
                'value': 'python'
            },
            {
                'name': 'HTML/CSS/JS',
                'value': 'html_css_js'
            },
            {
                'name': 'NodeJS',
                'value': 'nodejs'
            },
            {
                'name': 'Go',
                'value': 'go'
            },
            {
                'name': 'Java',
                'value': 'java'
            },
            {
                'name': 'C/C++',
                'value': 'c_c++'
            }
        ]


class TestGenerator:
    def __init__(self):
        self.options = TestOptions()
        self.dataset = LLMTestDataset()
        self.paas_env = get_env_var('PAAS_DOMAIN_URL', must=True).split('.')[0].replace('https://', '')
        self.socketio_server = MockSocketIOServer()
        self.playground_manager = PlaygroundManager()

    async def _generate_test_file_path(self, test_category: str, test_role: str | None, test_scenario: str,
                                     test_language: str | None, test_identifier: str) -> str:
        """生成测试文件路径"""
        if test_category == 'basic':
            test_dir = f"llm_tests/{test_category}/{test_role}"
            test_file = f"{test_dir}/test_{test_role}_for_{test_scenario}.py"
            if not os.path.exists(test_file):
                raise Exception(f"测试用例文件不存在: {test_file}，请先创建后重试！")
        else:  # smoke
            test_dir = f"llm_tests/{test_category}/{test_language}"
            test_file = f"{test_dir}/test_{test_scenario}_{test_identifier}.py"
            while os.path.exists(test_file):
                test_file = test_file.replace('.py', f'_{random.randint(1, 100)}.py')
        return test_file

    async def _fork_codezone(self, codezone_id: str) -> str:
        """Fork CodeZone并返回新ID"""
        response = await fork_codezone(codezone_id)
        logger.warning(f"Fork CodeZone: {response}")
        new_codezone_id = response['data'].get('id')
        if not new_codezone_id:
            raise Exception("Fork CodeZone 失败")
        logger.info(f"已 Fork 新 CodeZone: {new_codezone_id}")
        return new_codezone_id

    async def get_codezone_id_from_playground(self, playground_id: str) -> str:
        """从 Playground 获取 CodeZone ID"""
        playground_info = await bind_playground_info(playground_id)
        if playground_info['success']:
            codezone_id = playground_info['data']['codeZoneId']
            logger.info(f"Playground 有效，已获取 CodeZone: {codezone_id}")
            return codezone_id
        else:
            raise Exception(f"Playground 无效: {playground_info['data']}")

    async def create_codezone_by_specific_repo_path(self, owner: str, repo: str, branch_name: str, path: str, env_code: str):
        """根据特定 repo 路径创建 CodeZone"""
        result = await create_codezone(environment_ver_id="403710022250528768")  # blank 环境，固定不变
        if result is not None:
            codeZoneId = result["data"]["id"]
            playground_result = await get_playground_id(codeZoneId)
            playground_id = playground_result["data"]["id"]
            upgrade_result = await upgrade_codezone_environment(codeZoneId, env_code)
            logger.warning(f"CodeZone ID: {codeZoneId}, Playground ID: {playground_id}")

            playground = Playground(playground_id, self.socketio_server)
            self.playground_manager.add_playground(playground)
            playground_channel = PlaygroundChannel(f'sid-{playground_id}', self.socketio_server, self.playground_manager, playground_id)
            await playground_channel.start()
            playground = playground_channel.current_playground
            workspace = playground.agent_controller.workspace

            await workspace.tools.run_cmd(
                f"mkdir /tmp/temp_dir && git clone --depth 1 --branch {branch_name} https://github.com/{owner}/{repo}.git /tmp/temp_dir && cp -r /tmp/temp_dir/{path}/* . && rm -rf /tmp/temp_dir"  # noqa: E501
            )
            logger.info(f"已克隆 repo: {owner}/{repo}, path: {path}")
            return CodeZone(
                id=codeZoneId,
                playground_id=playground_id,
                environment_ver_id=upgrade_result["data"].get("environmentVerId"),
                environment_name=env_code,
                updated_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )
        else:
            raise Exception(f"创建 CodeZone 失败: {result}")

    async def create_codezone_by_specific_repo_commit(self, owner: str, repo: str, commit_id: str, env_code: str):
        """根据特定 repo 的 commit_id 创建 CodeZone"""
        result = await create_codezone(environment_ver_id="403710022250528768")  # blank 环境，固定不变
        if result is not None:
            codeZoneId = result["data"]["id"]
            playground_result = await get_playground_id(codeZoneId)
            playground_id = playground_result["data"]["id"]
            upgrade_result = await upgrade_codezone_environment(codeZoneId, env_code)
            logger.warning(f"CodeZone ID: {codeZoneId}, Playground ID: {playground_id}")

            playground = Playground(playground_id, self.socketio_server)
            self.playground_manager.add_playground(playground)
            playground_channel = PlaygroundChannel(f'sid-{playground_id}', self.socketio_server, self.playground_manager, playground_id)
            await playground_channel.start()
            playground = playground_channel.current_playground
            workspace = playground.agent_controller.workspace

            await workspace.tools.run_cmd(
                f"git init && git remote add origin https://github.com/{owner}/{repo}.git && git fetch origin {commit_id} && git clean -fd && git checkout FETCH_HEAD"  # noqa: E501
            )
            logger.info(f"已克隆 repo: {owner}/{repo} commit_id {commit_id}")
            return CodeZone(
                id=codeZoneId,
                playground_id=playground_id,
                environment_ver_id=upgrade_result["data"].get("environmentVerId"),
                environment_name=env_code,
                updated_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

    async def create_codezone_by_repo(self, owner: str, repo: str, branch_name: str, env_code: str):
        """根据 repo 创建 CodeZone"""

        async def import_repo_task_check(task_id: str):
            timeout = 30
            start_time = 0
            while True:
                if start_time > timeout:
                    raise TimeoutError(f"导入仓库任务超时: {task_id}")
                result = await import_repository_task_check(task_id=task_id)
                logger.debug(f"import_repo_task_check: {result}")
                if result['success'] and result["data"] is not None:
                    if result["data"]["progress"] == "COMPLETED":
                        return True
                    elif result["data"]["progress"] == "FAILED":
                        raise Exception(f"导入仓库任务失败: {task_id}: {result['data']['logs']}")
                await asyncio.sleep(1)
                start_time += 1

        import_repository_info = ImportRepositoryInfo(
            environmentVerId="403710022250528768",  # blank 环境，固定不变
            owner=owner, repo=repo, ref=branch_name, username=owner,
            purpose="1", token="", privateKey="test"
        )
        result = await import_repository_async(import_repository_info)
        if result["success"] and result["data"] is not None:
            codeZoneId = result["data"]["codeZoneId"]
            taskId = result["data"]["taskId"]
            playground_result = await get_playground_id(codeZoneId)
            logger.warning(f"CodeZone ID: {codeZoneId}, Playground ID: {playground_result['data']['id']}")
            check_result = await import_repo_task_check(taskId)
            if check_result:
                upgrade_result = await upgrade_codezone_environment(codeZoneId, env_code)
                if upgrade_result["success"]:
                    logger.info(f"升级 CodeZone 环境成功: {upgrade_result['data']}")
                    return CodeZone(
                        id=upgrade_result["data"].get("id"),
                        playground_id=playground_result["data"]["id"],
                        environment_ver_id=upgrade_result["data"].get("environmentVerId"),
                        environment_name=upgrade_result["data"].get("environmentName"),
                        updated_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    )

    async def generate_new_codezone_by_state_id(self, state_id: str):
        """根据 state_id 生成新的 CodeZone"""

        project = self.dataset.get_project_by_state_id(state_id)
        if project is None:
            raise Exception(f"项目不存在: {state_id}")
        state = self.dataset.states[state_id]
        owner, repo = project.github_repo.split("/")

        if state.commit_id:
            codezone = await self.create_codezone_by_specific_repo_commit(owner, repo, state.commit_id, project.env_code)
        elif state.branch_name:
            if state.path:
                codezone = await self.create_codezone_by_specific_repo_path(owner, repo, state.branch_name, state.path, project.env_code)
            else:
                codezone = await self.create_codezone_by_repo(owner, repo, state.branch_name, project.env_code)
        else:
            raise Exception(f"项目 {state_id} 没有有效的分支或 commit_id")
        return codezone

    async def generate_test(self,
                          codezone_id: str,
                          test_category_index: int,
                          test_scenario_index: int,
                          test_identifier: str,
                          test_role_index: Optional[int] = None,
                          test_language: Optional[str] = None) -> dict:
        """生成测试的核心逻辑"""
        try:
            # 获取测试分类和模板
            if not (0 <= test_category_index < len(self.options.test_category_map)):
                raise Exception("无效的测试用例分类索引")
            test_category, template = self.options.test_category_map[test_category_index]['value']

            # 获取测试场景
            if not (0 <= test_scenario_index < len(self.options.test_scenario_map)):
                raise Exception("无效的测试用例场景索引")
            test_scenario = self.options.test_scenario_map[test_scenario_index]['value']

            # 根据测试分类获取额外信息
            test_role = None
            if test_category == 'basic':
                if test_role_index is None or not (0 <= test_role_index < len(self.options.test_role_map)):
                    raise Exception("基础测试需要有效的测试角色索引")
                test_role, template = self.options.test_role_map[test_role_index]['value']
                title = test_role.replace('_', ' ').title() + ': ' + test_identifier.replace('_', ' ') + "/" + test_scenario
            elif test_category == 'smoke':
                if not test_language:
                    raise Exception("冒烟测试需要指定测试语言")
                title = "Smoke: " + test_language + "/" + test_identifier.replace('_', ' ') + "/" + test_scenario
            else:
                raise Exception("无效的测试用例分类")

            # 生成测试文件路径
            test_file = await self._generate_test_file_path(
                test_category, test_role, test_scenario, test_language, test_identifier
            )

            # Fork CodeZone
            codezone_id = await self._fork_codezone(codezone_id)

            # 写入测试用例文件
            if template is not None:
                test_content = template.format(
                    test_identifier=test_identifier,
                    test_scenario=test_scenario,
                    title=title,
                    codezone_id=codezone_id
                )
                with open(test_file, "a") as f:
                    f.write(test_content)

            logger.info(f"已生成测试用例文件: {test_file}")

            return {
                "success": True,
                "test_file": test_file,
                "codezone_id": codezone_id,
                "message": "测试用例生成成功"
            }

        except Exception as e:
            logger.error(f"生成测试时发生错误: {str(e)}")
            return {
                "success": False,
                "message": str(e)
            }

    async def update_codezone_data(self, state_id: Optional[str] = None, skip_existing: bool = True):
        """更新测试数据"""
        states_data = self.dataset.states
        codezones_data = self.dataset.codezones
        need_update_count = 0
        errors = []

        async def _update_single_codezone(state_id: str) -> bool:
            if state_id is not None and self.dataset.get_project_by_state_id(state_id) is None:
                logger.warning(f"所属项目不存在: {state_id}")
                return False
            if skip_existing and state_id in codezones_data and self.paas_env in codezones_data[state_id]:
                logger.warning(f"CodeZone 已存在: {state_id}，跳过更新")
                return False

            try:
                logger.warning(f"开始生成 CodeZone: {state_id}")
                codezone = await self.generate_new_codezone_by_state_id(state_id)
                if codezone is not None:
                    if state_id not in codezones_data:
                        codezones_data[state_id] = {}
                    codezones_data[state_id][self.paas_env] = codezone

                    # 运行定义的指定 steps 脚本
                    state = states_data[state_id]
                    if state.run_steps:
                        playground_id = codezone.playground_id
                        playground = Playground(playground_id, self.socketio_server)
                        self.playground_manager.add_playground(playground)
                        playground_channel = PlaygroundChannel(
                            f'sid-{playground_id}', self.socketio_server, self.playground_manager, playground_id
                        )
                        await playground_channel.start()
                        playground = playground_channel.current_playground
                        workspace = playground.agent_controller.workspace
                        for step in state.run_steps:
                            logger.info(f"运行步骤: {step}")
                            await workspace.tools.run_cmd(step)
                    return True
                else:
                    logger.warning(f"CodeZone 生成失败: {state_id}")
                    return False
            except Exception as e:
                logger.error(f"更新 CodeZone 失败: {state_id}，错误信息: {e}")
                errors.append(f"{state_id}: {e}")
                return False

        if state_id is None:
            for state in states_data.values():
                if await _update_single_codezone(state.id):
                    need_update_count += 1
        else:
            if await _update_single_codezone(state_id):
                need_update_count += 1

        if need_update_count > 0:
            self.dataset.persist_codezones()
            logger.info(f"成功更新 {need_update_count} 个 CodeZone 数据")
        if errors:
            error_message = "\n".join([f"- {error}" for error in errors])
            logger.error(f"以下 CodeZone 更新失败: \n{error_message}")
