SMOKE_TEMPLATE = """import pytest

@pytest.mark.asyncio
async def test_{test_scenario}_{test_identifier}(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="{title}",
        description="",
        codezone_id="{codezone_id}",
        codezone_description="",
        codezone_git_url="",
        goal="",
        goal_detail="",
        expectations=[],
    )

    await run_whole_process(test_data)
"""

SPEC_ROLE_TEMPLATE = """
@pytest.mark.asyncio
async def test_spec_role_{test_scenario}_{test_identifier}(create_role, create_test_data):
    test_data = create_test_data(
        title="{title}",
        codezone_id="{codezone_id}",
        codezone_git_url="",
        goal="",
        goal_detail="",
        expectations=[]
    )

    spec_role, context = await create_role(test_data, SpecRole)
    await spec_role.run(test_data.goal, test_data.goal_detail)

    final_score = await SpecRoleScoreRule(spec_role.workspace).execute_rules_and_score('all')
    context.score_model = final_score
    return final_score
"""

CHAT_ROLE_TEMPLATE = """
@pytest.mark.asyncio
async def test_chat_role_{test_scenario}_{test_identifier}(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='{title}',
        codezone_id='{codezone_id}',
        codezone_git_url='',
        expectations = []
    )
    chat_role, context = await create_role(test_basic_data, ChatRole)

    res = await chat_role.run("这是什么项目？")
    final_score = await ChatRoleScoreRule(chat_role.workspace, res, test_basic_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score
"""

PLAN_ROLE_TEMPLATE = """
@pytest.mark.asyncio
async def test_plan_role_{test_scenario}_{test_identifier}(create_role, create_test_data):
    test_data = create_test_data(
        title="{title}",
        codezone_id="{codezone_id}",
        codezone_git_url="",
        goal="",
        goal_detail="",
        proposed_list=[],
        expectations=[]
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)
    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(spec.goal, spec.goal_detail, spec.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, test_data.expectations).execute_rules_and_score('all')
    context.score_model = final_score
    return final_score
"""

CODE_ROLE_TEMPLATE = """
@pytest.mark.asyncio
async def test_code_role_{test_scenario}_{test_identifier}(create_role, create_test_data):
    test_data = create_test_data(
        title="{title}",
        codezone_id="{codezone_id}",
        codezone_git_url="",
    )

    task = Task(
        title="",
        description="",
        task_steps=[
            TaskStep(
                title="",
                description="",
                task_actions=[
                    TaskAction(
                        title="",
                        action=ActionType.ADD_FILE,
                        action_object=FileActionObject(
                            path='test.txt',
                            detailed_requirement='the content is "test"'
                        )
                    )
                ]
            )
        ]
    )

    task_action = TaskAction(
        title="",
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path='test.txt',
            detailed_requirement='the content is "test"'
        )
    )

    code_role, context = await create_role(test_data, CodeRole)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score
"""

ANALYZE_ROLE_TEMPLATE = """
@pytest.mark.asyncio
async def test_analyze_role_{test_scenario}_{test_identifier}(create_role, create_test_data):
    test_data = create_test_data(
        title="{title}",
        description="",
        codezone_id="{codezone_id}",
        codezone_description="",
        codezone_git_url="",
        goal="",
        goal_detail="",
        expectations=[],
        proposed_list=[]
    )

    analyze_role, context = await create_role(test_data, AnalyzeRole)
    await analyze_role.run(test_data.goal, test_data.goal_detail)

    final_score = await AnalyzeRoleScoreRule(analyze_role.workspace).execute_rules_and_score('all')
    context.score_model = final_score
    return final_score
"""
