import fire
from typing import Optional
from termcolor import colored
from heracles.core.logger import heracles_logger as logger
from llm_tests.datasets.test_generator import TestGenerator, TestOptions


def prompt_for_response(prompt: str) -> str:
    """用户输入并返回响应"""
    print(colored(f"\n{prompt}", 'cyan'), end='\n')
    print(colored("(empty or 'n' to cancel): ", 'yellow'), end='')
    response = input().strip()
    if response.lower() == 'n' or not response:
        raise Exception("用户已取消操作")
    return response


def confirm_step(step_name: str) -> bool:
    """用户确认是否继续执行下一步"""
    print(colored(f"\n是否执行 {step_name}?", 'cyan'), end=' ')
    print(colored("(y/N): ", 'yellow'), end='')
    response = input().lower().strip()
    if response in ('y', 'yes'):
        return True
    raise Exception("用户已取消操作")


class TestGeneratorCLI:
    def __init__(self):
        self.generator = TestGenerator()
        self.options = TestOptions()

    def _get_test_category(self) -> tuple[int, tuple[str, str | None]]:
        """获取测试用例分类"""
        prompt = "请输入测试用例分类：\n"
        for i, option in enumerate(self.options.test_category_map, 1):
            prompt += f"{i}. {option['name']}\n"
        response = prompt_for_response(prompt.rstrip())
        try:
            index = int(response) - 1
            if 0 <= index < len(self.options.test_category_map):
                return index, self.options.test_category_map[index]['value']
        except ValueError:
            pass
        raise Exception("无效的测试用例分类")

    def _get_test_scenario(self) -> tuple[int, str]:
        """获取测试用例场景"""
        prompt = "请输入测试用例场景：\n"
        for i, option in enumerate(self.options.test_scenario_map, 1):
            prompt += f"{i}. {option['name']}\n"
        response = prompt_for_response(prompt.rstrip())
        try:
            index = int(response) - 1
            if 0 <= index < len(self.options.test_scenario_map):
                return index, self.options.test_scenario_map[index]['value']
        except ValueError:
            pass
        raise Exception("无效的测试用例场景")

    def _get_test_role(self) -> tuple[int, tuple[str, str | None]]:
        """获取测试用例角色"""
        prompt = "请输入测试用例角色：\n"
        for i, option in enumerate(self.options.test_role_map, 1):
            prompt += f"{i}. {option['name']}\n"
        response = prompt_for_response(prompt.rstrip())
        try:
            index = int(response) - 1
            if 0 <= index < len(self.options.test_role_map):
                return index, self.options.test_role_map[index]['value']
        except ValueError:
            pass
        raise Exception("无效的测试用例角色")

    def _get_test_language(self) -> str:
        """获取测试用例语言"""
        prompt = "请输入测试用例语言：\n"
        for i, option in enumerate(self.options.test_language_map, 1):
            prompt += f"{i}. {option['name']}\n"
        prompt += "或者输入其他语言"
        response = prompt_for_response(prompt)
        try:
            index = int(response) - 1
            if 0 <= index < len(self.options.test_language_map):
                return self.options.test_language_map[index]['value']
        except ValueError:
            pass
        return response

    async def get_codezone_id(self):
        """获取 CodeZone ID 的命令行入口"""
        playground_id = prompt_for_response("请输入 Playground ID")
        codezone_id = await self.generator.get_codezone_id_from_playground(playground_id)
        print(f"CodeZone ID: {codezone_id}")
        return codezone_id

    async def generate_test(self):
        """生成测试的命令行入口"""
        try:
            # 获取 Playground ID
            codezone_id = await self.get_codezone_id()
            # 获取测试分类
            category_index, (test_category, _) = self._get_test_category()
            # 获取测试场景
            scenario_index, _ = self._get_test_scenario()
            # 获取测试标识符
            test_identifier = prompt_for_response(
                "请输入测试用例标识符, 英文, 框架名_项目名_功能名_其他。例如 nextjs_ragbot_starter_add_counter"
            )
            # 根据测试分类获取额外信息
            test_role_index = None
            test_language = None
            if test_category == 'basic':
                test_role_index, _ = self._get_test_role()
            elif test_category == 'smoke':
                test_language = self._get_test_language()

            # 调用生成器
            result = await self.generator.generate_test(
                codezone_id=codezone_id,
                test_category_index=category_index,
                test_scenario_index=scenario_index,
                test_identifier=test_identifier,
                test_role_index=test_role_index,
                test_language=test_language
            )
            if result['success']:
                logger.info(f"测试用例生成成功：{result['test_file']}")
                print(f"\n[{colored('INFO', 'green')}] 你可以通过以下命令运行：\n\npoetry run pytest -s {result['test_file']}\n\n")
            else:
                logger.error(f"生成测试失败：{result['message']}")

        except Exception as e:
            logger.error(f"生成测试时发生错误: {str(e)}")

    async def update_codezone_data(self, state_id: Optional[str] = None):
        """更新测试数据"""
        await self.generator.update_codezone_data(state_id)


def main():
    TestGeneratorCLI()


if __name__ == "__main__":
    fire.Fire(TestGeneratorCLI)
