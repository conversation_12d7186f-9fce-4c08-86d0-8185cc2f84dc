import json
import yaml
from pydantic import BaseModel, Field
from typing import Optional, List

# from heracles.core.schema.knowledge import ProjectKnowledge
from heracles.core.config import get_env_var
from heracles.core.logger import heracles_logger as logger

class CodeZone(BaseModel):
    """CodeZone: 代码状态码区"""

    id: str = Field(description='代码状态码区ID')
    playground_id: str = Field(description='Playground ID')
    environment_ver_id: str = Field(description='环境版本ID')
    environment_name: str = Field(description='环境名称')
    updated_at: str = Field(description='更新时间')

class ProjectState(BaseModel):
    """ProjectStateCodeZone: 项目代码状态的 CodeZone 快照"""

    id: str
    branch_name: Optional[str] = Field(description='分支名称', default=None)
    path: Optional[str] = Field(description='代码路径', default=None)
    commit_id: Optional[str] = Field(description='commit_id', default=None)
    description: str = Field(description='对于代码状态的描述', default='')
    environment_inited: bool = Field(description='环境是否已初始化', default=False)
    project_id: str = Field(description='项目ID')
    run_steps: List[str] = Field(description='初始化环境步骤', default=[])

class Project(BaseModel):
    """Project: 项目结构"""

    id: str = Field(description='项目ID，github_repo')
    name: str = Field(description='项目名称')
    description: str = Field(description='项目描述')
    github_repo: str = Field(description='项目Git URL')
    env_code: str = Field(description='项目环境代码')
    knowledge: Optional[dict] = Field(description='项目基础知识信息', default=None)

class LLMTestDataset(BaseModel):
    """LLMTestDataset: LLM 测试数据集"""

    projects: dict[str, Project] = Field(description='项目', default={})
    codezones: dict[str, dict[str, CodeZone]] = Field(description='代码状态码区', default={})
    states: dict[str, ProjectState] = Field(description='项目状态码区', default={})

    def __init__(self):
        super().__init__()
        self.projects, self.states = self._load_projects()
        self.codezones = self._load_codezones()
        logger.info(f"[LLMTestDataset] {len(self.projects)} projects, {len(self.codezones)} codezones loaded")

    def _load_projects(self):
        projects = {}
        states = {}
        with open('llm_tests/datasets/github_repos/projects.yml', 'r') as f:
            projects_list = yaml.load(f, Loader=yaml.SafeLoader)
            for project_data in projects_list:
                project = Project(id=f"{project_data['github_repo']}-{project_data['env_code']}", **project_data)
                if project.id in projects:
                    raise Exception(f"项目ID(github repo)重复: {project.id}")
                projects[project.id] = project
                for state in project_data['states']:
                    state['project_id'] = project.id
                    states[state['id']] = ProjectState(**state)
        return projects, states

    def _load_codezones(self):
        codezones: dict[str, dict[str, CodeZone]] = {}
        with open('llm_tests/datasets/github_repos/codezones.json', 'r') as f:
            codezones_list = json.load(f)
            for state_id, codezones_data in codezones_list.items():
                for paas_env, codezone_data in codezones_data.items():
                    codezone = CodeZone(
                        id=codezone_data['codezone_id'],
                        playground_id=codezone_data['playground_id'],
                        environment_ver_id=codezone_data['environment_ver_id'],
                        environment_name=codezone_data['environment_name'],
                        updated_at=codezone_data['updated_at']
                    )
                    codezones[state_id] = codezones.get(state_id, {})
                    codezones[state_id][paas_env] = codezone
        return codezones

    def get_project_by_state_id(self, state_id: str) -> Optional[Project]:
        state = self.states.get(state_id)
        if not state:
            return None
        return self.projects.get(state.project_id)

    def get_project(self, project_id: str) -> Optional[Project]:
        return self.projects.get(project_id)

    def get_codezone_id_by_state_id(self, state_id: str) -> Optional[CodeZone]:
        paas_env = get_env_var('PAAS_DOMAIN_URL', must=True).split('.')[0].replace('https://', '')
        codezones = self.codezones.get(state_id) or {}
        return codezones.get(paas_env)

    def persist_codezones(self):
        with open('llm_tests/datasets/github_repos/codezones.json', 'w') as f:
            codezones_dict = {
                state_id: {
                    env: {
                        'codezone_id': codezone.id,
                        'playground_id': codezone.playground_id,
                        'environment_ver_id': codezone.environment_ver_id,
                        'environment_name': codezone.environment_name,
                        'updated_at': codezone.updated_at
                    } for env, codezone in env_data.items()
                } for state_id, env_data in self.codezones.items()
            }
            json.dump(codezones_dict, f, indent=4)

class LLMTestSWEDataset(LLMTestDataset):
    """LLMTestSWEDataset: SWE LLM 测试数据集"""

    def _load_codezones(self):
        codezones: dict[str, dict[str, CodeZone]] = {}
        with open('llm_tests/datasets/swe/codezones.json', 'r') as f:
            codezones_list = json.load(f)
            for instance_id, codezones_data in codezones_list.items():
                for paas_env, codezone_data in codezones_data.items():
                    codezone = CodeZone(
                        id=codezone_data['codezone_id'],
                        playground_id=codezone_data['playground_id'],
                        environment_ver_id=codezone_data['environment_ver_id'],
                        environment_name=codezone_data['environment_name'],
                        updated_at=codezone_data['updated_at']
                    )
                    codezones[instance_id] = codezones.get(instance_id, {})
                    codezones[instance_id][paas_env] = codezone
        return codezones
