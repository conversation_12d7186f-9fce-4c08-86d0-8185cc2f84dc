from datetime import date
from langfuse import <PERSON><PERSON>
from llm_tests.utils.schema import ScoreModel
from heracles.agent_controller.llm import LLMAbilityType
from heracles.core.schema import Lang<PERSON>TraceOption
from heracles.agent_roles.check_role import CheckRole
from llm_tests.llm_speed_collector import llm_speed_collector

def get_today_tag():
    today = date.today()
    formatted_date = today.strftime('%m%d')
    return formatted_date


def update_trace_tag(workspace, tag_name: str, test_title: str):
    """更新 trace 信息"""
    if not workspace:
        # logger.error('workspace is none, update_trace_tag is ignored')
        return
    langfuse = Langfuse()
    langfuse.trace(id=workspace.playground.playground_id, name=test_title, tags=[tag_name, get_today_tag()])
    llm_speed_collector.register_trace_mapping(workspace.playground.playground_id, test_title)


def create_trace_score(workspace, value: float, comment: str, name: str = 'correctness') -> None:
    """设定 trace 打分"""
    if not workspace:
        # logger.error('workspace is none, create_trace_score is ignored')
        return
    langfuse = Langfuse()
    langfuse.score(
        trace_id=workspace.playground.playground_id,
        name=name,
        value=value,
        data_type='NUMERIC',
        comment=comment,
    )


async def custom_score_result(workspace, message, strong_llm=False) -> ScoreModel:
    """调用 LLM 进行打分"""
    langfuse_option = LangfuseTraceOption(trace_id=workspace.playground.playground_id, generation_name='CustomScoreResult')
    check_role = CheckRole(workspace)
    llm = check_role.llm
    llm.ability_type = LLMAbilityType.STRONG if strong_llm else LLMAbilityType.NORMAL
    llm.set_llm_metadata(langfuse_option)
    check_role.system_prompt = """
You are a score checker, you will be given a task result and a score expectation, you need to check if the task result meets the expectation.

You should return a ScoreModel with the following fields:
- value: float, the score, should be 0 or 1
- comment: str, the comment of the score, should be in Chinese

You should not use multiple tool calls, return one ScoreModel only.
"""  # noqa: E501
    score_model = await check_role.aask(
        message,
        response_model=ScoreModel,
    )
    return score_model


async def score_result(workspace, expect, result, strong_llm=False) -> ScoreModel:
    """调用 LLM 进行打分"""
    langfuse_option = LangfuseTraceOption(trace_id=workspace.playground.playground_id, generation_name='ScoreResult')
    prompt = """
Given a task result:
```
{result}
```

Please rate by following rules, score should be 0 or 1, and explain why in Chinese.
{expect}

- Do not use multiple tool calls, return one ScoreModel only.
"""
    check_role = CheckRole(workspace)
    llm = check_role.llm
    llm.ability_type = LLMAbilityType.STRONG if strong_llm else LLMAbilityType.NORMAL
    llm.set_llm_metadata(langfuse_option)
    check_role.system_prompt = """
You are a score checker, you will be given a task result and a score expectation, you need to check if the task result meets the expectation.

You should return a ScoreModel with the following fields:
- value: float, the score, should be 0 or 1
- comment: str, the comment of the score, should be in Chinese

You should not use multiple tool calls, return one ScoreModel only.
"""  # noqa: E501
    score_model = await check_role.aask(
        prompt.format(expect=expect, result=result),
        response_model=ScoreModel,
    )
    return score_model
