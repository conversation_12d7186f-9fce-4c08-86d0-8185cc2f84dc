# 项目 LLM 表现基准测试

## 分类

分为 basic, rag, smoke, external 三类测试

### Basic( 基础测试 )

```bash
$ poetry run pytest -s llm_tests/basic
```

用于 Role 本身功能的测试与验证

一是可以帮助调试开发真实环境下 Role 在 LLM 下的表现.

二是可以在跑 smoke 前, 进行一轮基础能力验证.

### Rag

```bash
$ poetry run pytest -s llm_tests/rag

# 可选测试对照组
# RAG_SCORE_MODE: llm | recall, default: recall 控制评分模式，默认通过召回率评分
$ RAG_SCORE_MODE=llm poetry run pytest -s llm_tests/rag
# QUESTION_EXPAND: y | n, default: y 控制使用 llm 对 search_question 进行扩展， 默认扩展
$ QUESTION_EXPAND=n poetry run pytest -s llm_tests/rag
```

Rag 相关的测试

### Smoke( 冒烟测试 )

```bash
$ poetry run pytest -s llm_tests/smoke
```

#### 支持环境变量

`AUTO_SCORE`（Check Role 对成果自动进行评分）, `WITH_RAG`（是否有真实的 RAG 参与，未开启则返回空值）

> 开启时使用 “true”, “True”, “1” 都可以，默认均为 “False”（未开启）

```bash
$ AUTO_SCORE=1 poetry run pytest -s llm_tests/smoke/next_js/test_chatbot_init.py::test_chatbot
```

#### 生成HTML报告

支持指定路径（例如 `report.html`）

```bash
$ AUTO_SCORE=1 poetry run pytest -s llm_tests/smoke/next_js/test_chatbot_init.py::test_chatbot --html=report.html
```


内部快速的基准测试

一是用例不多, 但都非常重要, 可以快速进行一轮验证, 确保效果.

二是每次发布前都应该跑一轮, 生成测试报告.

### external( 外部基准测试 )

```bash
$ poetry run pytest -s llm_tests/external
```

把外部的 SWE-benmark 进行验证, 完整但跑起来也会慢一些.

## 重要说明

系统中实现了几个重要的数据结构与 fixture, 便于进行测试, 每个重要的测试都有注释说明

High-level Fixture:

```
run_whole_process: 完整跑整个项目( for smoke )
run_whole_rag_process; RAG跑流程( for rag )
create_role: 创建 Role 角色( for basic )
```

Low-level Fixture( 平时不需要使用 ):

```
create_playground_channel: 构建真实 playground
create_workspace: 构建真实 workspace
```

测试用例数据结构( 整合到 Langfuse, 便于阅读与分析 ):

```
TestData: 标准用例结构, 用于完整项目运行
TestRagData: Rag专用结构
ScoreModel: 评分结构
```

Helper 方法:

```
update_trace_tag(): 更新 langfuse trace 信息
create_trace_score(): 设定 langfuse 分数
```
