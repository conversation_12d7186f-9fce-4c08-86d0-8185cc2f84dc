import pytest
import json
import pandas as pd

# 使用pandas加载swe bench lite数据集
splits = {'dev': 'data/dev-00000-of-00001.parquet', 'test': 'data/test-00000-of-00001.parquet'}
df = pd.read_parquet('hf://datasets/princeton-nlp/SWE-bench_Lite/' + splits['test'])


def load_swe_instance_ids():
    with open('llm_tests/datasets/swe/codezones.json') as f:
        data = json.load(f)
        data.pop('base_codezone_env')
        return list(data.keys())


@pytest.mark.parametrize('instance_id', load_swe_instance_ids())
@pytest.mark.asyncio
async def test_swe_bench(instance_id, create_swe_test_data, run_swe_whole_process):
    row = df.loc[df['instance_id'] == instance_id]
    if len(row) == 0:
        raise Exception('swe data not found')
    swe_data = row.iloc[0]
    test_data = create_swe_test_data(
        title=instance_id,
        project_state_id=instance_id,
        description=swe_data['problem_statement'],
        goal=swe_data['problem_statement']
    )

    await run_swe_whole_process(test_data)
