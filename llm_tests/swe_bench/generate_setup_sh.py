import os
from typing import Any
from functools import reduce
import shutil


def get_directory_structure(rootdir):
    """
    递归获取目录结构并返回一个字典
    """
    dir_structure: dict[str, Any] = {}
    rootdir = rootdir.rstrip(os.sep)
    start = rootdir.rfind(os.sep) + 1
    for path, _, files in os.walk(rootdir):
        folders = path[start:].split(os.sep)
        subdir = dict.fromkeys(files)
        parent = reduce(lambda d, k: d[k], folders[:-1], dir_structure)
        parent[folders[-1]] = subdir
    return dir_structure


rootdir = './logs_lite'
dir_json = get_directory_structure(rootdir)
sweb_lite_setup_dir = 'sweb_lite_setup_scripts'

if __name__ == '__main__':
    for k in dir_json['logs_lite']['build_images']['instances']:
        with open(f'./logs_lite/build_images/instances/{k}/Dockerfile', 'r') as f:
            instance_dockerfile_content = f.read()

        start_pos = len('FROM --platform=linux/x86_64 ')
        assert instance_dockerfile_content.splitlines()[0].startswith('FROM --platform=linux/x86_64 ')
        env_dir_name = instance_dockerfile_content.splitlines()[0][start_pos:].replace(':', '__')
        assert env_dir_name in dir_json['logs_lite']['build_images']['env']

        tname = k.removeprefix('sweb.eval.x86_64.').removesuffix('__latest')
        output_dir = os.path.join(sweb_lite_setup_dir, tname)
        os.makedirs(output_dir, exist_ok=True)
        shutil.copy2(f'./logs_lite/build_images/env/{env_dir_name}/setup_env.sh', f'{output_dir}/setup_env.sh')
        shutil.copy2(f'./logs_lite/build_images/instances/{k}/setup_repo.sh', f'{output_dir}/setup_repo.sh')
        shutil.copy2(f'./logs_lite/run_evaluation/validate-gold/gold/{tname}/eval.sh', f'{output_dir}/eval.sh')
