生成swe测试用例的相关脚本

> note: 脚本读取.env里的PAAS_DOMAIN_URL获取环境信息，要切换环境需要修改.env

1 环境准备脚本，如果codezone环境已经准备好，可以直接跳到第2步

###### 准备官方脚本
1. 克隆 https://github.com/swe-bench/SWE-bench，切到项目根目录
2. 生成官方脚本
注释掉构建镜像的代码, 执行
```
python -m swebench.harness.run_evaluation --predictions_path gold --dataset_name princeton-nlp/SWE-bench_Lite --split test --run_id validate-gold --force_rebuild true
```
3.提取需要的脚本
将生成logs目录, 重命名为logs_lite, 执行
```
python generate_setup_sh.py
```
会生成脚本到sweb_lite_setup_scripts
然后复制到到clacky的llm_tests/swe_bench/sweb_lite_setup_scripts/

###### 配置基础环境
```
python llm_tests/swe_bench/setup_codezone_env.py
```
setup_codezone_env.py 生成instance对应的codezone

###### 生成测试用例配置
```
python llm_tests/swe_bench/generate_swe_config.py
```
执行这个脚本会把配置写到
llm_tests/datasets/swe/codezones.json

2 执行单元测试
```
pytest llm_tests/swe_bench/test_swe_bench.py
```
