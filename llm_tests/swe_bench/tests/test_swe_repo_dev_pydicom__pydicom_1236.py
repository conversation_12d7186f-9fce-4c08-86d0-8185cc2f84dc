# == SWE data pydicom__pydicom_1236 problem statement:
# == Git info: pydicom/pydicom, c2c6145d679adc97924d6c8a761a50b8e2819e3f

import pytest

problem_statement = r"""
apply_voi_lut - unclear what it does if both WL/VOILUTFunction _and_ VOILUTSequence are present
https://pydicom.github.io/pydicom/dev/reference/generated/pydicom.pixel_data_handlers.util.html#pydicom.pixel_data_handlers.util.apply_voi_lut

Hi all,

I'm working with some mammo image (digital) that have both
- window/level (0028,1050 0028,1051) plus VOILUTFunction (0028,1056) (set to SIGMOID) (set of 3 WL values)
- VOILUT sequences (0028, 3010)

specified.

Problem
---

It's unclear from the documentation when both a VOILUT (0028,3010) _and_ WL (0028,1051...) are present which is applied - the lut or the wl.

It just says if a LUT's present, it will apply that, and if a WL set is present it will apply that.

Questions
---

- If both LUT and WL are supplied, by the dicom standard, which should be applied?
- Separately to the above question about which is applied, if _both_ LUT and WL sequences are supplied, is there a way in `apply_voi_lut` to specify applying one or the other?  (ie force application of the WL instead of LUT etc)

- Also, if an image has a sequence of WL values rather than being single valued (so 0028,1050 & 0028,1051 are sequences), does the `index` parameter to `apply_voi_lut` apply to specify which in the sequence you want to use?

Thanks!

apply_voi_lut can't handle missing DICOM meta info
I have encountered two real life examples where `apply_voi_lut` does not handle corruption in DICOM meta fields

case 1:
```
(0028, 1050) Window Center                       DS: "128.0"
(0028, 1051) Window Width                        DS: "256.0"
(0028, 1052) Rescale Intercept                   DS: None
(0028, 1053) Rescale Slope                       DS: None
```
throws an exception

```
  File "python3.7/site-packages/pydicom/pixel_data_handlers/util.py", line 380, in apply_voi_lut
    y_min = y_min * ds.RescaleSlope + ds.RescaleIntercept
TypeError: unsupported operand type(s) for *: 'int' and 'NoneType'
```


case 2:

```
(0028, 1050) Window Center                       DS: "2607.0"
(0028, 1051) Window Width                        DS: "2785.0"
(0028, 1052) Rescale Intercept                   DS: "0.0"
(0028, 1053) Rescale Slope                       DS: "1.0"
(0028, 1054) Rescale Type                        LO: 'US'
(0028, 2110) Lossy Image Compression             CS: '00'
(0028, 3010)  VOI LUT Sequence   1 item(s) ----
   (0028, 3002) LUT Descriptor                      SS: None
   (0028, 3003) LUT Explanation                     LO: 'Noramal'
   (0028, 3006) LUT Data                            OW: None
```

throws an exception

```
  File "python3.7/site-packages/pydicom/pixel_data_handlers/util.py", line 312, in apply_voi_lut
    nr_entries = item.LUTDescriptor[0] or 2**16
TypeError: 'NoneType' object is not subscriptable
```


So far I have handled this with:

```
    def _lut_convert(self):
        return apply_voi_lut(self.input_dicom.pixel_array, self.input_dicom)

    def _get_raw_data(self):

        # convert to presentation LUT
        try:
            data = self._lut_convert()
        # many things can be corrupted in the VOILUTSequence attribute,
        # fall back to default WC/WW conversion
        except Exception as e:
            try:
                if "VOILUTSequence" in self.input_dicom:
                    del self.input_dicom["VOILUTSequence"]
                    data = self._lut_convert()
            except Exception as e:
                raise InvalidImage(f"Could not convert to presentation LUT due to: {e}")
```

While the case 1 could be seen as an expected behavior (?), I imagine case 2 should be handled by WC/WW transformations if followed DICOM standard?









""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_handler_util.py b/pydicom/tests/test_handler_util.py
--- a/pydicom/tests/test_handler_util.py
+++ b/pydicom/tests/test_handler_util.py
@@ -28,7 +28,9 @@
     apply_modality_lut,
     apply_voi_lut,
     get_j2k_parameters,
-    get_nr_frames
+    get_nr_frames,
+    apply_voi,
+    apply_windowing
 )
 from pydicom.uid import (ExplicitVRLittleEndian, ImplicitVRLittleEndian,
                          UncompressedPixelTransferSyntaxes)
@@ -1412,191 +1414,8 @@ def test_unknown_opcode_raises(self):


 @pytest.mark.skipif(not HAVE_NP, reason="Numpy is not available")
-class TestNumpy_VOILUT:
-    \"\"\"Tests for util.apply_voi_lut().\"\"\"
-    def test_voi_single_view(self):
-        \"\"\"Test VOI LUT with a single view.\"\"\"
-        ds = dcmread(VOI_08_1F)
-        assert 8 == ds.BitsAllocated
-        assert 8 == ds.BitsStored
-        assert 0 == ds.PixelRepresentation
-        item = ds.VOILUTSequence[0]
-        assert [256, 0, 16] == item.LUTDescriptor
-        lut = item.LUTData
-        assert 0 == lut[0]
-        assert 19532 == lut[76]
-        assert 45746 == lut[178]
-        assert 65535 == lut[255]
-
-        arr = ds.pixel_array
-        assert 0 == arr[387, 448]
-        assert 76 == arr[178, 126]
-        assert 178 == arr[186, 389]
-        assert 255 == arr[129, 79]
-
-        out = apply_voi_lut(arr, ds)
-        assert 0 == out[387, 448]
-        assert 19532 == out[178, 126]
-        assert 45746 == out[186, 389]
-        assert 65535 == out[129, 79]
-
-    def test_voi_multi_view(self):
-        \"\"\"Test VOI LUT with multiple views.\"\"\"
-        ds = dcmread(VOI_08_1F)
-        assert 8 == ds.BitsAllocated
-        assert 8 == ds.BitsStored
-        assert 0 == ds.PixelRepresentation
-        item0 = ds.VOILUTSequence[0]
-        # Add another view thats the inverse
-        ds.VOILUTSequence.append(Dataset())
-        item1 = ds.VOILUTSequence[1]
-        item1.LUTDescriptor = [256, 0, 16]
-        item1.LUTData = item0.LUTData[::-1]
-
-        arr = ds.pixel_array
-        assert 0 == arr[387, 448]
-        assert 76 == arr[178, 126]
-        assert 178 == arr[186, 389]
-        assert 255 == arr[129, 79]
-
-        out0 = apply_voi_lut(arr, ds)
-        assert 0 == out0[387, 448]
-        assert 19532 == out0[178, 126]
-        assert 45746 == out0[186, 389]
-        assert 65535 == out0[129, 79]
-
-        out1 = apply_voi_lut(arr, ds, index=1)
-        assert 65535 == out1[387, 448]
-        assert 46003 == out1[178, 126]
-        assert 19789 == out1[186, 389]
-        assert 0 == out1[129, 79]
-
-    def test_voi_multi_frame(self):
-        \"\"\"Test VOI with a multiple frames.\"\"\"
-        ds = dcmread(VOI_08_1F)
-        assert 8 == ds.BitsAllocated
-        assert 8 == ds.BitsStored
-        assert 0 == ds.PixelRepresentation
-
-        arr = ds.pixel_array
-        arr = np.stack([arr, 255 - arr])
-        assert (2, 512, 512) == arr.shape
-
-        out = apply_voi_lut(arr, ds)
-        assert 0 == out[0, 387, 448]
-        assert 19532 == out[0, 178, 126]
-        assert 45746 == out[0, 186, 389]
-        assert 65535 == out[0, 129, 79]
-        assert 65535 == out[1, 387, 448]
-        assert 46003 == out[1, 178, 126]
-        assert 19789 == out[1, 186, 389]
-        assert 0 == out[1, 129, 79]
-
-    def test_voi_zero_entries(self):
-        \"\"\"Test that 0 entries is interpreted correctly.\"\"\"
-        ds = dcmread(VOI_08_1F)
-        seq = ds.VOILUTSequence[0]
-        seq.LUTDescriptor = [0, 0, 16]
-        assert 256 == len(seq.LUTData)
-        arr = np.asarray([0, 255, 256, 65535])
-        msg = r"index 256 is out of bounds"
-        with pytest.raises(IndexError, match=msg):
-            apply_voi_lut(arr, ds)
-
-        # LUTData with 65536 entries
-        seq.LUTData = [0] * 65535 + [1]
-        out = apply_voi_lut(arr, ds)
-        assert [0, 0, 0, 1] == list(out)
-
-    def test_voi_uint8(self):
-        \"\"\"Test uint VOI LUT with an 8-bit LUT.\"\"\"
-        ds = Dataset()
-        ds.PixelRepresentation = 0
-        ds.BitsStored = 8
-        ds.VOILUTSequence = [Dataset()]
-        item = ds.VOILUTSequence[0]
-        item.LUTDescriptor = [4, 0, 8]
-        item.LUTData = [0, 127, 128, 255]
-        arr = np.asarray([0, 1, 128, 254, 255], dtype='uint8')
-        out = apply_voi_lut(arr, ds)
-        assert 'uint8' == out.dtype
-        assert [0, 127, 255, 255, 255] == out.tolist()
-
-    def test_voi_uint16(self):
-        \"\"\"Test uint VOI LUT with an 16-bit LUT.\"\"\"
-        ds = Dataset()
-        ds.PixelRepresentation = 0
-        ds.BitsStored = 16
-        ds.VOILUTSequence = [Dataset()]
-        item = ds.VOILUTSequence[0]
-        item.LUTDescriptor = [4, 0, 16]
-        item.LUTData = [0, 127, 32768, 65535]
-        arr = np.asarray([0, 1, 2, 3, 255], dtype='uint16')
-        out = apply_voi_lut(arr, ds)
-        assert 'uint16' == out.dtype
-        assert [0, 127, 32768, 65535, 65535] == out.tolist()
-
-    def test_voi_int8(self):
-        \"\"\"Test int VOI LUT with an 8-bit LUT.\"\"\"
-        ds = Dataset()
-        ds.PixelRepresentation = 1
-        ds.BitsStored = 8
-        ds.VOILUTSequence = [Dataset()]
-        item = ds.VOILUTSequence[0]
-        item.LUTDescriptor = [4, 0, 8]
-        item.LUTData = [0, 127, 128, 255]
-        arr = np.asarray([0, -1, 2, -128, 127], dtype='int8')
-        out = apply_voi_lut(arr, ds)
-        assert 'uint8' == out.dtype
-        assert [0, 0, 128, 0, 255] == out.tolist()
-
-    def test_voi_int16(self):
-        \"\"\"Test int VOI LUT with an 16-bit LUT.\"\"\"
-        ds = Dataset()
-        ds.PixelRepresentation = 0
-        ds.BitsStored = 16
-        ds.VOILUTSequence = [Dataset()]
-        item = ds.VOILUTSequence[0]
-        item.LUTDescriptor = [4, 0, 16]
-        item.LUTData = [0, 127, 32768, 65535]
-        arr = np.asarray([0, -1, 2, -128, 255], dtype='int16')
-        out = apply_voi_lut(arr, ds)
-        assert 'uint16' == out.dtype
-        assert [0, 0, 32768, 0, 65535] == out.tolist()
-
-    def test_voi_bad_depth(self):
-        \"\"\"Test bad LUT depth raises exception.\"\"\"
-        ds = dcmread(VOI_08_1F)
-        item = ds.VOILUTSequence[0]
-        item.LUTDescriptor[2] = 7
-        msg = r"'7' bits per LUT entry is not supported"
-        with pytest.raises(NotImplementedError, match=msg):
-            apply_voi_lut(ds.pixel_array, ds)
-
-        item.LUTDescriptor[2] = 17
-        msg = r"'17' bits per LUT entry is not supported"
-        with pytest.raises(NotImplementedError, match=msg):
-            apply_voi_lut(ds.pixel_array, ds)
-
-    def test_voi_uint16_array_float(self):
-        \"\"\"Test warning when array is float and VOI LUT with an 16-bit LUT\"\"\"
-        ds = Dataset()
-        ds.PixelRepresentation = 0
-        ds.BitsStored = 16
-        ds.VOILUTSequence = [Dataset()]
-        item = ds.VOILUTSequence[0]
-        item.LUTDescriptor = [4, 0, 16]
-        item.LUTData = [0, 127, 32768, 65535]
-        arr = np.asarray([0, 1, 2, 3, 255], dtype='float64')
-        msg = (
-            r"Applying a VOI LUT on a float input array may give "
-            r"incorrect results"
-        )
-
-        with pytest.warns(UserWarning, match=msg):
-            out = apply_voi_lut(arr, ds)
-            assert [0, 127, 32768, 65535, 65535] == out.tolist()
-
+class TestNumpy_ApplyWindowing:
+    \"\"\"Tests for util.apply_windowing().\"\"\"
     def test_window_single_view(self):
         \"\"\"Test windowing with a single view.\"\"\"
         # 12-bit unsigned
@@ -1611,7 +1430,7 @@ def test_window_single_view(self):

         arr = ds.pixel_array
         assert 642 == arr[326, 130]
-        out = apply_voi_lut(arr, ds)
+        out = apply_windowing(arr, ds)
         assert 3046.6 == pytest.approx(out[326, 130], abs=0.1)

     def test_window_multi_view(self):
@@ -1631,9 +1450,9 @@ def test_window_multi_view(self):

         arr = ds.pixel_array
         assert 642 == arr[326, 130]
-        out = apply_voi_lut(arr, ds)
+        out = apply_windowing(arr, ds)
         assert 3046.6 == pytest.approx(out[326, 130], abs=0.1)
-        out = apply_voi_lut(arr, ds, index=1)
+        out = apply_windowing(arr, ds, index=1)
         assert 4095.0 == pytest.approx(out[326, 130], abs=0.1)

     def test_window_uint8(self):
@@ -1647,24 +1466,24 @@ def test_window_uint8(self):
         # Linear
         ds.WindowWidth = 1
         ds.WindowCenter = 0
-        assert [255, 255, 255, 255, 255] == apply_voi_lut(arr, ds).tolist()
+        assert [255, 255, 255, 255, 255] == apply_windowing(arr, ds).tolist()

         ds.WindowWidth = 128
         ds.WindowCenter = 254
         assert [0, 0, 0, 128.5, 130.5] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist(), abs=0.1
+            apply_windowing(arr, ds).tolist(), abs=0.1
         )

         # Linear exact
         ds.VOILUTFunction = 'LINEAR_EXACT'
         assert [0, 0, 0, 127.5, 129.5] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist(), abs=0.1
+            apply_windowing(arr, ds).tolist(), abs=0.1
         )

         # Sigmoid
         ds.VOILUTFunction = 'SIGMOID'
         assert [0.1, 0.1, 4.9, 127.5, 129.5] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist(), abs=0.1
+            apply_windowing(arr, ds).tolist(), abs=0.1
         )

     def test_window_uint16(self):
@@ -1677,22 +1496,22 @@ def test_window_uint16(self):

         ds.WindowWidth = 1
         ds.WindowCenter = 0
-        assert [65535] * 5 == apply_voi_lut(arr, ds).tolist()
+        assert [65535] * 5 == apply_windowing(arr, ds).tolist()

         ds.WindowWidth = 32768
         ds.WindowCenter = 254
         assert [32260.5, 32262.5, 65535, 65535, 65535] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist(), abs=0.1
+            apply_windowing(arr, ds).tolist(), abs=0.1
         )

         ds.VOILUTFunction = 'LINEAR_EXACT'
         assert [32259.5, 32261.5, 65535, 65535, 65535] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist(), abs=0.1
+            apply_windowing(arr, ds).tolist(), abs=0.1
         )

         ds.VOILUTFunction = 'SIGMOID'
         assert [32259.5, 32261.5, 64319.8, 65512.3, 65512.3] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist(), abs=0.1
+            apply_windowing(arr, ds).tolist(), abs=0.1
         )

     def test_window_uint32(self):
@@ -1706,14 +1525,14 @@ def test_window_uint32(self):

         ds.WindowWidth = 1
         ds.WindowCenter = 0
-        assert [y_max] * 5 == apply_voi_lut(arr, ds).tolist()
+        assert [y_max] * 5 == apply_windowing(arr, ds).tolist()

         ds.WindowWidth = 342423423423
         ds.WindowCenter = 757336
         assert (
             [2147474148.4, 2147474148.4,
              2174409724, 2201345299.7, 2201345299.7] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1721,7 +1540,7 @@ def test_window_uint32(self):
         assert (
             [2147474148.3, 2147474148.4,
              2174409724, 2201345299.7, 2201345299.7] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1729,7 +1548,7 @@ def test_window_uint32(self):
         assert (
             [2147474148.3, 2147474148.4,
              2174408313.1, 2201334008.2, 2201334008.3] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1745,25 +1564,25 @@ def test_window_int8(self):
         ds.WindowWidth = 1
         ds.WindowCenter = 0
         assert [-128, -128, -128, 127, 127, 127, 127] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist()
+            apply_windowing(arr, ds).tolist()
         )

         ds.WindowWidth = 128
         ds.WindowCenter = -5
         assert [-128, -128, 8.5, 10.5, 12.6, 127, 127] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist(), abs=0.1
+            apply_windowing(arr, ds).tolist(), abs=0.1
         )

         # Linear exact
         ds.VOILUTFunction = 'LINEAR_EXACT'
         assert [-128, -128, 7.5, 9.5, 11.5, 127, 127] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist(), abs=0.1
+            apply_windowing(arr, ds).tolist(), abs=0.1
         )

         # Sigmoid
         ds.VOILUTFunction = 'SIGMOID'
         assert [-122.7, -122.5, 7.5, 9.4, 11.4, 122.8, 122.9] == pytest.approx(
-            apply_voi_lut(arr, ds).tolist(), abs=0.1
+            apply_windowing(arr, ds).tolist(), abs=0.1
         )

     def test_window_int16(self):
@@ -1780,7 +1599,7 @@ def test_window_int16(self):
         assert (
             [-32768, -32768, -32768,
              32767, 32767, 32767, 32767] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1789,7 +1608,7 @@ def test_window_int16(self):
         assert (
             [-32768, -32768, 2321.6,
              2837.6, 3353.7, 32767, 32767] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1798,7 +1617,7 @@ def test_window_int16(self):
         assert (
             [-32768, -32768, 2047.5,
              2559.5, 3071.5, 32767, 32767] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1807,7 +1626,7 @@ def test_window_int16(self):
         assert (
             [-31394.1, -31351.4, 2044.8,
              2554.3, 3062.5, 31692, 31724.6] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1825,7 +1644,7 @@ def test_window_int32(self):
         assert (
             [-2**31, -2**31, -2**31,
              2**31 - 1, 2**31 - 1, 2**31 - 1, 2**31 - 1] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1834,7 +1653,7 @@ def test_window_int32(self):
         assert (
             [-2147483648, -2147483648, 152183880, 186002520.1,
              219821160.3, 2147483647, 2147483647] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1843,7 +1662,7 @@ def test_window_int32(self):
         assert (
             [-2147483648, -2147483648, 134217727.5, 167772159.5,
              201326591.5, 2147483647, 2147483647] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1852,7 +1671,7 @@ def test_window_int32(self):
         assert (
             [-2057442919.3, -2054646500.7, 134043237.4, 167431657.4,
              200738833.7, 2077033158.8, 2079166214.8] == pytest.approx(
-                apply_voi_lut(arr, ds).tolist(), abs=0.1
+                apply_windowing(arr, ds).tolist(), abs=0.1
             )
         )

@@ -1872,7 +1691,7 @@ def test_window_multi_frame(self):
         assert (2, 484, 484) == arr.shape
         assert 642 == arr[0, 326, 130]
         assert 3453 == arr[1, 326, 130]
-        out = apply_voi_lut(arr, ds)
+        out = apply_windowing(arr, ds)
         assert 3046.6 == pytest.approx(out[0, 326, 130], abs=0.1)
         assert 4095.0 == pytest.approx(out[1, 326, 130], abs=0.1)

@@ -1902,7 +1721,7 @@ def test_window_rescale(self):
         assert 770.4 == hu[326, 130]
         assert 1347.6 == hu[316, 481]
         # With rescale -> output range is 0 to 4914
-        out = apply_voi_lut(hu, ds)
+        out = apply_windowing(hu, ds)
         assert 0 == pytest.approx(out[16, 60], abs=0.1)
         assert 4455.6 == pytest.approx(out[326, 130], abs=0.1)
         assert 4914.0 == pytest.approx(out[316, 481], abs=0.1)
@@ -1930,7 +1749,7 @@ def test_window_modality_lut(self):
         hu = apply_modality_lut(arr, ds)
         assert 65535 == hu[16, 60]
         assert 49147 == hu[0, 1]
-        out = apply_voi_lut(hu, ds)
+        out = apply_windowing(hu, ds)
         assert 65535.0 == pytest.approx(out[16, 60], abs=0.1)
         assert 32809.0 == pytest.approx(out[0, 1], abs=0.1)
         # Output range must be 0 to 2**16 - 1
@@ -1943,7 +1762,7 @@ def test_window_bad_photometric_interp(self):
         ds.PhotometricInterpretation = 'RGB'
         msg = r"only 'MONOCHROME1' and 'MONOCHROME2' are allowed"
         with pytest.raises(ValueError, match=msg):
-            apply_voi_lut(ds.pixel_array, ds)
+            apply_windowing(ds.pixel_array, ds)

     def test_window_bad_parameters(self):
         \"\"\"Test bad windowing parameters raise exceptions.\"\"\"
@@ -1952,22 +1771,22 @@ def test_window_bad_parameters(self):
         ds.VOILUTFunction = 'LINEAR'
         msg = r"Width must be greater than or equal to 1"
         with pytest.raises(ValueError, match=msg):
-            apply_voi_lut(ds.pixel_array, ds)
+            apply_windowing(ds.pixel_array, ds)

         ds.VOILUTFunction = 'LINEAR_EXACT'
         msg = r"Width must be greater than 0"
         with pytest.raises(ValueError, match=msg):
-            apply_voi_lut(ds.pixel_array, ds)
+            apply_windowing(ds.pixel_array, ds)

         ds.VOILUTFunction = 'SIGMOID'
         msg = r"Width must be greater than 0"
         with pytest.raises(ValueError, match=msg):
-            apply_voi_lut(ds.pixel_array, ds)
+            apply_windowing(ds.pixel_array, ds)

         ds.VOILUTFunction = 'UNKNOWN'
         msg = r"Unsupported \(0028,1056\) VOI LUT Function value 'UNKNOWN'"
         with pytest.raises(ValueError, match=msg):
-            apply_voi_lut(ds.pixel_array, ds)
+            apply_windowing(ds.pixel_array, ds)

     def test_window_bad_index(self, no_numpy_use):
         \"\"\"Test windowing with a bad view index.\"\"\"
@@ -1975,7 +1794,7 @@ def test_window_bad_index(self, no_numpy_use):
         assert 2 == len(ds.WindowWidth)
         arr = ds.pixel_array
         with pytest.raises(IndexError, match=r"list index out of range"):
-            apply_voi_lut(arr, ds, index=2)
+            apply_windowing(arr, ds, index=2)

     def test_unchanged(self):
         \"\"\"Test input array is unchanged if no VOI LUT\"\"\"
@@ -1984,7 +1803,219 @@ def test_unchanged(self):
         ds.PixelRepresentation = 1
         ds.BitsStored = 8
         arr = np.asarray([-128, -127, -1, 0, 1, 126, 127], dtype='int8')
-        out = apply_voi_lut(arr, ds)
+        out = apply_windowing(arr, ds)
+        assert [-128, -127, -1, 0, 1, 126, 127] == out.tolist()
+
+    def test_rescale_empty(self):
+        \"\"\"Test RescaleSlope and RescaleIntercept being empty.\"\"\"
+        ds = dcmread(WIN_12_1F)
+        ds.RescaleSlope = None
+        ds.RescaleIntercept = None
+
+        arr = ds.pixel_array
+        assert 0 == arr[16, 60]
+        assert 642 == arr[326, 130]
+        assert 1123 == arr[316, 481]
+        out = apply_windowing(arr, ds)
+        assert 0 == pytest.approx(out[16, 60], abs=0.1)
+        assert 3046.6 == pytest.approx(out[326, 130], abs=0.1)
+        assert 4095.0 == pytest.approx(out[316, 481], abs=0.1)
+
+
+@pytest.mark.skipif(not HAVE_NP, reason="Numpy is not available")
+class TestNumpy_ApplyVOI:
+    \"\"\"Tests for util.apply_voi().\"\"\"
+    def test_voi_single_view(self):
+        \"\"\"Test VOI LUT with a single view.\"\"\"
+        ds = dcmread(VOI_08_1F)
+        assert 8 == ds.BitsAllocated
+        assert 8 == ds.BitsStored
+        assert 0 == ds.PixelRepresentation
+        item = ds.VOILUTSequence[0]
+        assert [256, 0, 16] == item.LUTDescriptor
+        lut = item.LUTData
+        assert 0 == lut[0]
+        assert 19532 == lut[76]
+        assert 45746 == lut[178]
+        assert 65535 == lut[255]
+
+        arr = ds.pixel_array
+        assert 0 == arr[387, 448]
+        assert 76 == arr[178, 126]
+        assert 178 == arr[186, 389]
+        assert 255 == arr[129, 79]
+
+        out = apply_voi(arr, ds)
+        assert 0 == out[387, 448]
+        assert 19532 == out[178, 126]
+        assert 45746 == out[186, 389]
+        assert 65535 == out[129, 79]
+
+    def test_voi_multi_view(self):
+        \"\"\"Test VOI LUT with multiple views.\"\"\"
+        ds = dcmread(VOI_08_1F)
+        assert 8 == ds.BitsAllocated
+        assert 8 == ds.BitsStored
+        assert 0 == ds.PixelRepresentation
+        item0 = ds.VOILUTSequence[0]
+        # Add another view thats the inverse
+        ds.VOILUTSequence.append(Dataset())
+        item1 = ds.VOILUTSequence[1]
+        item1.LUTDescriptor = [256, 0, 16]
+        item1.LUTData = item0.LUTData[::-1]
+
+        arr = ds.pixel_array
+        assert 0 == arr[387, 448]
+        assert 76 == arr[178, 126]
+        assert 178 == arr[186, 389]
+        assert 255 == arr[129, 79]
+
+        out0 = apply_voi(arr, ds)
+        assert 0 == out0[387, 448]
+        assert 19532 == out0[178, 126]
+        assert 45746 == out0[186, 389]
+        assert 65535 == out0[129, 79]
+
+        out1 = apply_voi(arr, ds, index=1)
+        assert 65535 == out1[387, 448]
+        assert 46003 == out1[178, 126]
+        assert 19789 == out1[186, 389]
+        assert 0 == out1[129, 79]
+
+    def test_voi_multi_frame(self):
+        \"\"\"Test VOI with a multiple frames.\"\"\"
+        ds = dcmread(VOI_08_1F)
+        assert 8 == ds.BitsAllocated
+        assert 8 == ds.BitsStored
+        assert 0 == ds.PixelRepresentation
+
+        arr = ds.pixel_array
+        arr = np.stack([arr, 255 - arr])
+        assert (2, 512, 512) == arr.shape
+
+        out = apply_voi(arr, ds)
+        assert 0 == out[0, 387, 448]
+        assert 19532 == out[0, 178, 126]
+        assert 45746 == out[0, 186, 389]
+        assert 65535 == out[0, 129, 79]
+        assert 65535 == out[1, 387, 448]
+        assert 46003 == out[1, 178, 126]
+        assert 19789 == out[1, 186, 389]
+        assert 0 == out[1, 129, 79]
+
+    def test_voi_zero_entries(self):
+        \"\"\"Test that 0 entries is interpreted correctly.\"\"\"
+        ds = dcmread(VOI_08_1F)
+        seq = ds.VOILUTSequence[0]
+        seq.LUTDescriptor = [0, 0, 16]
+        assert 256 == len(seq.LUTData)
+        arr = np.asarray([0, 255, 256, 65535])
+        msg = r"index 256 is out of bounds"
+        with pytest.raises(IndexError, match=msg):
+            apply_voi(arr, ds)
+
+        # LUTData with 65536 entries
+        seq.LUTData = [0] * 65535 + [1]
+        out = apply_voi(arr, ds)
+        assert [0, 0, 0, 1] == list(out)
+
+    def test_voi_uint8(self):
+        \"\"\"Test uint VOI LUT with an 8-bit LUT.\"\"\"
+        ds = Dataset()
+        ds.PixelRepresentation = 0
+        ds.BitsStored = 8
+        ds.VOILUTSequence = [Dataset()]
+        item = ds.VOILUTSequence[0]
+        item.LUTDescriptor = [4, 0, 8]
+        item.LUTData = [0, 127, 128, 255]
+        arr = np.asarray([0, 1, 128, 254, 255], dtype='uint8')
+        out = apply_voi(arr, ds)
+        assert 'uint8' == out.dtype
+        assert [0, 127, 255, 255, 255] == out.tolist()
+
+    def test_voi_uint16(self):
+        \"\"\"Test uint VOI LUT with an 16-bit LUT.\"\"\"
+        ds = Dataset()
+        ds.PixelRepresentation = 0
+        ds.BitsStored = 16
+        ds.VOILUTSequence = [Dataset()]
+        item = ds.VOILUTSequence[0]
+        item.LUTDescriptor = [4, 0, 16]
+        item.LUTData = [0, 127, 32768, 65535]
+        arr = np.asarray([0, 1, 2, 3, 255], dtype='uint16')
+        out = apply_voi(arr, ds)
+        assert 'uint16' == out.dtype
+        assert [0, 127, 32768, 65535, 65535] == out.tolist()
+
+    def test_voi_int8(self):
+        \"\"\"Test int VOI LUT with an 8-bit LUT.\"\"\"
+        ds = Dataset()
+        ds.PixelRepresentation = 1
+        ds.BitsStored = 8
+        ds.VOILUTSequence = [Dataset()]
+        item = ds.VOILUTSequence[0]
+        item.LUTDescriptor = [4, 0, 8]
+        item.LUTData = [0, 127, 128, 255]
+        arr = np.asarray([0, -1, 2, -128, 127], dtype='int8')
+        out = apply_voi(arr, ds)
+        assert 'uint8' == out.dtype
+        assert [0, 0, 128, 0, 255] == out.tolist()
+
+    def test_voi_int16(self):
+        \"\"\"Test int VOI LUT with an 16-bit LUT.\"\"\"
+        ds = Dataset()
+        ds.PixelRepresentation = 0
+        ds.BitsStored = 16
+        ds.VOILUTSequence = [Dataset()]
+        item = ds.VOILUTSequence[0]
+        item.LUTDescriptor = [4, 0, 16]
+        item.LUTData = [0, 127, 32768, 65535]
+        arr = np.asarray([0, -1, 2, -128, 255], dtype='int16')
+        out = apply_voi(arr, ds)
+        assert 'uint16' == out.dtype
+        assert [0, 0, 32768, 0, 65535] == out.tolist()
+
+    def test_voi_bad_depth(self):
+        \"\"\"Test bad LUT depth raises exception.\"\"\"
+        ds = dcmread(VOI_08_1F)
+        item = ds.VOILUTSequence[0]
+        item.LUTDescriptor[2] = 7
+        msg = r"'7' bits per LUT entry is not supported"
+        with pytest.raises(NotImplementedError, match=msg):
+            apply_voi(ds.pixel_array, ds)
+
+        item.LUTDescriptor[2] = 17
+        msg = r"'17' bits per LUT entry is not supported"
+        with pytest.raises(NotImplementedError, match=msg):
+            apply_voi(ds.pixel_array, ds)
+
+    def test_voi_uint16_array_float(self):
+        \"\"\"Test warning when array is float and VOI LUT with an 16-bit LUT\"\"\"
+        ds = Dataset()
+        ds.PixelRepresentation = 0
+        ds.BitsStored = 16
+        ds.VOILUTSequence = [Dataset()]
+        item = ds.VOILUTSequence[0]
+        item.LUTDescriptor = [4, 0, 16]
+        item.LUTData = [0, 127, 32768, 65535]
+        arr = np.asarray([0, 1, 2, 3, 255], dtype='float64')
+        msg = (
+            r"Applying a VOI LUT on a float input array may give "
+            r"incorrect results"
+        )
+
+        with pytest.warns(UserWarning, match=msg):
+            out = apply_voi(arr, ds)
+            assert [0, 127, 32768, 65535, 65535] == out.tolist()
+
+    def test_unchanged(self):
+        \"\"\"Test input array is unchanged if no VOI LUT\"\"\"
+        ds = Dataset()
+        ds.PhotometricInterpretation = 'MONOCHROME1'
+        ds.PixelRepresentation = 1
+        ds.BitsStored = 8
+        arr = np.asarray([-128, -127, -1, 0, 1, 126, 127], dtype='int8')
+        out = apply_voi(arr, ds)
         assert [-128, -127, -1, 0, 1, 126, 127] == out.tolist()

     def test_voi_lutdata_ow(self):
@@ -2001,11 +2032,94 @@ def test_voi_lutdata_ow(self):
         item.LUTData = pack('<4H', *item.LUTData)
         item['LUTData'].VR = 'OW'
         arr = np.asarray([0, 1, 2, 3, 255], dtype='uint16')
-        out = apply_voi_lut(arr, ds)
+        out = apply_voi(arr, ds)
         assert 'uint16' == out.dtype
         assert [0, 127, 32768, 65535, 65535] == out.tolist()


+@pytest.mark.skipif(not HAVE_NP, reason="Numpy is not available")
+class TestNumpy_ApplyVOILUT:
+    def test_unchanged(self):
+        \"\"\"Test input array is unchanged if no VOI LUT\"\"\"
+        ds = Dataset()
+        ds.PhotometricInterpretation = 'MONOCHROME1'
+        ds.PixelRepresentation = 1
+        ds.BitsStored = 8
+        arr = np.asarray([-128, -127, -1, 0, 1, 126, 127], dtype='int8')
+        out = apply_voi_lut(arr, ds)
+        assert [-128, -127, -1, 0, 1, 126, 127] == out.tolist()
+
+    def test_only_windowing(self):
+        \"\"\"Test only windowing operation elements present.\"\"\"
+        ds = Dataset()
+        ds.PhotometricInterpretation = 'MONOCHROME1'
+        ds.PixelRepresentation = 0
+        ds.BitsStored = 8
+        arr = np.asarray([0, 1, 128, 254, 255], dtype='uint8')
+
+        ds.WindowWidth = 1
+        ds.WindowCenter = 0
+        assert [255, 255, 255, 255, 255] == apply_voi_lut(arr, ds).tolist()
+
+    def test_only_voi(self):
+        \"\"\"Test only LUT operation elements present.\"\"\"
+        ds = Dataset()
+        ds.PixelRepresentation = 0
+        ds.BitsStored = 8
+        ds.VOILUTSequence = [Dataset()]
+        item = ds.VOILUTSequence[0]
+        item.LUTDescriptor = [4, 0, 8]
+        item.LUTData = [0, 127, 128, 255]
+        arr = np.asarray([0, 1, 128, 254, 255], dtype='uint8')
+        out = apply_voi_lut(arr, ds)
+        assert 'uint8' == out.dtype
+        assert [0, 127, 255, 255, 255] == out.tolist()
+
+    def test_voi_windowing(self):
+        \"\"\"Test both LUT and windowing operation elements present.\"\"\"
+        ds = Dataset()
+        ds.PhotometricInterpretation = 'MONOCHROME1'
+        ds.PixelRepresentation = 0
+        ds.BitsStored = 8
+        ds.WindowWidth = 1
+        ds.WindowCenter = 0
+        ds.VOILUTSequence = [Dataset()]
+        item = ds.VOILUTSequence[0]
+        item.LUTDescriptor = [4, 0, 8]
+        item.LUTData = [0, 127, 128, 255]
+        arr = np.asarray([0, 1, 128, 254, 255], dtype='uint8')
+
+        # Defaults to LUT
+        out = apply_voi_lut(arr, ds)
+        assert [0, 127, 255, 255, 255] == out.tolist()
+
+        out = apply_voi_lut(arr, ds, prefer_lut=False)
+        assert [255, 255, 255, 255, 255] == out.tolist()
+
+    def test_voi_windowing_empty(self):
+        \"\"\"Test empty VOI elements.\"\"\"
+        ds = Dataset()
+        ds.PhotometricInterpretation = 'MONOCHROME1'
+        ds.PixelRepresentation = 0
+        ds.BitsStored = 8
+        ds.WindowWidth = 1
+        ds.WindowCenter = 0
+        ds.VOILUTSequence = [Dataset()]
+        item = ds.VOILUTSequence[0]
+        item.LUTDescriptor = [4, 0, 8]
+        item.LUTData = [0, 127, 128, 255]
+        arr = np.asarray([0, 1, 128, 254, 255], dtype='uint8')
+
+        # Test empty VOI elements
+        item.LUTData = None
+        out = apply_voi_lut(arr, ds)
+        assert [255, 255, 255, 255, 255] == out.tolist()
+
+        # Test empty windowing elements
+        ds.WindowWidth = None
+        out = apply_voi_lut(arr, ds)
+        assert [0, 1, 128, 254, 255] == out.tolist()
+
 class TestGetJ2KParameters:
     \"\"\"Tests for get_j2k_parameters.\"\"\"
     def test_precision(self):
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1236(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1236',
        'repo': 'pydicom/pydicom',
        'base_commit': 'c2c6145d679adc97924d6c8a761a50b8e2819e3f',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_unknown_pixel_representation_raises", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_unknown_bits_allocated_raises", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_unsupported_dtypes", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[1-0-False-uint8]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[1-1-False-uint8]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[8-0-False-uint8]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[8-1-False-int8]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[16-0-False-uint16]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[16-1-False-int16]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[32-0-False-uint32]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[32-1-False-int32]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[32-0-True-float32]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_supported_dtypes[64-0-True-float64]", "pydicom/tests/test_handler_util.py::TestNumpy_PixelDtype::test_byte_swapping", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_reference_1frame_1sample", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_reference_1frame_3sample", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_reference_2frame_1sample", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_reference_2frame_3sample", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_1frame_1sample", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_1frame_3sample_0conf", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_1frame_3sample_1conf", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_2frame_1sample", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_2frame_3sample_0conf", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_2frame_3sample_1conf", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_compressed_syntaxes_0conf", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_compressed_syntaxes_1conf", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_uncompressed_syntaxes", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_invalid_nr_frames_raises", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_invalid_samples_raises", "pydicom/tests/test_handler_util.py::TestNumpy_ReshapePixelArray::test_invalid_planar_conf_raises", "pydicom/tests/test_handler_util.py::TestNumpy_ConvertColourSpace::test_unknown_current_raises", "pydicom/tests/test_handler_util.py::TestNumpy_ConvertColourSpace::test_unknown_desired_raises", "pydicom/tests/test_handler_util.py::TestNumpy_ConvertColourSpace::test_current_is_desired[RGB-RGB]", "pydicom/tests/test_handler_util.py::TestNumpy_ConvertColourSpace::test_current_is_desired[YBR_FULL-YBR_FULL]", "pydicom/tests/test_handler_util.py::TestNumpy_ConvertColourSpace::test_current_is_desired[YBR_FULL-YBR_FULL_422]", "pydicom/tests/test_handler_util.py::TestNumpy_ConvertColourSpace::test_current_is_desired[YBR_FULL_422-YBR_FULL_422]", "pydicom/tests/test_handler_util.py::TestNumpy_ConvertColourSpace::test_current_is_desired[YBR_FULL_422-YBR_FULL]", "pydicom/tests/test_handler_util.py::TestNumpy_ConvertColourSpace::test_rgb_ybr_rgb_single_frame", "pydicom/tests/test_handler_util.py::TestNumpy_ConvertColourSpace::test_rgb_ybr_rgb_multi_frame", "pydicom/tests/test_handler_util.py::TestNumpy_DtypeCorrectedForEndianness::test_byte_swapping", "pydicom/tests/test_handler_util.py::TestNumpy_DtypeCorrectedForEndianness::test_no_endian_raises", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape0-1-length0]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape1-1-length1]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape2-1-length2]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape3-1-length3]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape4-1-length4]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape5-1-length5]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape6-1-length6]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape7-1-length7]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape8-1-length8]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape9-8-length9]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape10-8-length10]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape11-8-length11]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape12-8-length12]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape13-8-length13]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape14-8-length14]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape15-16-length15]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape16-16-length16]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape17-16-length17]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape18-16-length18]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape19-16-length19]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape20-32-length20]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape21-32-length21]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape22-32-length22]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape23-32-length23]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape24-32-length24]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape25-1-length25]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape26-1-length26]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape27-1-length27]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape28-1-length28]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape29-1-length29]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape30-1-length30]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape31-1-length31]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape32-1-length32]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape33-1-length33]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape34-8-length34]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape35-8-length35]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape36-8-length36]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape37-8-length37]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape38-8-length38]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape39-8-length39]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape40-16-length40]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape41-16-length41]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape42-16-length42]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape43-32-length43]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape44-32-length44]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape45-32-length45]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape46-1-length46]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape47-1-length47]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape48-1-length48]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape49-1-length49]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape50-1-length50]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape51-1-length51]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape52-1-length52]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape53-1-length53]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape54-1-length54]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape55-8-length55]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape56-8-length56]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape57-8-length57]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape58-16-length58]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape59-16-length59]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape60-16-length60]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape61-32-length61]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape62-32-length62]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_bytes[shape63-32-length63]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape0-1-length0]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape1-1-length1]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape2-1-length2]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape3-1-length3]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape4-1-length4]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape5-1-length5]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape6-1-length6]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape7-1-length7]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape8-1-length8]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape9-8-length9]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape10-8-length10]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape11-8-length11]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape12-8-length12]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape13-8-length13]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape14-8-length14]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape15-16-length15]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape16-16-length16]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape17-16-length17]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape18-16-length18]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape19-16-length19]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape20-32-length20]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape21-32-length21]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape22-32-length22]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape23-32-length23]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape24-32-length24]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape25-1-length25]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape26-1-length26]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape27-1-length27]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape28-1-length28]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape29-1-length29]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape30-1-length30]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape31-1-length31]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape32-1-length32]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape33-1-length33]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape34-8-length34]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape35-8-length35]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape36-8-length36]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape37-8-length37]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape38-8-length38]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape39-8-length39]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape40-16-length40]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape41-16-length41]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape42-16-length42]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape43-32-length43]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape44-32-length44]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape45-32-length45]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape46-1-length46]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape47-1-length47]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape48-1-length48]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape49-1-length49]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape50-1-length50]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape51-1-length51]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape52-1-length52]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape53-1-length53]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape54-1-length54]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape55-8-length55]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape56-8-length56]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape57-8-length57]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape58-16-length58]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape59-16-length59]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape60-16-length60]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape61-32-length61]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape62-32-length62]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_in_pixels[shape63-32-length63]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape0-1-length0]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape1-1-length1]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape2-1-length2]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape3-1-length3]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape4-1-length4]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape5-1-length5]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape6-1-length6]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape7-1-length7]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape8-1-length8]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape9-8-length9]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape10-8-length10]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape11-8-length11]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape12-8-length12]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape13-8-length13]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape14-8-length14]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape15-16-length15]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape16-16-length16]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape17-16-length17]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape18-16-length18]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape19-16-length19]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape20-32-length20]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape21-32-length21]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape22-32-length22]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape23-32-length23]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape24-32-length24]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape25-1-length25]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape26-1-length26]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape27-1-length27]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape28-1-length28]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape29-1-length29]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape30-1-length30]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape31-1-length31]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape32-1-length32]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape33-1-length33]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape34-8-length34]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape35-8-length35]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape36-8-length36]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape37-8-length37]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape38-8-length38]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape39-8-length39]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape40-16-length40]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape41-16-length41]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape42-16-length42]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape43-32-length43]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape44-32-length44]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape45-32-length45]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape46-1-length46]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape47-1-length47]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape48-1-length48]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape49-1-length49]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape50-1-length50]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape51-1-length51]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape52-1-length52]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape53-1-length53]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape54-1-length54]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape55-8-length55]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape56-8-length56]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape57-8-length57]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape58-16-length58]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape59-16-length59]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape60-16-length60]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape61-32-length61]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape62-32-length62]", "pydicom/tests/test_handler_util.py::TestGetExpectedLength::test_length_ybr_422[shape63-32-length63]", "pydicom/tests/test_handler_util.py::TestNumpy_ModalityLUT::test_slope_intercept", "pydicom/tests/test_handler_util.py::TestNumpy_ModalityLUT::test_lut_sequence", "pydicom/tests/test_handler_util.py::TestNumpy_ModalityLUT::test_lut_sequence_zero_entries", "pydicom/tests/test_handler_util.py::TestNumpy_ModalityLUT::test_unchanged", "pydicom/tests/test_handler_util.py::TestNumpy_ModalityLUT::test_lutdata_ow", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_neither_ds_nor_palette_raises", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_palette_unknown_raises", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_palette_unavailable_raises", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_supplemental_raises", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_invalid_bit_depth_raises", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_invalid_lut_bit_depth_raises", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_unequal_lut_length_raises", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_no_palette_color", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_uint08_16", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_uint08_16_2frame", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_uint16_16_segmented_litle", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_uint16_16_segmented_big", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_16_allocated_8_entries", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_alpha", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_well_known_palette", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_first_map_positive", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_first_map_negative", "pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_unchanged", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_discrete", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_linear", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_indirect_08", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_indirect_16", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_palettes_spring", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_palettes_summer", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_palettes_fall", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_palettes_winter", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_first_linear_raises", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_first_indirect_raises", "pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_unknown_opcode_raises", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_single_view", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_multi_view", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_uint8", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_uint16", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_uint32", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_int8", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_int16", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_int32", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_multi_frame", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_rescale", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_modality_lut", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_bad_photometric_interp", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_bad_parameters", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_window_bad_index", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_unchanged", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyWindowing::test_rescale_empty", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_single_view", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_multi_view", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_multi_frame", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_zero_entries", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_uint8", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_uint16", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_int8", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_int16", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_bad_depth", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_uint16_array_float", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_unchanged", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_voi_lutdata_ow", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOILUT::test_unchanged", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOILUT::test_only_windowing", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOILUT::test_only_voi", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOILUT::test_voi_windowing", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOILUT::test_voi_windowing_empty", "pydicom/tests/test_handler_util.py::TestGetJ2KParameters::test_precision", "pydicom/tests/test_handler_util.py::TestGetJ2KParameters::test_not_j2k", "pydicom/tests/test_handler_util.py::TestGetJ2KParameters::test_no_siz", "pydicom/tests/test_handler_util.py::TestGetJ2KParameters::test_short_bytestream", "pydicom/tests/test_handler_util.py::TestGetNrFrames::test_none", "pydicom/tests/test_handler_util.py::TestGetNrFrames::test_missing", "pydicom/tests/test_handler_util.py::TestGetNrFrames::test_existing"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
