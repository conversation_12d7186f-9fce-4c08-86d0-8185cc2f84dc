# == SWE data pydicom__pydicom_1416 problem statement:
# == Git info: pydicom/pydicom, 699c9f0a8e190d463dd828822106250523d38154

import pytest

problem_statement = r"""
Segmented LUTs are incorrectly expanded
**Describe the bug**
`pydicom.pixel_data_handlers.util._expand_segmented_lut()` expands segmented LUTs to an incorrect length.

**Expected behavior**
A correct length LUT to be produced.

**Steps To Reproduce**
Initialize the following variables.
```
import numpy as np
length = 48
y0 = 163
y1 = 255
```

Run the following two lines from [`pydicom.pixel_data_handlers.util._expand_segmented_lut()`](https://github.com/pydicom/pydicom/blob/699c9f0a8e190d463dd828822106250523d38154/pydicom/pixel_data_handlers/util.py#L875
)
```
step = (y1 - y0) / length
vals = np.around(np.arange(y0 + step, y1 + step, step))
```

Confirm that variable `vals` if of incorrect length
```
print(len(vals) == length)
> False
```

Alternatively, the code below produces similarly false results

```
from pydicom.pixel_data_handlers.util import _expand_segmented_lut
lut = _expand_segmented_lut(([0, 1, 163, 1, 48, 255]), "B")
print(len(lut) == (1+48))
> False
```

`np.arange` [explicitly states](https://numpy.org/doc/stable/reference/generated/numpy.arange.html) that it's "results will often not be consistent" when using "non-integer step", which is a very possible scenario in this function. The following alternative code does function correctly:

```
vals = np.around(np.linspace(y0 + step, y1, length))
```

**Your environment**
```bash
$ python -m pydicom.env_info
module       | version
------       | -------
platform     | Darwin-20.5.0-x86_64-i386-64bit
Python       | 3.7.10 (default, Feb 26 2021, 10:16:00)  [Clang 10.0.0 ]
pydicom      | 2.1.2
gdcm         | _module not found_
jpeg_ls      | _module not found_
numpy        | 1.20.3
PIL          | 8.2.0
```
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_handler_util.py b/pydicom/tests/test_handler_util.py
--- a/pydicom/tests/test_handler_util.py
+++ b/pydicom/tests/test_handler_util.py
@@ -1252,6 +1252,11 @@ def test_linear(self):
         out = _expand_segmented_lut(data, 'H')
         assert [-400, -320, -240, -160, -80, 0] == out

+        # Positive slope, floating point steps
+        data = (0, 1, 163, 1, 48, 255)
+        out = _expand_segmented_lut(data, 'H')
+        assert (1 + 48) == len(out)
+
         # No slope
         data = (0, 2, 0, 28672, 1, 5, 28672)
         out = _expand_segmented_lut(data, 'H')
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1416(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1416',
        'repo': 'pydicom/pydicom',
        'base_commit': '699c9f0a8e190d463dd828822106250523d38154',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_handler_util.py::TestNumpy_ExpandSegmentedLUT::test_linear"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
