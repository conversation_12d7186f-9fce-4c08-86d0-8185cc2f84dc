# == SWE data pydicom__pydicom_1428 problem statement:
# == Git info: pydicom/pydicom, 674da68db47a71ee6929288a047b56cf31cf8168

import pytest

problem_statement = r"""
Allow to search a list of elements in a `FileSet` while only loading instances once, to drastically improve execution time
**Is your feature request related to a problem? Please describe.**
Currently, `fileset.FileSet.find_values` only allows for elements to be searched for one at a time. When executing this action while setting `load` to `True`, this results in a substantial overhead.

**Describe the solution you'd like**
The following example code allows loading the instances once, and iterating over a list of elements to find:
```python
def find_values_quick(self, elements, instances=None):
    results = {element: [] for element in elements}
    instances = instances or iter(self)
    for instance in instances:
        instance = instance.load()
        for element in elements:
            if element not in instance:
                continue
            val = instance[element].value
            if val not in results[element]:
                results[element].append(val)
    return results
```
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_fileset.py b/pydicom/tests/test_fileset.py
--- a/pydicom/tests/test_fileset.py
+++ b/pydicom/tests/test_fileset.py
@@ -1945,33 +1945,45 @@ def test_find_load(self, private):
     def test_find_values(self, private):
         \"\"\"Test searching the FileSet for element values.\"\"\"
         fs = FileSet(private)
-        assert ['77654033', '98890234'] == fs.find_values("PatientID")
-        assert (
-            [
+        expected = {
+            "PatientID": ['77654033', '98890234'],
+            "StudyDescription": [
                 'XR C Spine Comp Min 4 Views',
                 'CT, HEAD/BRAIN WO CONTRAST',
                 '',
                 'Carotids',
                 'Brain',
-                'Brain-MRA'
-            ] == fs.find_values("StudyDescription")
-        )
+                'Brain-MRA',
+            ],
+        }
+        for k, v in expected.items():
+            assert fs.find_values(k) == v
+        assert fs.find_values(list(expected.keys())) == expected

     def test_find_values_load(self, private):
         \"\"\"Test FileSet.find_values(load=True).\"\"\"
         fs = FileSet(private)
+        search_element = "PhotometricInterpretation"
         msg = (
             r"None of the records in the DICOMDIR dataset contain "
-            r"the query element, consider using the 'load' parameter "
+            fr"\['{search_element}'\], consider using the 'load' parameter "
             r"to expand the search to the corresponding SOP instances"
         )
         with pytest.warns(UserWarning, match=msg):
-            results = fs.find_values("PhotometricInterpretation", load=False)
+            results = fs.find_values(search_element, load=False)
             assert not results

-        assert ['MONOCHROME1', 'MONOCHROME2'] == fs.find_values(
-            "PhotometricInterpretation", load=True
-        )
+        assert fs.find_values(search_element, load=True) == [
+            'MONOCHROME1', 'MONOCHROME2'
+        ]
+
+        with pytest.warns(UserWarning, match=msg):
+            results = fs.find_values([search_element], load=False)
+            assert not results[search_element]
+
+        assert (
+            fs.find_values([search_element], load=True)
+        ) == {search_element: ['MONOCHROME1', 'MONOCHROME2']}

     def test_empty_file_id(self, dicomdir):
         \"\"\"Test loading a record with an empty File ID.\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1428(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1428',
        'repo': 'pydicom/pydicom',
        'base_commit': '674da68db47a71ee6929288a047b56cf31cf8168',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_fileset.py::TestFileSet_Load::test_find_values", "pydicom/tests/test_fileset.py::TestFileSet_Load::test_find_values_load"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
