# == SWE data pydicom__pydicom_933 problem statement:
# == Git info: pydicom/pydicom, 38436b6824c079564b8760ea6acfa4c0fd3ee9c3

import pytest

problem_statement = r"""
Deferred Read Fails For File-Like Objects
#### Description
Deferred reads are failing when dcmread is passed a file-like object (instead of a filepath).  There are two old issues from 2014 which describe the same issue which were apparently fixed, but I'm still seeing it on v1.3:
https://github.com/pydicom/pydicom/issues/104
https://github.com/pydicom/pydicom/issues/74

#### Steps/Code to Reproduce
```
import io
import pydicom

with open("./0.dcm", "rb") as fp:
   data = fp.read()
filelike = io.BytesIO(data)

dataset = pydicom.dcmread(filelike, defer_size=1024)
print(len(dataset.PixelData))
```

#### Expected Results
Pydicom should hold onto the supplied file-like and use that for the deferred read, rather than trying to grab the file-like's .name/.filename attr and use that to re-open.  It could also hold onto it's own open'd file-like (if supplied a file_path) and use that for deferred reads to simplify things.

#### Actual Results
Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
  File "/usr/local/lib/python3.6/dist-packages/pydicom/dataset.py", line 747, in __getattr__
    data_elem = self[tag]
  File "/usr/local/lib/python3.6/dist-packages/pydicom/dataset.py", line 826, in __getitem__
    data_elem)
  File "/usr/local/lib/python3.6/dist-packages/pydicom/filereader.py", line 911, in read_deferred_data_element
    raise IOError("Deferred read -- original filename not stored. "
OSError: Deferred read -- original filename not stored. Cannot re-open

#### Versions
Linux-4.18.0-25-generic-x86_64-with-Ubuntu-18.10-cosmic
Python 3.6.7 (default, Oct 22 2018, 11:32:17)
pydicom 1.3.0
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filereader.py b/pydicom/tests/test_filereader.py
--- a/pydicom/tests/test_filereader.py
+++ b/pydicom/tests/test_filereader.py
@@ -3,6 +3,7 @@
 \"\"\"Unit tests for the pydicom.filereader module.\"\"\"

 import gzip
+import io
 from io import BytesIO
 import os
 import shutil
@@ -918,6 +919,14 @@ def test_zipped_deferred(self):
         # the right place, it was re-opened as a normal file, not a zip file
         ds.InstanceNumber

+    def test_filelike_deferred(self):
+        \"\"\"Deferred values work with file-like objects.\"\"\"
+        with open(ct_name, 'rb') as fp:
+            data = fp.read()
+        filelike = io.BytesIO(data)
+        dataset = pydicom.dcmread(filelike, defer_size=1024)
+        assert 32768 == len(dataset.PixelData)
+

 class TestReadTruncatedFile(object):
     def testReadFileWithMissingPixelData(self):
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_933(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-933',
        'repo': 'pydicom/pydicom',
        'base_commit': '38436b6824c079564b8760ea6acfa4c0fd3ee9c3',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filereader.py::TestDeferredRead::test_filelike_deferred"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
