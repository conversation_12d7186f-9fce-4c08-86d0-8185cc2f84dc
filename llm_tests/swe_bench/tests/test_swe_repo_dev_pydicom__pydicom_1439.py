# == SWE data pydicom__pydicom_1439 problem statement:
# == Git info: pydicom/pydicom, 506ecea8f378dc687d5c504788fc78810a190b7a

import pytest

problem_statement = r"""
Exception decompressing RLE encoded data with non-conformant padding
Getting Following error
"Could not convert:  The amount of decoded RLE segment data doesn't match the expected amount (786433 vs. 786432 bytes)"
For following code
plt.imsave(os.path.join(output_folder,file)+'.png', convert_color_space(ds.pixel_array, ds[0x28,0x04].value, 'RGB'))
Also attaching DICOM file
[US.1.2.156.112536.1.2127.130145051254127131.13912524190.144.txt](https://github.com/pydicom/pydicom/files/6799721/US.1.2.156.112536.1.2127.130145051254127131.13912524190.144.txt)

Please remove .txt extension to use DICOM file



""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_rle_pixel_data.py b/pydicom/tests/test_rle_pixel_data.py
--- a/pydicom/tests/test_rle_pixel_data.py
+++ b/pydicom/tests/test_rle_pixel_data.py
@@ -881,11 +881,10 @@ def test_invalid_nr_segments_raises(self, header, samples, bits):
                 header, rows=1, columns=1, nr_samples=samples, nr_bits=bits
             )

-    def test_invalid_frame_data_raises(self):
-        \"\"\"Test that invalid segment data raises exception.\"\"\"
+    def test_invalid_segment_data_raises(self):
+        \"\"\"Test invalid segment data raises exception\"\"\"
         ds = dcmread(RLE_16_1_1F)
         pixel_data = defragment_data(ds.PixelData)
-        # Missing byte
         msg = r"amount \(4095 vs. 4096 bytes\)"
         with pytest.raises(ValueError, match=msg):
             _rle_decode_frame(
@@ -896,13 +895,19 @@ def test_invalid_frame_data_raises(self):
                 ds.BitsAllocated
             )

-        # Extra byte
-        msg = r'amount \(4097 vs. 4096 bytes\)'
-        with pytest.raises(ValueError, match=msg):
-            _rle_decode_frame(
+    def test_nonconf_segment_padding_warns(self):
+        \"\"\"Test non-conformant segment padding warns\"\"\"
+        ds = dcmread(RLE_16_1_1F)
+        pixel_data = defragment_data(ds.PixelData)
+        msg = (
+            r"The decoded RLE segment contains non-conformant padding - 4097 "
+            r"vs. 4096 bytes expected"
+        )
+        with pytest.warns(UserWarning, match=msg):
+            frame = _rle_decode_frame(
                 pixel_data + b'\x00\x01',
-                ds.Rows,
-                ds.Columns,
+                4096,
+                1,
                 ds.SamplesPerPixel,
                 ds.BitsAllocated
             )
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1439(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1439',
        'repo': 'pydicom/pydicom',
        'base_commit': '506ecea8f378dc687d5c504788fc78810a190b7a',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_rle_pixel_data.py::TestNumpy_RLEDecodeFrame::test_nonconf_segment_padding_warns"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
