# == SWE data pydicom__pydicom_1000 problem statement:
# == Git info: pydicom/pydicom, 14acdd4d767ee55e8e84daeb6f80f81ef4748fee

import pytest

problem_statement = r"""
Heuristic for Explicit VR acting in sequence datasets
**Describe the bug**
There is a check to confirm implicit VR by looking for two ascii characters and switching to explicit with a warning (#823).  It was thought this was safe because length in first data elements would not be that large.  However, in sequence item datasets this may not be true.

Noted in google group conversation at https://groups.google.com/forum/#!topic/pydicom/VUmvUYmQxc0 (note that the title of that thread is not correct. that was not the problem).

Test demonstrating it and fix already done - PR to follow shortly.

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filereader.py b/pydicom/tests/test_filereader.py
--- a/pydicom/tests/test_filereader.py
+++ b/pydicom/tests/test_filereader.py
@@ -22,6 +22,7 @@
 from pydicom.errors import InvalidDicomError
 from pydicom.filebase import DicomBytesIO
 from pydicom.filereader import data_element_generator
+from pydicom.sequence import Sequence
 from pydicom.tag import Tag, TupleTag
 from pydicom.uid import ImplicitVRLittleEndian
 import pydicom.valuerep
@@ -729,6 +730,35 @@ def test_explicit_vr_expected_implicit_used_strict(self):
                 self.ds_implicit, is_implicit_VR=False, is_little_endian=True
             )

+    def test_seq_item_looks_like_explicit_VR(self):
+        # For issue 999.
+
+        # Set up an implicit VR dataset with a "normal" group 8 tag,
+        # followed by a sequence with an item (dataset) having
+        # a data element length that looks like a potential valid VR
+        ds = Dataset()
+        ds.file_meta = Dataset()
+        ds.file_meta.MediaStorageSOPClassUID = "1.1.1"
+        ds.file_meta.MediaStorageSOPInstanceUID = "2.2.2"
+        ds.is_implicit_VR = True
+        ds.is_little_endian = True
+        ds.SOPClassUID = '9.9.9'  # First item group 8 in top-level dataset
+        seq = Sequence()
+        seq_ds = Dataset()
+        seq_ds.BadPixelImage = b"\3" * 0x5244  # length looks like "DR"
+        seq.append(seq_ds)
+        ds.ReferencedImageSequence = seq
+
+        dbio = DicomBytesIO()
+        ds.save_as(dbio, write_like_original=False)
+
+        # Now read the constructed dataset back in
+        # In original issue, shows warning that has detected what appears
+        # to be Explicit VR, then throws NotImplemented for the unknown VR
+        dbio.seek(0)
+        ds = dcmread(dbio)
+        ds.remove_private_tags()  # forces it to actually parse SQ
+

 class TestUnknownVR(object):
     @pytest.mark.parametrize(
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1000(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1000',
        'repo': 'pydicom/pydicom',
        'base_commit': '14acdd4d767ee55e8e84daeb6f80f81ef4748fee',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filereader.py::TestIncorrectVR::test_seq_item_looks_like_explicit_VR"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
