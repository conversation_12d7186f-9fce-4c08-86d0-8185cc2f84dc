# == SWE data pydicom__pydicom_811 problem statement:
# == Git info: pydicom/pydicom, 7d0889e7143f5d4773fa74606efa816ed4e54c9f

import pytest

problem_statement = r"""
0 byte file causes traceback on dcmreader
<!-- Instructions For Filing a Bug: https://github.com/pydicom/pydicom/blob/master/CONTRIBUTING.md#filing-bugs -->

#### Description
Trying to open a 0 byte file with dcmreader causes a traceback originating in the read_partial method. The problem is line 692 in filereader.py (GitHub):
`    fileobj.seek(-1, 1)`
Changing this to:
`    if peek != b'':
        fileobj.seek(-1, 1)`
Appears to solve the problem, but I don't have the experience to test thoroughly.

#### Steps/Code to Reproduce
Create a 0 byte file
$ touch mysillyfile.dcm
Start python, import pydicom and read the the file
$ python3
Python 3.6.8 (default, Jan  3 2019, 16:11:14)
[GCC 8.2.1 20181215 (Red Hat 8.2.1-6)] on linux
Type "help", "copyright", "credits" or "license" for more information.
>>> import pydicom
>>> image = pydicom.dcmread('mysillyfile.dcm',force=True)

#### Expected Results
Should either warn that the file is not DICOM or exit gracefully

#### Actual Results
Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
  File "/usr/local/lib/python3.6/site-packages/pydicom/filereader.py", line 880, in dcmread
    force=force, specific_tags=specific_tags)
  File "/usr/local/lib/python3.6/site-packages/pydicom/filereader.py", line 693, in read_partial
    fileobj.seek(-1, 1)
OSError: [Errno 22] Invalid argument

#### Versions
>>> import platform; print(platform.platform())
Linux-4.19.16-200.fc28.x86_64-x86_64-with-fedora-28-Twenty_Eight
>>> import sys; print("Python", sys.version)
Python 3.6.8 (default, Jan  3 2019, 16:11:14)
[GCC 8.2.1 20181215 (Red Hat 8.2.1-6)]
>>> import numpy; print("numpy", numpy.__version__)
numpy 1.16.1
>>> import pydicom; print("pydicom", pydicom.__version__)
pydicom 1.2.2
>>>

Regards
Alan

<!-- Thanks for contributing! -->

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filereader.py b/pydicom/tests/test_filereader.py
--- a/pydicom/tests/test_filereader.py
+++ b/pydicom/tests/test_filereader.py
@@ -672,6 +672,14 @@ def test_no_dataset(self):
         self.assertEqual(ds.file_meta, Dataset())
         self.assertEqual(ds[:], Dataset())

+    def test_empty_file(self):
+        \"\"\"Test reading no elements from file produces empty Dataset\"\"\"
+        with tempfile.NamedTemporaryFile() as f:
+            ds = dcmread(f, force=True)
+            self.assertTrue(ds.preamble is None)
+            self.assertEqual(ds.file_meta, Dataset())
+            self.assertEqual(ds[:], Dataset())
+
     def test_dcmread_does_not_raise(self):
         \"\"\"Test that reading from DicomBytesIO does not raise on EOF.
         Regression test for #358.\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_811(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-811',
        'repo': 'pydicom/pydicom',
        'base_commit': '7d0889e7143f5d4773fa74606efa816ed4e54c9f',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filereader.py::ReaderTests::test_empty_file"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
