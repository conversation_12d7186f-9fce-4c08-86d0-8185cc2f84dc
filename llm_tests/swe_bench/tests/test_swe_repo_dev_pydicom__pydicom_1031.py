# == SWE data pydicom__pydicom_1031 problem statement:
# == Git info: pydicom/pydicom, 64f5b8daaa798836579c56912244b7732ab073be

import pytest

problem_statement = r"""
Crash writing DICOM with 1.4.0
pydicom 1.4.0
Windows-10-10.0.18362-SP0
Python  3.7.4 (tags/v3.7.4:e09359112e, Jul  8 2019, 20:34:20) [MSC v.1916 64 bit (AMD64)]
GDCM 3.0.2
Pillow 7.0.0

Type error raises when writing file with pydicom 1.4.0, works in 1.3.0.

```
ds = pydicom.read_file('fail2404.anon.dcm')
#print(ds.get((0x0040, 0x0275)))
ds.save_as('bort.dcm')
```

Interestingly, the crash goes away if the offending tag is accessed (uncomment the print and then the `save_as` works fine).

```
Traceback (most recent call last):
  File "C:\Program Files\Python37\lib\site-packages\pydicom\tag.py", line 30, in tag_in_exception
    yield
  File "C:\Program Files\Python37\lib\site-packages\pydicom\filewriter.py", line 555, in write_dataset
    write_data_element(fp, dataset.get_item(tag), dataset_encoding)
  File "C:\Program Files\Python37\lib\site-packages\pydicom\filewriter.py", line 463, in write_data_element
    buffer.write(data_element.value)
TypeError: a bytes-like object is required, not 'list'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "./pydcmbug.py", line 7, in <module>
    ds.save_as('bort.dcm')
  File "C:\Program Files\Python37\lib\site-packages\pydicom\dataset.py", line 1810, in save_as
    pydicom.dcmwrite(filename, self, write_like_original)
  File "C:\Program Files\Python37\lib\site-packages\pydicom\filewriter.py", line 946, in dcmwrite
    write_dataset(fp, get_item(dataset, slice(0x00010000, None)))
  File "C:\Program Files\Python37\lib\site-packages\pydicom\filewriter.py", line 555, in write_dataset
    write_data_element(fp, dataset.get_item(tag), dataset_encoding)
  File "C:\Program Files\Python37\lib\contextlib.py", line 130, in __exit__
    self.gen.throw(type, value, traceback)
  File "C:\Program Files\Python37\lib\site-packages\pydicom\tag.py", line 37, in tag_in_exception
    raise type(ex)(msg)
TypeError: With tag (0040, 0275) got exception: a bytes-like object is required, not 'list'
Traceback (most recent call last):
  File "C:\Program Files\Python37\lib\site-packages\pydicom\tag.py", line 30, in tag_in_exception
    yield
  File "C:\Program Files\Python37\lib\site-packages\pydicom\filewriter.py", line 555, in write_dataset
    write_data_element(fp, dataset.get_item(tag), dataset_encoding)
  File "C:\Program Files\Python37\lib\site-packages\pydicom\filewriter.py", line 463, in write_data_element
    buffer.write(data_element.value)
TypeError: a bytes-like object is required, not 'list'
```

[fail.zip](https://github.com/pydicom/pydicom/files/4072693/fail.zip)

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filewriter.py b/pydicom/tests/test_filewriter.py
--- a/pydicom/tests/test_filewriter.py
+++ b/pydicom/tests/test_filewriter.py
@@ -15,11 +15,11 @@

 from pydicom._storage_sopclass_uids import CTImageStorage
 from pydicom import config, __version_info__, uid
-from pydicom.data import get_testdata_files, get_charset_files
+from pydicom.data import get_testdata_file, get_charset_files
 from pydicom.dataset import Dataset, FileDataset
 from pydicom.dataelem import DataElement, RawDataElement
 from pydicom.filebase import DicomBytesIO
-from pydicom.filereader import dcmread, read_dataset
+from pydicom.filereader import dcmread, read_dataset, read_file
 from pydicom.filewriter import (write_data_element, write_dataset,
                                 correct_ambiguous_vr, write_file_meta_info,
                                 correct_ambiguous_vr_element, write_numbers,
@@ -34,16 +34,16 @@
 from pydicom.values import convert_text
 from ._write_stds import impl_LE_deflen_std_hex

-rtplan_name = get_testdata_files("rtplan.dcm")[0]
-rtdose_name = get_testdata_files("rtdose.dcm")[0]
-ct_name = get_testdata_files("CT_small.dcm")[0]
-mr_name = get_testdata_files("MR_small.dcm")[0]
-mr_implicit_name = get_testdata_files("MR_small_implicit.dcm")[0]
-mr_bigendian_name = get_testdata_files("MR_small_bigendian.dcm")[0]
-jpeg_name = get_testdata_files("JPEG2000.dcm")[0]
-no_ts = get_testdata_files("meta_missing_tsyntax.dcm")[0]
-color_pl_name = get_testdata_files("color-pl.dcm")[0]
-sc_rgb_name = get_testdata_files("SC_rgb.dcm")[0]
+rtplan_name = get_testdata_file("rtplan.dcm")
+rtdose_name = get_testdata_file("rtdose.dcm")
+ct_name = get_testdata_file("CT_small.dcm")
+mr_name = get_testdata_file("MR_small.dcm")
+mr_implicit_name = get_testdata_file("MR_small_implicit.dcm")
+mr_bigendian_name = get_testdata_file("MR_small_bigendian.dcm")
+jpeg_name = get_testdata_file("JPEG2000.dcm")
+no_ts = get_testdata_file("meta_missing_tsyntax.dcm")
+color_pl_name = get_testdata_file("color-pl.dcm")
+sc_rgb_name = get_testdata_file("SC_rgb.dcm")
 datetime_name = mr_name

 unicode_name = get_charset_files("chrH31.dcm")[0]
@@ -204,6 +204,15 @@ def test_write_removes_grouplength(self):
         # group length has been removed
         assert 0x00080000 not in ds

+    def test_write_empty_sequence(self):
+        \"\"\"Make sure that empty sequence is correctly written.\"\"\"
+        # regression test for #1030
+        ds = read_file(get_testdata_file('test-SR.dcm'))
+        ds.save_as(self.file_out)
+        self.file_out.seek(0)
+        ds = read_file(self.file_out)
+        assert ds.PerformedProcedureCodeSequence == []
+

 class TestScratchWriteDateTime(TestWriteFile):
     \"\"\"Write and reread simple or multi-value DA/DT/TM data elements\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1031(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1031',
        'repo': 'pydicom/pydicom',
        'base_commit': '64f5b8daaa798836579c56912244b7732ab073be',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filewriter.py::TestWriteFile::test_write_empty_sequence", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_empty_sequence"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
