# == SWE data pydicom__pydicom_1562 problem statement:
# == Git info: pydicom/pydicom, e1a035a88fe36d466579b2f3940bde5b8b1bc84d

import pytest

problem_statement = r"""
Revise the type annotation for pydicom.datadict.dictionary_has_tag()
**Describe the bug**

The documentation of [`pydicom.datadict.dictionary_has_tag()`](https://pydicom.github.io/pydicom/dev/reference/generated/pydicom.datadict.dictionary_has_tag.html#pydicom.datadict.dictionary_has_tag) suggests that a query using keywords (instead of a tag integer) would work:

```python
pydicom.datadict.dictionary_has_tag(tag: Union[int, str, Tuple[int, int], pydicom.tag.BaseTag]) -> bool
```

However, the function only accepts integer arguments.

```python
from pydicom.datadict import dictionary_has_tag, keyword_dict
dictionary_has_tag("PixelData")
# Returns False

dictionary_has_tag(keyword_dict["PixelData"])
# Returns True
```

(The problem may apply to other functions as well...)

**Expected behavior**
Following the docs, `dictionary_has_tag("PixelData")` should return True.

It would be nice, if the flexible conversion of tags from names or hex-tuples (as the type annotation suggests) would also be possible for this function.

**Your environment**
```text
module       | version
------       | -------
platform     | macOS-10.14.6-x86_64-i386-64bit
Python       | 3.9.0 (v3.9.0:9cf6752276, Oct  5 2020, 11:29:23)  [Clang 6.0 (clang-600.0.57)]
pydicom      | 2.2.2
gdcm         | _module not found_
jpeg_ls      | _module not found_
numpy        | 1.20.1
PIL          | 8.0.1
pylibjpeg    | _module not found_
openjpeg     | _module not found_
libjpeg      | _module not found_
```


""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_dictionary.py b/pydicom/tests/test_dictionary.py
--- a/pydicom/tests/test_dictionary.py
+++ b/pydicom/tests/test_dictionary.py
@@ -30,6 +30,8 @@ def test_dict_has_tag(self):
         \"\"\"Test dictionary_has_tag\"\"\"
         assert dictionary_has_tag(0x00100010)
         assert not dictionary_has_tag(0x11110010)
+        assert dictionary_has_tag("PatientName")
+        assert not dictionary_has_tag("PatientMane")

     def test_repeater_has_tag(self):
         \"\"\"Test repeater_has_tag\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1562(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1562',
        'repo': 'pydicom/pydicom',
        'base_commit': 'e1a035a88fe36d466579b2f3940bde5b8b1bc84d',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_dictionary.py::TestDict::test_dict_has_tag"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
