# == SWE data pydicom__pydicom_958 problem statement:
# == Git info: pydicom/pydicom, 40652fc0a18fd9f1204cb3d4b9829e3a8be5cbe0

import pytest

problem_statement = r"""
Encoding to ISO 2022 IR 159 doesn't work
<!-- Instructions For Filing a Bug: https://github.com/pydicom/pydicom/blob/master/CONTRIBUTING.md#filing-bugs -->

#### Description
Encoding to ISO 2022 IR 159 doesn't work even if 'ISO 2022 IR 159' is passed to pydicom.charset.convert_encodings.

#### Steps/Code to Reproduce
ISO 2022 IR 159 is designed as supplement characters to ISO 2022 IR 87. So these characters are not frequent use. But person name sometimes contains them. In the following example, the letter of "鷗" is only in ISO 2022 IR 159. But we cannot encode them correctly.

```
import pydicom

japanese_pn = u"Mori^Ogai=森^鷗外=もり^おうがい"
specific_character_sets = ["ISO 2022 IR 6", "ISO 2022 IR 87", "ISO 2022 IR 159"]
expect_encoded = (
    b"\x4d\x6f\x72\x69\x5e\x4f\x67\x61\x69\x3d\x1b\x24\x42\x3f"
    b"\x39\x1b\x28\x42\x5e\x1b\x24\x28\x44\x6c\x3f\x1b\x24\x42"
    b"\x33\x30\x1b\x28\x42\x3d\x1b\x24\x42\x24\x62\x24\x6a\x1b"
    b"\x28\x42\x5e\x1b\x24\x42\x24\x2a\x24\x26\x24\x2c\x24\x24"
    b"\x1b\x28\x42"
)

python_encodings = pydicom.charset.convert_encodings(specific_character_sets)
actual_encoded = pydicom.charset.encode_string(japanese_pn, python_encodings)

print("actual:{}".format(actual_encoded))
print("expect:{}".format(expect_encoded))
```
#### Expected Results
<!-- Please paste or describe the expected results.
Example: No error is thrown and the name of the patient is printed.-->
```
b'Mori^Ogai=\x1b$B?9\x1b(B^\x1b$(Dl?\x1b$B30\x1b(B=\x1b$B$b$j\x1b(B^\x1b$B$*$&$,$$\x1b(B'
```
#### Actual Results
<!-- Please paste or specifically describe the actual output or traceback.
(Use %xmode to deactivate ipython's trace beautifier)
Example: ```AttributeError: 'FileDataset' object has no attribute 'PatientID'```
-->
```
b'Mori^Ogai=?^??=??^????'
```

And the followin exception occurs.

```
/PATH/TO/MY/PYTHON/PACKAGES/pydicom/charset.py:488: UserWarning: Failed to encode value with encodings: iso8859, iso2022_jp, iso-2022-jp - using replacement characters in encoded string
  .format(', '.join(encodings)))
```

#### Versions
<!--
Please run the following snippet and paste the output below.
import platform; print(platform.platform())
import sys; print("Python", sys.version)
import pydicom; print("pydicom", pydicom.__version__)
-->
```
Linux-4.15.0-55-generic-x86_64-with-debian-buster-sid
Python 3.6.9 |Anaconda, Inc.| (default, Jul 30 2019, 19:07:31)
[GCC 7.3.0]
pydicom 1.3.0
```


<!-- Thanks for contributing! -->

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_charset.py b/pydicom/tests/test_charset.py
--- a/pydicom/tests/test_charset.py
+++ b/pydicom/tests/test_charset.py
@@ -9,6 +9,7 @@
 from pydicom.data import get_charset_files, get_testdata_files
 from pydicom.dataelem import DataElement
 from pydicom.filebase import DicomBytesIO
+from pydicom.valuerep import PersonName3

 # The file names (without '.dcm' extension) of most of the character test
 # files, together with the respective decoded PatientName tag values.
@@ -197,9 +198,9 @@ def test_bad_decoded_multi_byte_encoding(self):
                            b'\x1b$(D\xc4\xe9\xef\xed\xf5\xf3\xe9\xef\xf2')

         with pytest.warns(UserWarning, match='Failed to decode byte string '
-                                             'with encodings: iso-2022-jp'):
+                                             'with encodings: iso2022_jp_2'):
             pydicom.charset.decode_element(elem, ['ISO 2022 IR 159'])
-            assert u'����������' == elem.value
+            assert u'���������' == elem.value

     def test_bad_decoded_multi_byte_encoding_enforce_standard(self):
         \"\"\"Test handling bad encoding for single encoding if
@@ -207,7 +208,7 @@ def test_bad_decoded_multi_byte_encoding_enforce_standard(self):
         config.enforce_valid_values = True
         elem = DataElement(0x00100010, 'PN',
                            b'\x1b$(D\xc4\xe9\xef\xed\xf5\xf3\xe9\xef\xf2')
-        msg = ("'iso2022_jp' codec can't decode bytes in position 0-3: "
+        msg = ("'iso2022_jp_2' codec can't decode byte 0xc4 in position 4: "
                "illegal multibyte sequence")
         with pytest.raises(UnicodeDecodeError, match=msg):
             pydicom.charset.decode_element(elem, ['ISO 2022 IR 159'])
@@ -435,11 +436,26 @@ def test_japanese_multi_byte_personname(self):
             ds_out = dcmread(fp)
             assert original_string == ds_out.PatientName.original_string

+        japanese_pn = PersonName3(u"Mori^Ogai=森^鷗外=もり^おうがい")
+        pyencs = pydicom.charset.convert_encodings(["ISO 2022 IR 6",
+                                                    "ISO 2022 IR 87",
+                                                    "ISO 2022 IR 159"])
+        actual_encoded = bytes(japanese_pn.encode(pyencs))
+        expect_encoded = (
+            b"\x4d\x6f\x72\x69\x5e\x4f\x67\x61\x69\x3d\x1b\x24\x42\x3f"
+            b"\x39\x1b\x28\x42\x5e\x1b\x24\x28\x44\x6c\x3f\x1b\x24\x42"
+            b"\x33\x30\x1b\x28\x42\x3d\x1b\x24\x42\x24\x62\x24\x6a\x1b"
+            b"\x28\x42\x5e\x1b\x24\x42\x24\x2a\x24\x26\x24\x2c\x24\x24"
+            b"\x1b\x28\x42"
+        )
+        assert expect_encoded == actual_encoded
+
     def test_japanese_multi_byte_encoding(self):
         \"\"\"Test japanese multi byte strings are correctly encoded.\"\"\"
-        encoded = pydicom.charset.encode_string(u'あaｱア',
-                                                ['shift_jis', 'iso2022_jp'])
-        assert b'\x1b$B$"\x1b(Ja\x1b)I\xb1\x1b$B%"\x1b(J' == encoded
+        encoded = pydicom.charset.encode_string(u'あaｱア齩', ['shift_jis',
+                                                'iso2022_jp', 'iso2022_jp_2'])
+        expect = b'\x1b$B$"\x1b(Ja\x1b)I\xb1\x1b$B%"\x1b$(DmN\x1b(J'
+        assert expect == bytes(encoded)

     def test_bad_japanese_encoding(self):
         \"\"\"Test japanese multi byte strings are not correctly encoded.\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_958(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-958',
        'repo': 'pydicom/pydicom',
        'base_commit': '40652fc0a18fd9f1204cb3d4b9829e3a8be5cbe0',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_charset.py::TestCharset::test_bad_decoded_multi_byte_encoding", "pydicom/tests/test_charset.py::TestCharset::test_bad_decoded_multi_byte_encoding_enforce_standard", "pydicom/tests/test_charset.py::TestCharset::test_japanese_multi_byte_personname", "pydicom/tests/test_charset.py::TestCharset::test_japanese_multi_byte_encoding"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
