# == SWE data pydicom__pydicom_816 problem statement:
# == Git info: pydicom/pydicom, 3551f5b5a5f8d4de3ed92e5e479ac8c74a8c893a

import pytest

problem_statement = r"""
LookupError: unknown encoding: Not Supplied
#### Description
Output from `ds = pydicom.read_file(dcmFile)` (an RTSTRUCT dicom file, SOP UID 1.2.840.10008.*******.1.481.3) results in some tags throwing a LookupError: "LookupError: unknown encoding: Not Supplied"
Specific tags which cannot be decoded are as follows:
['DeviceSerialNumber',
 'Manufacturer',
 'ManufacturerModelName',
 'PatientID',
 'PatientName',
 'RTROIObservationsSequence',
 'ReferringPhysicianName',
 'SeriesDescription',
 'SoftwareVersions',
 'StructureSetLabel',
 'StructureSetName',
 'StructureSetROISequence',
 'StudyDescription',
 'StudyID']

I suspect that it's due to the fact that `ds.SpecificCharacterSet = 'Not Supplied'`, but when I try to set `ds.SpecificCharacterSet` to something reasonable (ie ISO_IR_100 or 'iso8859'), it doesn't seem to make any difference.

Reading the same file, with NO modifications, in gdcm does not result in any errors and all fields are readable.

#### Steps/Code to Reproduce
```py
import pydicom
ds = pydicom.read_file(dcmFile)
print(ds.PatientName)
```

#### Expected Results
No error is thrown and the name of the patient is printed.

#### Actual Results
Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Continuum\anaconda3\envs\itk\lib\site-packages\pydicom\valuerep.py", line 706, in __str__
    return '='.join(self.components).__str__()
  File "C:\Users\<USER>\AppData\Local\Continuum\anaconda3\envs\itk\lib\site-packages\pydicom\valuerep.py", line 641, in components
    self._components = _decode_personname(groups, self.encodings)
  File "C:\Users\<USER>\AppData\Local\Continuum\anaconda3\envs\itk\lib\site-packages\pydicom\valuerep.py", line 564, in _decode_personname
    for comp in components]
  File "C:\Users\<USER>\AppData\Local\Continuum\anaconda3\envs\itk\lib\site-packages\pydicom\valuerep.py", line 564, in <listcomp>
    for comp in components]
  File "C:\Users\<USER>\AppData\Local\Continuum\anaconda3\envs\itk\lib\site-packages\pydicom\charset.py", line 129, in decode_string
    return value.decode(encodings[0])
LookupError: unknown encoding: Not Supplied

#### Versions
Platform: Windows-10-10.0.17763-SP0
Python Version: Python 3.6.4 |Anaconda, Inc.| (default, Mar 12 2018, 20:20:50) [MSC v.1900 64 bit (AMD64)]
pydicom Version: pydicom 1.2.2

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_charset.py b/pydicom/tests/test_charset.py
--- a/pydicom/tests/test_charset.py
+++ b/pydicom/tests/test_charset.py
@@ -100,6 +100,26 @@ def test_standard_file(self):
         ds.decode()
         assert u'CompressedSamples^CT1' == ds.PatientName

+    def test_invalid_character_set(self):
+        \"\"\"charset: replace invalid encoding with default encoding\"\"\"
+        ds = dcmread(get_testdata_files("CT_small.dcm")[0])
+        ds.read_encoding = None
+        ds.SpecificCharacterSet = 'Unsupported'
+        with pytest.warns(UserWarning,
+                          match=u"Unknown encoding 'Unsupported' "
+                                u"- using default encoding instead"):
+            ds.decode()
+            assert u'CompressedSamples^CT1' == ds.PatientName
+
+    def test_invalid_character_set_enforce_valid(self):
+        \"\"\"charset: raise on invalid encoding\"\"\"
+        config.enforce_valid_values = True
+        ds = dcmread(get_testdata_files("CT_small.dcm")[0])
+        ds.read_encoding = None
+        ds.SpecificCharacterSet = 'Unsupported'
+        with pytest.raises(LookupError, match='unknown encoding: Unsupported'):
+            ds.decode()
+
     def test_decoding_with_specific_tags(self):
         \"\"\"Decoding is correctly applied even if  Specific Character Set
         is not in specific tags...\"\"\"
@@ -126,8 +146,8 @@ def test_bad_encoded_single_encoding(self):
         elem = DataElement(0x00100010, 'PN',
                            b'\xc4\xe9\xef\xed\xf5\xf3\xe9\xef\xf2')

-        with pytest.warns(UserWarning, match='Failed to decode byte string '
-                                             'with encoding UTF8'):
+        with pytest.warns(UserWarning, match="Failed to decode byte string "
+                                             "with encoding 'UTF8'"):
             pydicom.charset.decode(elem, ['ISO_IR 192'])
             assert u'���������' == elem.value

@@ -235,9 +255,11 @@ def test_patched_charset(self):
             # make sure no warning is issued for the correct value
             assert 1 == len(w)

-        # not patched incorrect encoding raises
+        # not patched incorrect encoding is replaced by default encoding
         elem = DataElement(0x00100010, 'PN', b'Buc^J\xc3\xa9r\xc3\xb4me')
-        with pytest.raises(LookupError):
+        with pytest.warns(UserWarning,
+                          match=u"Unknown encoding 'ISOIR 192' - "
+                                u"using default encoding instead"):
             pydicom.charset.decode(elem, ['ISOIR 192'])

         # Python encoding also can be used directly
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_816(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-816',
        'repo': 'pydicom/pydicom',
        'base_commit': '3551f5b5a5f8d4de3ed92e5e479ac8c74a8c893a',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_charset.py::TestCharset::test_invalid_character_set", "pydicom/tests/test_charset.py::TestCharset::test_bad_encoded_single_encoding", "pydicom/tests/test_charset.py::TestCharset::test_patched_charset"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
