# == SWE data pydicom__pydicom_866 problem statement:
# == Git info: pydicom/pydicom, b4b44acbf1ddcaf03df16210aac46cb3a8acd6b9

import pytest

problem_statement = r"""
Handle odd-sized dicoms with warning
<!-- Instructions For Filing a Bug: https://github.com/pydicom/pydicom/blob/master/CONTRIBUTING.md#filing-bugs -->

#### Description
<!-- Example: Attribute Error thrown when printing (0x0010, 0x0020) patient Id> 0-->

We have some uncompressed dicoms with an odd number of pixel bytes (saved by older versions of pydicom actually).

When we re-open with pydicom 1.2.2, we're now unable to extract the image, due to the change made by https://github.com/pydicom/pydicom/pull/601

Would it be possible to emit a warning instead of rejecting the dicom for such cases?

#### Version
1.2.2
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_numpy_pixel_data.py b/pydicom/tests/test_numpy_pixel_data.py
--- a/pydicom/tests/test_numpy_pixel_data.py
+++ b/pydicom/tests/test_numpy_pixel_data.py
@@ -986,13 +986,23 @@ def test_bad_length_raises(self):
         # Too short
         ds.PixelData = ds.PixelData[:-1]
         msg = (
-            r"The length of the pixel data in the dataset doesn't match the "
-            r"expected amount \(479999 vs. 480000 bytes\). The dataset may be "
-            r"corrupted or there may be an issue with the pixel data handler."
+            r"The length of the pixel data in the dataset \(479999 bytes\) "
+            r"doesn't match the expected length \(480000 bytes\). "
+            r"The dataset may be corrupted or there may be an issue "
+            r"with the pixel data handler."
         )
         with pytest.raises(ValueError, match=msg):
             get_pixeldata(ds)

+    def test_missing_padding_warns(self):
+        \"\"\"A warning shall be issued if the padding for odd data is missing.\"\"\"
+        ds = dcmread(EXPL_8_3_1F_ODD)
+        # remove the padding byte
+        ds.PixelData = ds.PixelData[:-1]
+        msg = "The pixel data length is odd and misses a padding byte."
+        with pytest.warns(UserWarning, match=msg):
+            get_pixeldata(ds)
+
     def test_change_photometric_interpretation(self):
         \"\"\"Test get_pixeldata changes PhotometricInterpretation if required.\"\"\"
         def to_rgb(ds):
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_866(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-866',
        'repo': 'pydicom/pydicom',
        'base_commit': 'b4b44acbf1ddcaf03df16210aac46cb3a8acd6b9',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_bad_length_raises", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_padding_warns"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
