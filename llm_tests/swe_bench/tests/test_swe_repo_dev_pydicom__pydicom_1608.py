# == SWE data pydicom__pydicom_1608 problem statement:
# == Git info: pydicom/pydicom, 37dd49e2754a10db22e7cde2ac18aa8afc1f3af6

import pytest

problem_statement = r"""
Unable to assign single element list to PN field
I am getting `AttributeError` while trying to assign a list of single element to a `PN` field.
It's converting `val` to a 2D array [here](https://github.com/pydicom/pydicom/blob/master/pydicom/filewriter.py#L328) when `VM` is 1.

**Code**
```
>>> from pydicom import dcmread, dcmwrite
>>> ds = dcmread("SOP1.dcm")
>>> a = ["name1"]
>>> b = ["name1", "name2"]
>>> ds.PatientName = a
>>> dcmwrite("out.dcm", ds)     # throws the error below
>>> ds.PatientName = b
>>> dcmwrite("out.dcm", ds)     # works fine
```

**Error**
```
Traceback (most recent call last):
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/tag.py", line 28, in tag_in_exception
    yield
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 662, in write_dataset
    write_data_element(fp, dataset.get_item(tag), dataset_encoding)
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 562, in write_data_element
    fn(buffer, elem, encodings=encodings)  # type: ignore[operator]
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 333, in write_PN
    enc = b'\\'.join([elem.encode(encodings) for elem in val])
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 333, in <listcomp>
    enc = b'\\'.join([elem.encode(encodings) for elem in val])
AttributeError: 'MultiValue' object has no attribute 'encode'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 1153, in dcmwrite
    _write_dataset(fp, dataset, write_like_original)
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 889, in _write_dataset
    write_dataset(fp, get_item(dataset, slice(0x00010000, None)))
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 662, in write_dataset
    write_data_element(fp, dataset.get_item(tag), dataset_encoding)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.8/lib/python3.8/contextlib.py", line 131, in __exit__
    self.gen.throw(type, value, traceback)
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/tag.py", line 32, in tag_in_exception
    raise type(exc)(msg) from exc
AttributeError: With tag (0010, 0010) got exception: 'MultiValue' object has no attribute 'encode'
Traceback (most recent call last):
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/tag.py", line 28, in tag_in_exception
    yield
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 662, in write_dataset
    write_data_element(fp, dataset.get_item(tag), dataset_encoding)
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 562, in write_data_element
    fn(buffer, elem, encodings=encodings)  # type: ignore[operator]
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 333, in write_PN
    enc = b'\\'.join([elem.encode(encodings) for elem in val])
  File "/Users/<USER>/virtualenv/deid/lib/python3.8/site-packages/pydicom/filewriter.py", line 333, in <listcomp>
    enc = b'\\'.join([elem.encode(encodings) for elem in val])
AttributeError: 'MultiValue' object has no attribute 'encode'
```
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_json.py b/pydicom/tests/test_json.py
--- a/pydicom/tests/test_json.py
+++ b/pydicom/tests/test_json.py
@@ -1,6 +1,7 @@
 # -*- coding: utf-8 -*-
 # Copyright 2008-2019 pydicom authors. See LICENSE file for details.
 import json
+from unittest import mock

 import pytest

@@ -271,13 +272,14 @@ def test_sort_order(self):
         assert ds_json.index('"00100020"') < ds_json.index('"00100030"')
         assert ds_json.index('"00100030"') < ds_json.index('"00100040"')

-    def test_suppress_invalid_tags(self):
+    @mock.patch("pydicom.DataElement.to_json_dict", side_effect=ValueError)
+    def test_suppress_invalid_tags(self, _):
         \"\"\"Test tags that raise exceptions don't if suppress_invalid_tags True.
         \"\"\"
         ds = Dataset()
-        ds.add_new(0x00100010, 'PN', ['Jane^Doe'])
+        ds.add_new(0x00100010, 'PN', 'Jane^Doe')

-        with pytest.raises(Exception):
+        with pytest.raises(ValueError):
             ds.to_json_dict()

         ds_json = ds.to_json_dict(suppress_invalid_tags=True)
diff --git a/pydicom/tests/test_valuerep.py b/pydicom/tests/test_valuerep.py
--- a/pydicom/tests/test_valuerep.py
+++ b/pydicom/tests/test_valuerep.py
@@ -1540,20 +1540,27 @@ def test_set_value(vr, pytype, vm0, vmN, keyword, disable_value_validation):
         assert value == elem.value

     # Test VM = 1
+    if vr != 'SQ':
+        ds = Dataset()
+        value = vmN[0]
+        setattr(ds, keyword, value)
+        elem = ds[keyword]
+        assert elem.value == value
+        assert value == elem.value
+
+    # Test VM = 1 as list
     ds = Dataset()
     value = vmN[0]
+    setattr(ds, keyword, [value])
+    elem = ds[keyword]
     if vr == 'SQ':
-        setattr(ds, keyword, [value])
-        elem = ds[keyword]
         assert elem.value[0] == value
         assert value == elem.value[0]
     else:
-        setattr(ds, keyword, value)
-        elem = ds[keyword]
         assert elem.value == value
         assert value == elem.value

-    if vr[0] == 'O':
+    if vr[0] == 'O' or vr == 'UN':
         return

     # Test VM > 1
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1608(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1608',
        'repo': 'pydicom/pydicom',
        'base_commit': '37dd49e2754a10db22e7cde2ac18aa8afc1f3af6',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_valuerep.py::test_set_value[AE-str-vm00-vmN0-Receiver]", "pydicom/tests/test_valuerep.py::test_set_value[AS-str-vm01-vmN1-PatientAge]", "pydicom/tests/test_valuerep.py::test_set_value[AT-int-vm02-vmN2-OffendingElement]", "pydicom/tests/test_valuerep.py::test_set_value[CS-str-vm03-vmN3-QualityControlSubject]", "pydicom/tests/test_valuerep.py::test_set_value[DA-str-vm04-vmN4-PatientBirthDate]", "pydicom/tests/test_valuerep.py::test_set_value[DS-str-vm05-vmN5-PatientWeight]", "pydicom/tests/test_valuerep.py::test_set_value[DS-int-vm06-vmN6-PatientWeight]", "pydicom/tests/test_valuerep.py::test_set_value[DS-float-vm07-vmN7-PatientWeight]", "pydicom/tests/test_valuerep.py::test_set_value[DT-str-vm08-vmN8-AcquisitionDateTime]", "pydicom/tests/test_valuerep.py::test_set_value[FD-float-vm09-vmN9-RealWorldValueLUTData]", "pydicom/tests/test_valuerep.py::test_set_value[FL-float-vm010-vmN10-VectorAccuracy]", "pydicom/tests/test_valuerep.py::test_set_value[IS-str-vm011-vmN11-BeamNumber]", "pydicom/tests/test_valuerep.py::test_set_value[IS-int-vm012-vmN12-BeamNumber]", "pydicom/tests/test_valuerep.py::test_set_value[IS-float-vm013-vmN13-BeamNumber]", "pydicom/tests/test_valuerep.py::test_set_value[LO-str-vm014-vmN14-DataSetSubtype]", "pydicom/tests/test_valuerep.py::test_set_value[LT-str-vm015-vmN15-ExtendedCodeMeaning]", "pydicom/tests/test_valuerep.py::test_set_value[OB-bytes-vm016-vmN16-FillPattern]", "pydicom/tests/test_valuerep.py::test_set_value[OD-bytes-vm017-vmN17-DoubleFloatPixelData]", "pydicom/tests/test_valuerep.py::test_set_value[OF-bytes-vm018-vmN18-UValueData]", "pydicom/tests/test_valuerep.py::test_set_value[OL-bytes-vm019-vmN19-TrackPointIndexList]", "pydicom/tests/test_valuerep.py::test_set_value[OV-bytes-vm020-vmN20-SelectorOVValue]", "pydicom/tests/test_valuerep.py::test_set_value[OW-bytes-vm021-vmN21-TrianglePointIndexList]", "pydicom/tests/test_valuerep.py::test_set_value[PN-str-vm022-vmN22-PatientName]", "pydicom/tests/test_valuerep.py::test_set_value[SH-str-vm023-vmN23-CodeValue]", "pydicom/tests/test_valuerep.py::test_set_value[SL-int-vm024-vmN24-RationalNumeratorValue]", "pydicom/tests/test_valuerep.py::test_set_value[SS-int-vm026-vmN26-SelectorSSValue]", "pydicom/tests/test_valuerep.py::test_set_value[ST-str-vm027-vmN27-InstitutionAddress]", "pydicom/tests/test_valuerep.py::test_set_value[SV-int-vm028-vmN28-SelectorSVValue]", "pydicom/tests/test_valuerep.py::test_set_value[TM-str-vm029-vmN29-StudyTime]", "pydicom/tests/test_valuerep.py::test_set_value[UC-str-vm030-vmN30-LongCodeValue]", "pydicom/tests/test_valuerep.py::test_set_value[UI-str-vm031-vmN31-SOPClassUID]", "pydicom/tests/test_valuerep.py::test_set_value[UL-int-vm032-vmN32-SimpleFrameList]", "pydicom/tests/test_valuerep.py::test_set_value[UN-bytes-vm033-vmN33-SelectorUNValue]", "pydicom/tests/test_valuerep.py::test_set_value[UR-str-vm034-vmN34-CodingSchemeURL]", "pydicom/tests/test_valuerep.py::test_set_value[US-int-vm035-vmN35-SourceAcquisitionBeamNumber]", "pydicom/tests/test_valuerep.py::test_set_value[UT-str-vm036-vmN36-StrainAdditionalInformation]", "pydicom/tests/test_valuerep.py::test_set_value[UV-int-vm037-vmN37-SelectorUVValue]"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
