# == SWE data pydicom__pydicom_1228 problem statement:
# == Git info: pydicom/pydicom, 8112bb69bfc0423c3a08cb89e7960defbe7237bf

import pytest

problem_statement = r"""
Add Tag and VR to the bulk data handling in `from_json`
Currently, if you convert back to a Dataset format from a JSON format, you MUST re-hydrate all of the bulk data URI's or you will loose the information.

This causes a problem if you just wish to use the Dataset's header (maybe to extract some data, or rearrange some data), because now you have to pay the cost of getting all the pixel data and then handling the pixel data again upon conversion back to JSON

**Describe the solution you'd like**
Add the tag and the vr to the bulk data handler in `from_json` (this can be done in a backwards compatible way). This will allow the user to store the BulkDataURI's by tag in a map, return dummy data large enough to trigger the bulk handling when to_json is called next, and to use the map to convert back to the original URI's when bulk handling is triggered from to_json.

I'm going to drop a PR tomorrow that does this in a fully backward compatible, non-breaking fashion.

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_json.py b/pydicom/tests/test_json.py
--- a/pydicom/tests/test_json.py
+++ b/pydicom/tests/test_json.py
@@ -334,7 +334,18 @@ def test_invalid_bulkdata_uri(self):
             Dataset.from_json(ds_json)

     def test_bulk_data_reader_is_called(self):
-        def bulk_data_reader(_):
+        def bulk_data_reader(value):
+            return b'xyzzy'
+
+        json_data = {
+            "00091002": {"vr": "OB", "BulkDataURI": "https://a.dummy.url"}
+        }
+        ds = Dataset().from_json(json.dumps(json_data), bulk_data_reader)
+
+        assert b'xyzzy' == ds[0x00091002].value
+
+    def test_bulk_data_reader_is_called_2(self):
+        def bulk_data_reader(tag, vr, value):
             return b'xyzzy'

         json_data = {
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1228(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1228',
        'repo': 'pydicom/pydicom',
        'base_commit': '8112bb69bfc0423c3a08cb89e7960defbe7237bf',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_json.py::TestBinary::test_bulk_data_reader_is_called_2"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
