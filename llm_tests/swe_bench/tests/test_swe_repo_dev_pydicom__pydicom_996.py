# == SWE data pydicom__pydicom_996 problem statement:
# == Git info: pydicom/pydicom, d21e97c9a35b5c225dc3340faaa7c293e7c8ee9b

import pytest

problem_statement = r"""
Memory leaks when accessing sequence tags with Dataset.__getattr__.
**Describe the bug**
Accessing sequences via `Dataset.__getattr__` seems to leak memory. The bug occurred for me when I was processing many DICOMs and manipulating some tags contained in sequences and each leaked a bit of memory, ultimately crashing the process.

**Expected behavior**
Memory should not leak. It works correctly when you replace the `__getattr__` call with `__getitem__` (by manually constructing the necessary tag beforehand).

Without being an expert in the codebase, one difference I think that could explain it is that `__getattr__` sets `value.parent = self` for sequences while `__getitem__` doesn't seem to do that. Maybe this loop of references somehow confuses Python's garbage collection?

**Steps To Reproduce**
This increases the memory consumption of the Python process by about 700 MB on my machine. The DICOM file I've tested it with is 27MB and has one item in `SourceImageSequence`. Note that the memory leak plateaus after a while in this example, maybe because it's the same file. In my actual workflow when iterating over many different files, the process filled all memory and crashed.

```python
import pydicom
for i in range(100):
  dcm = pydicom.dcmread("my_dicom.dcm")
  test = dcm.SourceImageSequence
```

For comparison, this keeps the memory constant. `(0x0008, 0x2112)` is `SourceImageSequence`:

```python
import pydicom
import pydicom.tag
for i in range(100):
  dcm = pydicom.dcmread("my_dicom.dcm")
  test = dcm[pydicom.tag.TupleTag((0x0008, 0x2112))]
```

**Your environment**

```bash
Linux-4.15.0-72-generic-x86_64-with-Ubuntu-18.04-bionic
Python  3.6.8 (default, Jan 14 2019, 11:02:34)
pydicom  1.3.0
```

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filewriter.py b/pydicom/tests/test_filewriter.py
--- a/pydicom/tests/test_filewriter.py
+++ b/pydicom/tests/test_filewriter.py
@@ -875,47 +875,90 @@ def test_write_new_ambiguous(self):
         assert ds[0x00283002].VR == 'US'
         assert ds.LUTDescriptor == [1, 0]

-    def test_ambiguous_element_in_sequence_explicit(self):
-        \"\"\"Test that writing a sequence with an ambiguous element
-        as explicit transfer syntax works.\"\"\"
-        # regression test for #804
+    def dataset_with_modality_lut_sequence(self, pixel_repr):
         ds = Dataset()
-        ds.PixelRepresentation = 0
+        ds.PixelRepresentation = pixel_repr
         ds.ModalityLUTSequence = [Dataset()]
         ds.ModalityLUTSequence[0].LUTDescriptor = [0, 0, 16]
         ds.ModalityLUTSequence[0].LUTExplanation = None
         ds.ModalityLUTSequence[0].ModalityLUTType = 'US'  # US = unspecified
         ds.ModalityLUTSequence[0].LUTData = b'\x0000\x149a\x1f1c\xc2637'
-
         ds.is_little_endian = True
+        return ds
+
+    def test_ambiguous_element_in_sequence_explicit_using_attribute(self):
+        \"\"\"Test that writing a sequence with an ambiguous element
+        as explicit transfer syntax works if accessing the tag via keyword.\"\"\"
+        # regression test for #804
+        ds = self.dataset_with_modality_lut_sequence(pixel_repr=0)
         ds.is_implicit_VR = False
         fp = BytesIO()
         ds.save_as(fp, write_like_original=True)
-
         ds = dcmread(fp, force=True)
         assert 'US' == ds.ModalityLUTSequence[0][0x00283002].VR

-    def test_ambiguous_element_in_sequence_implicit(self):
+        ds = self.dataset_with_modality_lut_sequence(pixel_repr=1)
+        ds.is_implicit_VR = False
+        fp = BytesIO()
+        ds.save_as(fp, write_like_original=True)
+        ds = dcmread(fp, force=True)
+        assert 'SS' == ds.ModalityLUTSequence[0][0x00283002].VR
+
+    def test_ambiguous_element_in_sequence_explicit_using_index(self):
+        \"\"\"Test that writing a sequence with an ambiguous element
+        as explicit transfer syntax works if accessing the tag
+        via the tag number.\"\"\"
+        ds = self.dataset_with_modality_lut_sequence(pixel_repr=0)
+        ds.is_implicit_VR = False
+        fp = BytesIO()
+        ds.save_as(fp, write_like_original=True)
+        ds = dcmread(fp, force=True)
+        assert 'US' == ds[0x00283000][0][0x00283002].VR
+
+        ds = self.dataset_with_modality_lut_sequence(pixel_repr=1)
+        ds.is_implicit_VR = False
+        fp = BytesIO()
+        ds.save_as(fp, write_like_original=True)
+        ds = dcmread(fp, force=True)
+        assert 'SS' == ds[0x00283000][0][0x00283002].VR
+
+    def test_ambiguous_element_in_sequence_implicit_using_attribute(self):
         \"\"\"Test that reading a sequence with an ambiguous element
-        from a file with implicit transfer syntax works.\"\"\"
+        from a file with implicit transfer syntax works if accessing the
+        tag via keyword.\"\"\"
         # regression test for #804
-        ds = Dataset()
-        ds.PixelRepresentation = 0
-        ds.ModalityLUTSequence = [Dataset()]
-        ds.ModalityLUTSequence[0].LUTDescriptor = [0, 0, 16]
-        ds.ModalityLUTSequence[0].LUTExplanation = None
-        ds.ModalityLUTSequence[0].ModalityLUTType = 'US'  # US = unspecified
-        ds.ModalityLUTSequence[0].LUTData = b'\x0000\x149a\x1f1c\xc2637'
-
-        ds.is_little_endian = True
+        ds = self.dataset_with_modality_lut_sequence(pixel_repr=0)
         ds.is_implicit_VR = True
         fp = BytesIO()
         ds.save_as(fp, write_like_original=True)
         ds = dcmread(fp, force=True)
-        # we first have to access the value to trigger correcting the VR
-        assert 16 == ds.ModalityLUTSequence[0].LUTDescriptor[2]
         assert 'US' == ds.ModalityLUTSequence[0][0x00283002].VR

+        ds = self.dataset_with_modality_lut_sequence(pixel_repr=1)
+        ds.is_implicit_VR = True
+        fp = BytesIO()
+        ds.save_as(fp, write_like_original=True)
+        ds = dcmread(fp, force=True)
+        assert 'SS' == ds.ModalityLUTSequence[0][0x00283002].VR
+
+    def test_ambiguous_element_in_sequence_implicit_using_index(self):
+        \"\"\"Test that reading a sequence with an ambiguous element
+        from a file with implicit transfer syntax works if accessing the tag
+        via the tag number.\"\"\"
+        ds = self.dataset_with_modality_lut_sequence(pixel_repr=0)
+        ds.is_implicit_VR = True
+        fp = BytesIO()
+        ds.save_as(fp, write_like_original=True)
+        ds = dcmread(fp, force=True)
+        assert 'US' == ds[0x00283000][0][0x00283002].VR
+
+        ds = self.dataset_with_modality_lut_sequence(pixel_repr=1)
+        ds.is_implicit_VR = True
+        fp = BytesIO()
+        ds.save_as(fp, write_like_original=True)
+        ds = dcmread(fp, force=True)
+        assert 'SS' == ds[0x00283000][0][0x00283002].VR
+

 class TestCorrectAmbiguousVRElement(object):
     \"\"\"Test filewriter.correct_ambiguous_vr_element\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_996(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-996',
        'repo': 'pydicom/pydicom',
        'base_commit': 'd21e97c9a35b5c225dc3340faaa7c293e7c8ee9b',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_implicit_using_index"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
