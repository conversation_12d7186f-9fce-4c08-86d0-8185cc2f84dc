# == SWE data pydicom__pydicom_1050 problem statement:
# == Git info: pydicom/pydicom, 00c248441ffb8b7d46c6d855b723e696a8f5aada

import pytest

problem_statement = r"""
LUT Descriptor tag with no value yields TypeError
**Describe the bug**
I have a DICOM image with the following tag (copied from ImageJ)

```
0028,1101  Red Palette Color Lookup Table Descriptor:
```

which corresponds to the raw data element, produced by [`DataElement_from_raw`](https://github.com/pydicom/pydicom/blob/v1.4.1/pydicom/dataelem.py#L699):
```
RawDataElement(tag=(0028, 1101), VR='US', length=0, value=None, value_tell=1850, is_implicit_VR=False, is_little_endian=True)
```

Because this tag is matched by the [LUT Descriptor tags](https://github.com/pydicom/pydicom/blob/v1.4.1/pydicom/dataelem.py#L696) and the value is empty (`None`), the [following line](https://github.com/pydicom/pydicom/blob/v1.4.1/pydicom/dataelem.py#L761):
```
if raw.tag in _LUT_DESCRIPTOR_TAGS and value[0] < 0:
```
results in
```
TypeError: 'NoneType' object is not subscriptable
```

**Expected behavior**

Given that I discovered this by parsing what seems to be a set of faulty DICOMs (mangled pixel data), I'm not sure if an error should be raised if the colour attribute value is not provided.

However, given that `value` can be `None` for other tags, the simple fix is

```python
try:
    if raw.tag in _LUT_DESCRIPTOR_TAGS and value[0] < 0:
        # We only fix the first value as the third value is 8 or 16
        value[0] += 65536
except TypeError:
    pass
```

(or test if `value` is iterable).

**Your environment**
```
Darwin-19.3.0-x86_64-i386-64bit
Python  3.7.6 | packaged by conda-forge | (default, Jan  7 2020, 22:05:27)
[Clang 9.0.1 ]
pydicom  1.4.1
```

Many thanks!
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filereader.py b/pydicom/tests/test_filereader.py
--- a/pydicom/tests/test_filereader.py
+++ b/pydicom/tests/test_filereader.py
@@ -674,6 +674,27 @@ def test_lut_descriptor(self):
             assert elem.VR == 'SS'
             assert elem.value == [62720, -2048, 16]

+    def test_lut_descriptor_empty(self):
+        \"\"\"Regression test for #1049: LUT empty raises.\"\"\"
+        bs = DicomBytesIO(b'\x28\x00\x01\x11\x53\x53\x00\x00')
+        bs.is_little_endian = True
+        bs.is_implicit_VR = False
+        ds = dcmread(bs, force=True)
+        elem = ds[0x00281101]
+        assert elem.value is None
+        assert elem.VR == 'SS'
+
+    def test_lut_descriptor_singleton(self):
+        \"\"\"Test LUT Descriptor with VM = 1\"\"\"
+        bs = DicomBytesIO(b'\x28\x00\x01\x11\x53\x53\x02\x00\x00\xf5')
+        bs.is_little_endian = True
+        bs.is_implicit_VR = False
+        ds = dcmread(bs, force=True)
+        elem = ds[0x00281101]
+        # No conversion to US if not a triplet
+        assert elem.value == -2816
+        assert elem.VR == 'SS'
+

 class TestIncorrectVR(object):
     def setup(self):
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1050(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1050',
        'repo': 'pydicom/pydicom',
        'base_commit': '00c248441ffb8b7d46c6d855b723e696a8f5aada',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filereader.py::TestReader::test_lut_descriptor_empty", "pydicom/tests/test_filereader.py::TestReader::test_lut_descriptor_singleton"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
