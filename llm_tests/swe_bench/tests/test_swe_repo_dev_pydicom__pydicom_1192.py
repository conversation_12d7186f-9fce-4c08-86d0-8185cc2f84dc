# == SWE data pydicom__pydicom_1192 problem statement:
# == Git info: pydicom/pydicom, 1f099ae0f75f0e2ed402a21702e584aac54a30ef

import pytest

problem_statement = r"""
"TypeError: 'NoneType' object is not subscriptable" when reading dcm file with empty string as Chartset and "use_none_as_empty_text_VR_value=True"
**Describe the bug**
Once thing I noticed is that `convert_encodings` in `charset.py` expects a list of encodings (according to the docstrings) from tag `0008,0005` but it can be just a value.

The problem is when reading Dicom files in production environments I noticed that some devices that are capturing the DICOMs are not very DICOM Compliant and is sending empty string , which it should be allowed as `0008,0005` is a 1C type, which means that if present it should have a valid value.

I enabled `use_none_as_empty_text_VR_value` to make sure other tags whose value should be float or int have None instead of empty string, but if `0008,0005` value is empty string is switched to None and `convert_encodings` fails with `TypeError: 'NoneType' object is not subscriptable`

**Expected behavior**
The expected behavior should be that if empty string or not present it should default to:
```
# default encoding if no encoding defined - corresponds to ISO IR 6 / ASCII
default_encoding = "iso8859"
```

**Steps To Reproduce**

out.dcm file if provided for testing with mock data but `Specific Character Set` set to empty string

If setting the `(0008, 0005) Specific Character Set` to empty string and setting `pydicom.config.use_none_as_empty_text_VR_value = True`

```
>>> import pydicom
>>> pydicom.config.datetime_conversion = True
>>> pydicom.config.allow_DS_float = True
>>> pydicom.config.use_none_as_empty_text_VR_value = True
>>> dataset = pydicom.dcmread("test.dcm")
Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
  File "/Users/<USER>/.virtualenvs/backend-api/lib/python3.7/site-packages/pydicom/filereader.py", line 871, in dcmread
    force=force, specific_tags=specific_tags)
  File "/Users/<USER>/.virtualenvs/backend-api/lib/python3.7/site-packages/pydicom/filereader.py", line 744, in read_partial
    specific_tags=specific_tags)
  File "/Users/<USER>/.virtualenvs/backend-api/lib/python3.7/site-packages/pydicom/filereader.py", line 383, in read_dataset
    encoding = convert_encodings(char_set)
  File "/Users/<USER>/.virtualenvs/backend-api/lib/python3.7/site-packages/pydicom/charset.py", line 638, in convert_encodings
    encodings = encodings[:]
TypeError: 'NoneType' object is not subscriptable
>>> pydicom.config.use_none_as_empty_text_VR_value = False
>>> dataset = pydicom.dcmread("test.dcm")
```
`(0008, 0005) Specific Character Set              CS: ''`

**Your environment**

```bash
python -m pydicom.env_info
module       | version
------       | -------
platform     | Darwin-19.6.0-x86_64-i386-64bit
Python       | 3.7.6 (default, Dec 30 2019, 19:38:26)  [Clang 11.0.0 (clang-1100.0.33.16)]
pydicom      | 2.0.0
gdcm         | _module not found_
jpeg_ls      | _module not found_
numpy        | _module not found_
PIL          | 7.0.0
```


[out.dcm.zip](https://github.com/pydicom/pydicom/files/5248618/out.dcm.zip)

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_charset.py b/pydicom/tests/test_charset.py
--- a/pydicom/tests/test_charset.py
+++ b/pydicom/tests/test_charset.py
@@ -140,6 +140,15 @@ def test_bad_charset(self):
         pydicom.charset.decode_element(elem, [])
         assert 'iso8859' in elem.value.encodings

+    def test_empty_charset(self):
+        \"\"\"Empty charset defaults to ISO IR 6\"\"\"
+        elem = DataElement(0x00100010, 'PN', 'CITIZEN')
+        pydicom.charset.decode_element(elem, [''])
+        assert ('iso8859',) == elem.value.encodings
+        elem = DataElement(0x00100010, 'PN', 'CITIZEN')
+        pydicom.charset.decode_element(elem, None)
+        assert ('iso8859',) == elem.value.encodings
+
     def test_bad_encoded_single_encoding(self, allow_invalid_values):
         \"\"\"Test handling bad encoding for single encoding\"\"\"
         elem = DataElement(0x00100010, 'PN',
@@ -189,6 +198,15 @@ def test_convert_python_encodings(self):
         encodings = ['iso_ir_126', 'iso_ir_144']
         assert encodings == pydicom.charset.convert_encodings(encodings)

+    def test_convert_empty_encoding(self):
+        \"\"\"Test that empty encodings are handled as default encoding\"\"\"
+        encodings = ''
+        assert ['iso8859'] == pydicom.charset.convert_encodings(encodings)
+        encodings = ['']
+        assert ['iso8859'] == pydicom.charset.convert_encodings(encodings)
+        encodings = None
+        assert ['iso8859'] == pydicom.charset.convert_encodings(encodings)
+
     def test_bad_decoded_multi_byte_encoding(self, allow_invalid_values):
         \"\"\"Test handling bad encoding for single encoding\"\"\"
         elem = DataElement(0x00100010, 'PN',
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1192(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1192',
        'repo': 'pydicom/pydicom',
        'base_commit': '1f099ae0f75f0e2ed402a21702e584aac54a30ef',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_charset.py::TestCharset::test_convert_empty_encoding"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
