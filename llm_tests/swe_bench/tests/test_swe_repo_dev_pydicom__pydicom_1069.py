# == SWE data pydicom__pydicom_1069 problem statement:
# == Git info: pydicom/pydicom, 30ac743bcaedbc06f0e0d5cef590cb173756eb2d

import pytest

problem_statement = r"""
apply_color_lut() incorrect exception when missing RedPaletteColorLUTDescriptor
**Describe the bug**
`AttributeError` when used on a dataset without `RedPaletteColorLookupTableDescriptor `

**Expected behavior**
Should raise `ValueError` for consistency with later exceptions

**Steps To Reproduce**
```python
from pydicom.pixel_data_handlers.util import apply_color_lut
ds = dcmread("CT_small.dcm")
arr = ds.apply_color_lut(arr, ds)
```
```
Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
  File ".../pydicom/pixel_data_handlers/util.py", line 116, in apply_color_lut
    lut_desc = ds.RedPaletteColorLookupTableDescriptor
  File ".../pydicom/dataset.py", line 768, in __getattr__
    return object.__getattribute__(self, name)
AttributeError: 'FileDataset' object has no attribute 'RedPaletteColorLookupTableDescriptor'
```
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_handler_util.py b/pydicom/tests/test_handler_util.py
--- a/pydicom/tests/test_handler_util.py
+++ b/pydicom/tests/test_handler_util.py
@@ -1150,6 +1150,15 @@ def test_first_map_negative(self):
         assert [60160, 25600, 37376] == list(rgb[arr == 130][0])
         assert ([60160, 25600, 37376] == rgb[arr == 130]).all()

+    def test_unchanged(self):
+        \"\"\"Test dataset with no LUT is unchanged.\"\"\"
+        # Regression test for #1068
+        ds = dcmread(MOD_16, force=True)
+        assert 'RedPaletteColorLookupTableDescriptor' not in ds
+        msg = r"No suitable Palette Color Lookup Table Module found"
+        with pytest.raises(ValueError, match=msg):
+            apply_color_lut(ds.pixel_array, ds)
+

 @pytest.mark.skipif(not HAVE_NP, reason="Numpy is not available")
 class TestNumpy_ExpandSegmentedLUT(object):
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1069(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1069',
        'repo': 'pydicom/pydicom',
        'base_commit': '30ac743bcaedbc06f0e0d5cef590cb173756eb2d',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_handler_util.py::TestNumpy_PaletteColor::test_unchanged"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
