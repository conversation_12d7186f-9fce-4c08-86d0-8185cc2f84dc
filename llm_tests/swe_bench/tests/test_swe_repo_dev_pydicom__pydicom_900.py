# == SWE data pydicom__pydicom_900 problem statement:
# == Git info: pydicom/pydicom, 3746878d8edf1cbda6fbcf35eec69f9ba79301ca

import pytest

problem_statement = r"""
can open my dicom, error in re.match('^ISO[^_]IR', encoding)
```
(test) root@DESKTOP-COPUCVT:/mnt/e/test# python3 mydicom.py
Traceback (most recent call last):
  File "/root/.local/share/virtualenvs/test-LINKoilU/lib/python3.6/site-packages/pydicom/charset.py", line 625, in convert_encodings
    py_encodings.append(python_encoding[encoding])
KeyError: 73

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "mydicom.py", line 12, in <module>
    pydicom.dcmread("DX.X.1.2.276.0.7230010.3.1.4.313262848.25.**********.444385.dcm")
  File "/root/.local/share/virtualenvs/test-LINKoilU/lib/python3.6/site-packages/pydicom/filereader.py", line 850, in dcmread
    force=force, specific_tags=specific_tags)
  File "/root/.local/share/virtualenvs/test-LINKoilU/lib/python3.6/site-packages/pydicom/filereader.py", line 728, in read_partial
    specific_tags=specific_tags)
  File "/root/.local/share/virtualenvs/test-LINKoilU/lib/python3.6/site-packages/pydicom/filereader.py", line 382, in read_dataset
    encoding = convert_encodings(char_set)
  File "/root/.local/share/virtualenvs/test-LINKoilU/lib/python3.6/site-packages/pydicom/charset.py", line 628, in convert_encodings
    _python_encoding_for_corrected_encoding(encoding))
  File "/root/.local/share/virtualenvs/test-LINKoilU/lib/python3.6/site-packages/pydicom/charset.py", line 647, in _python_encoding_for_corrected_encoding
    if re.match('^ISO[^_]IR', encoding) is not None:
  File "/root/.local/share/virtualenvs/test-LINKoilU/lib/python3.6/re.py", line 172, in match
    return _compile(pattern, flags).match(string)
TypeError: expected string or bytes-like object
```

#### Description
 I dont know why pydicom cant open my pictures, but other python library can read the picture and read some meta data correctly. I suspect " if re.match('^ISO[^_]IR', encoding) is not None:"  the encoding here is not string for my dicom picture.   I am new to pydicom,
Has anyone encountered a similar problem? how to solve it?  need help,thanks!

here is some dicom tags:
![image](https://user-images.githubusercontent.com/32253100/61868213-8016f500-af0b-11e9-8736-8703230229cf.png)
![image](https://user-images.githubusercontent.com/32253100/61868247-91600180-af0b-11e9-8767-a4045e901b8f.png)
![image](https://user-images.githubusercontent.com/32253100/61868284-a50b6800-af0b-11e9-88fd-10180e0acf56.png)



#### Steps/Code to Reproduce
```py

import pydicom
import os
import numpy
child_path = "DX.X.1.2.276.0.7230010.3.1.4.313262848.25.**********.444385.dcm"
pydicom.dcmread("DX.X.1.2.276.0.7230010.3.1.4.313262848.25.**********.444385.dcm"）

```

#### Expected Results
Example: read the file without error

#### Actual Results
cant read the file

#### Versions
v1.3.0

python v3.6

<!-- Thanks for contributing! -->

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_dataelem.py b/pydicom/tests/test_dataelem.py
--- a/pydicom/tests/test_dataelem.py
+++ b/pydicom/tests/test_dataelem.py
@@ -1,3 +1,4 @@
+# -*- coding: utf-8 -*-
 # Copyright 2008-2018 pydicom authors. See LICENSE file for details.
 \"\"\"Unit tests for the pydicom.dataelem module.\"\"\"

@@ -351,6 +352,51 @@ def test_private_repeater_tag(self):
         assert '[Overlay ID]' == private_data_elem.name
         assert 'UN' == private_data_elem.VR

+    def test_known_tags_with_UN_VR(self):
+        \"\"\"Known tags with VR UN are correctly decoded.\"\"\"
+        ds = Dataset()
+        ds[0x00080005] = DataElement(0x00080005, 'UN', b'ISO_IR 126')
+        ds[0x00100010] = DataElement(0x00100010, 'UN',
+                                     u'Διονυσιος'.encode('iso_ir_126'))
+        ds.decode()
+        assert 'CS' == ds[0x00080005].VR
+        assert 'PN' == ds[0x00100010].VR
+        assert u'Διονυσιος' == ds[0x00100010].value
+
+        ds = Dataset()
+        ds[0x00080005] = DataElement(0x00080005, 'UN',
+                                     b'ISO 2022 IR 100\\ISO 2022 IR 126')
+        ds[0x00100010] = DataElement(0x00100010, 'UN',
+                                     b'Dionysios=\x1b\x2d\x46'
+                                     + u'Διονυσιος'.encode('iso_ir_126'))
+        ds.decode()
+        assert 'CS' == ds[0x00080005].VR
+        assert 'PN' == ds[0x00100010].VR
+        assert u'Dionysios=Διονυσιος' == ds[0x00100010].value
+
+    def test_unknown_tags_with_UN_VR(self):
+        \"\"\"Unknown tags with VR UN are not decoded.\"\"\"
+        ds = Dataset()
+        ds[0x00080005] = DataElement(0x00080005, 'CS', b'ISO_IR 126')
+        ds[0x00111010] = DataElement(0x00111010, 'UN',
+                                     u'Διονυσιος'.encode('iso_ir_126'))
+        ds.decode()
+        assert 'UN' == ds[0x00111010].VR
+        assert u'Διονυσιος'.encode('iso_ir_126') == ds[0x00111010].value
+
+    def test_tag_with_long_value_UN_VR(self):
+        \"\"\"Tag with length > 64kb with VR UN is not changed.\"\"\"
+        ds = Dataset()
+        ds[0x00080005] = DataElement(0x00080005, 'CS', b'ISO_IR 126')
+
+        single_value = b'123456.789012345'
+        large_value = b'\\'.join([single_value] * 4500)
+        ds[0x30040058] = DataElement(0x30040058, 'UN',
+                                     large_value,
+                                     is_undefined_length=False)
+        ds.decode()
+        assert 'UN' == ds[0x30040058].VR
+
     def test_empty_text_values(self):
         \"\"\"Test that assigning an empty value behaves as expected.\"\"\"
         def check_empty_text_element(value):
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_900(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-900',
        'repo': 'pydicom/pydicom',
        'base_commit': '3746878d8edf1cbda6fbcf35eec69f9ba79301ca',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_dataelem.py::TestDataElement::test_known_tags_with_UN_VR"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
