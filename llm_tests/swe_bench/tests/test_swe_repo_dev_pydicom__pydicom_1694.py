# == SWE data pydicom__pydicom_1694 problem statement:
# == Git info: pydicom/pydicom, f8cf45b6c121e5a4bf4a43f71aba3bc64af3db9c

import pytest

problem_statement = r"""
Dataset.to_json_dict can still generate exceptions when suppress_invalid_tags=True
**Describe the bug**
I'm using `Dataset.to_json_dict(suppress_invalid_tags=True)` and can live with losing invalid tags.  Unfortunately, I can still trigger an exception with something like  `2.0` in an `IS` field.

**Expected behavior**
to_json_dict shouldn't throw an error about an invalid tag when `suppress_invalid_tags` is enabled.

My thought was simply to move the `data_element = self[key]` into the try/catch block that's right after it.

**Steps To Reproduce**

Traceback:
```
  File "dicom.py", line 143, in create_dict
    json_ds = ds.to_json_dict(suppress_invalid_tags=True)
  File "/usr/lib/python3/dist-packages/pydicom/dataset.py", line 2495, in to_json_dict
    data_element = self[key]
  File "/usr/lib/python3/dist-packages/pydicom/dataset.py", line 939, in __getitem__
    self[tag] = DataElement_from_raw(elem, character_set, self)
  File "/usr/lib/python3/dist-packages/pydicom/dataelem.py", line 859, in DataElement_from_raw
    value = convert_value(vr, raw, encoding)
  File "/usr/lib/python3/dist-packages/pydicom/values.py", line 771, in convert_value
    return converter(byte_string, is_little_endian, num_format)
  File "/usr/lib/python3/dist-packages/pydicom/values.py", line 348, in convert_IS_string
    return MultiString(num_string, valtype=pydicom.valuerep.IS)
  File "/usr/lib/python3/dist-packages/pydicom/valuerep.py", line 1213, in MultiString
    return valtype(splitup[0])
  File "/usr/lib/python3/dist-packages/pydicom/valuerep.py", line 1131, in __new__
    raise TypeError("Could not convert value to integer without loss")
TypeError: Could not convert value to integer without loss
```

**Your environment**
python 3.7, pydicom 2.3


""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_json.py b/pydicom/tests/test_json.py
--- a/pydicom/tests/test_json.py
+++ b/pydicom/tests/test_json.py
@@ -7,7 +7,7 @@

 from pydicom import dcmread
 from pydicom.data import get_testdata_file
-from pydicom.dataelem import DataElement
+from pydicom.dataelem import DataElement, RawDataElement
 from pydicom.dataset import Dataset
 from pydicom.tag import Tag, BaseTag
 from pydicom.valuerep import PersonName
@@ -284,7 +284,23 @@ def test_suppress_invalid_tags(self, _):

         ds_json = ds.to_json_dict(suppress_invalid_tags=True)

-        assert ds_json.get("00100010") is None
+        assert "00100010" not in ds_json
+
+    def test_suppress_invalid_tags_with_failed_dataelement(self):
+        \"\"\"Test tags that raise exceptions don't if suppress_invalid_tags True.
+        \"\"\"
+        ds = Dataset()
+        # we have to add a RawDataElement as creating a DataElement would
+        # already raise an exception
+        ds[0x00082128] = RawDataElement(
+            Tag(0x00082128), 'IS', 4, b'5.25', 0, True, True)
+
+        with pytest.raises(TypeError):
+            ds.to_json_dict()
+
+        ds_json = ds.to_json_dict(suppress_invalid_tags=True)
+
+        assert "00082128" not in ds_json


 class TestSequence:
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1694(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1694',
        'repo': 'pydicom/pydicom',
        'base_commit': 'f8cf45b6c121e5a4bf4a43f71aba3bc64af3db9c',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_json.py::TestDataSetToJson::test_suppress_invalid_tags_with_failed_dataelement"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
