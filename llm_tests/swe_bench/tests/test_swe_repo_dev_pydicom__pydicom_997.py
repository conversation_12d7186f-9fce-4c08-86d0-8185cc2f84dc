# == SWE data pydicom__pydicom_997 problem statement:
# == Git info: pydicom/pydicom, 41e984c8df5533805ae13cbcf419e6c5f63da30c

import pytest

problem_statement = r"""
Generators in encaps don't handle single fragment per frame correctly with no BOT value
#### Description
Generators in `encaps.py` handling of encapsulated pixel data incorrect when the Basic Offset Table has no value and each frame is a single fragment.

#### Steps/Code to Reproduce
```python
from pydicom import dcmread
from pydicom.encaps import generate_pixel_data_frame

fpath = 'pydicom/data/test_files/emri_small_jpeg_2k_lossless.dcm'
ds = dcmread(fpath)
ds.NumberOfFrames  # 10
frame_generator = generate_pixel_data_frame(ds.PixelData)
next(frame_generator)
next(frame_generator) # StopIteration raised
```

#### Expected Results
All 10 frames of the pixel data should be accessible.

#### Actual Results
Only the first frame is accessible.
[MRG] Some pixel handlers will not decode multiple fragments per frame


Added test cases to demonstrate failures for jpeg ls with multiple fragments per frame.  The test files were created with dcmtk 3.6.1 using dcmcjpls +fs 1. One file has an offset table, the other does not.

#### Reference Issue
See #685


#### What does this implement/fix? Explain your changes.
These test cases show that the pixel decoders (jpeg and jpeg_ls most likely) will not handle multiple fragments per frame.

No fix yet...

Any suggestions?
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_encaps.py b/pydicom/tests/test_encaps.py
--- a/pydicom/tests/test_encaps.py
+++ b/pydicom/tests/test_encaps.py
@@ -4,10 +4,11 @@
 import pytest

 from pydicom import dcmread
-from pydicom.data import get_testdata_files
+from pydicom.data import get_testdata_file
 from pydicom.encaps import (
     generate_pixel_data_fragment,
     get_frame_offsets,
+    get_nr_fragments,
     generate_pixel_data_frame,
     generate_pixel_data,
     decode_data_sequence,
@@ -20,7 +21,7 @@
 from pydicom.filebase import DicomBytesIO


-JP2K_10FRAME_NOBOT = get_testdata_files('emri_small_jpeg_2k_lossless.dcm')[0]
+JP2K_10FRAME_NOBOT = get_testdata_file('emri_small_jpeg_2k_lossless.dcm')


 class TestGetFrameOffsets(object):
@@ -57,7 +58,7 @@ def test_zero_length(self):
                      b'\x00\x00\x00\x00'
         fp = DicomBytesIO(bytestream)
         fp.is_little_endian = True
-        assert [0] == get_frame_offsets(fp)
+        assert (False, [0]) == get_frame_offsets(fp)

     def test_multi_frame(self):
         \"\"\"Test reading multi-frame BOT item\"\"\"
@@ -69,7 +70,7 @@ def test_multi_frame(self):
                      b'\xFE\x37\x00\x00'
         fp = DicomBytesIO(bytestream)
         fp.is_little_endian = True
-        assert [0, 4966, 9716, 14334] == get_frame_offsets(fp)
+        assert (True, [0, 4966, 9716, 14334]) == get_frame_offsets(fp)

     def test_single_frame(self):
         \"\"\"Test reading single-frame BOT item\"\"\"
@@ -78,7 +79,7 @@ def test_single_frame(self):
                      b'\x00\x00\x00\x00'
         fp = DicomBytesIO(bytestream)
         fp.is_little_endian = True
-        assert [0] == get_frame_offsets(fp)
+        assert (True, [0]) == get_frame_offsets(fp)

     def test_not_little_endian(self):
         \"\"\"Test reading big endian raises exception\"\"\"
@@ -91,6 +92,113 @@ def test_not_little_endian(self):
             get_frame_offsets(fp)


+class TestGetNrFragments(object):
+    \"\"\"Test encaps.get_nr_fragments\"\"\"
+    def test_item_undefined_length(self):
+        \"\"\"Test exception raised if item length undefined.\"\"\"
+        bytestream = (
+            b'\xFE\xFF\x00\xE0'
+            b'\xFF\xFF\xFF\xFF'
+            b'\x00\x00\x00\x01'
+        )
+        fp = DicomBytesIO(bytestream)
+        fp.is_little_endian = True
+        with pytest.raises(ValueError):
+            get_nr_fragments(fp)
+
+    def test_item_sequence_delimiter(self):
+        \"\"\"Test that the fragments are returned if seq delimiter hit.\"\"\"
+        bytestream = (
+            b'\xFE\xFF\x00\xE0'
+            b'\x04\x00\x00\x00'
+            b'\x01\x00\x00\x00'
+            b'\xFE\xFF\xDD\xE0'
+            b'\x00\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0'
+            b'\x04\x00\x00\x00'
+            b'\x02\x00\x00\x00'
+        )
+        fp = DicomBytesIO(bytestream)
+        fp.is_little_endian = True
+        assert 1 == get_nr_fragments(fp)
+
+    def test_item_bad_tag(self):
+        \"\"\"Test exception raised if item has unexpected tag\"\"\"
+        bytestream = (
+            b'\xFE\xFF\x00\xE0'
+            b'\x04\x00\x00\x00'
+            b'\x01\x00\x00\x00'
+            b'\x10\x00\x10\x00'
+            b'\x00\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0'
+            b'\x04\x00\x00\x00'
+            b'\x02\x00\x00\x00'
+        )
+        fp = DicomBytesIO(bytestream)
+        fp.is_little_endian = True
+        msg = (
+            r"Unexpected tag '\(0010, 0010\)' at offset 12 when parsing the "
+            r"encapsulated pixel data fragment items."
+        )
+        with pytest.raises(ValueError, match=msg):
+            get_nr_fragments(fp)
+
+    def test_single_fragment_no_delimiter(self):
+        \"\"\"Test single fragment is returned OK\"\"\"
+        bytestream = b'\xFE\xFF\x00\xE0' \
+                     b'\x04\x00\x00\x00' \
+                     b'\x01\x00\x00\x00'
+        fp = DicomBytesIO(bytestream)
+        fp.is_little_endian = True
+        assert 1 == get_nr_fragments(fp)
+
+    def test_multi_fragments_no_delimiter(self):
+        \"\"\"Test multi fragments are returned OK\"\"\"
+        bytestream = b'\xFE\xFF\x00\xE0' \
+                     b'\x04\x00\x00\x00' \
+                     b'\x01\x00\x00\x00' \
+                     b'\xFE\xFF\x00\xE0' \
+                     b'\x06\x00\x00\x00' \
+                     b'\x01\x02\x03\x04\x05\x06'
+        fp = DicomBytesIO(bytestream)
+        fp.is_little_endian = True
+        assert 2 == get_nr_fragments(fp)
+
+    def test_single_fragment_delimiter(self):
+        \"\"\"Test single fragment is returned OK with sequence delimiter item\"\"\"
+        bytestream = b'\xFE\xFF\x00\xE0' \
+                     b'\x04\x00\x00\x00' \
+                     b'\x01\x00\x00\x00' \
+                     b'\xFE\xFF\xDD\xE0'
+        fp = DicomBytesIO(bytestream)
+        fp.is_little_endian = True
+        assert 1 == get_nr_fragments(fp)
+
+    def test_multi_fragments_delimiter(self):
+        \"\"\"Test multi fragments are returned OK with sequence delimiter item\"\"\"
+        bytestream = b'\xFE\xFF\x00\xE0' \
+                     b'\x04\x00\x00\x00' \
+                     b'\x01\x00\x00\x00' \
+                     b'\xFE\xFF\x00\xE0' \
+                     b'\x06\x00\x00\x00' \
+                     b'\x01\x02\x03\x04\x05\x06' \
+                     b'\xFE\xFF\xDD\xE0'
+        fp = DicomBytesIO(bytestream)
+        fp.is_little_endian = True
+        assert 2 == get_nr_fragments(fp)
+
+    def test_not_little_endian(self):
+        \"\"\"Test reading big endian raises exception\"\"\"
+        bytestream = b'\xFE\xFF\x00\xE0' \
+                     b'\x04\x00\x00\x00' \
+                     b'\x01\x00\x00\x00'
+        fp = DicomBytesIO(bytestream)
+        fp.is_little_endian = False
+        with pytest.raises(ValueError,
+                           match="'fp.is_little_endian' must be True"):
+            get_nr_fragments(fp)
+
+
 class TestGeneratePixelDataFragment(object):
     \"\"\"Test encaps.generate_pixel_data_fragment\"\"\"
     def test_item_undefined_length(self):
@@ -242,7 +350,7 @@ def test_empty_bot_triple_fragment_single_frame(self):
                      b'\xFE\xFF\x00\xE0' \
                      b'\x04\x00\x00\x00' \
                      b'\x03\x00\x00\x00'
-        frames = generate_pixel_data_frame(bytestream)
+        frames = generate_pixel_data_frame(bytestream, 1)
         assert next(frames) == (
             b'\x01\x00\x00\x00\x02\x00\x00\x00\x03\x00\x00\x00'
         )
@@ -362,6 +470,18 @@ def test_multi_frame_varied_ratio(self):
         assert next(frames) == b'\x03\x00\x00\x00\x02\x04'
         pytest.raises(StopIteration, next, frames)

+    def test_empty_bot_multi_fragments_per_frame(self):
+        \"\"\"Test multi-frame where multiple frags per frame and no BOT.\"\"\"
+        # Regression test for #685
+        ds = dcmread(JP2K_10FRAME_NOBOT)
+        assert 10 == ds.NumberOfFrames
+        frame_gen = generate_pixel_data_frame(ds.PixelData, ds.NumberOfFrames)
+        for ii in range(10):
+            next(frame_gen)
+
+        with pytest.raises(StopIteration):
+            next(frame_gen)
+

 class TestGeneratePixelData(object):
     \"\"\"Test encaps.generate_pixel_data\"\"\"
@@ -391,12 +511,122 @@ def test_empty_bot_triple_fragment_single_frame(self):
                      b'\xFE\xFF\x00\xE0' \
                      b'\x04\x00\x00\x00' \
                      b'\x03\x00\x00\x00'
-        frames = generate_pixel_data(bytestream)
+        frames = generate_pixel_data(bytestream, 1)
         assert next(frames) == (b'\x01\x00\x00\x00',
                                 b'\x02\x00\x00\x00',
                                 b'\x03\x00\x00\x00')
         pytest.raises(StopIteration, next, frames)

+    def test_empty_bot_no_nr_frames_raises(self):
+        \"\"\"Test parsing raises if not BOT and no nr_frames.\"\"\"
+        # 1 frame, 3 fragments long
+        bytestream = b'\xFE\xFF\x00\xE0' \
+                     b'\x00\x00\x00\x00' \
+                     b'\xFE\xFF\x00\xE0' \
+                     b'\x04\x00\x00\x00' \
+                     b'\x01\x00\x00\x00' \
+                     b'\xFE\xFF\x00\xE0' \
+                     b'\x04\x00\x00\x00' \
+                     b'\x02\x00\x00\x00' \
+                     b'\xFE\xFF\x00\xE0' \
+                     b'\x04\x00\x00\x00' \
+                     b'\x03\x00\x00\x00'
+        msg = (
+            r"Unable to determine the frame boundaries for the "
+            r"encapsulated pixel data as the Basic Offset Table is empty "
+            r"and `nr_frames` parameter is None"
+        )
+        with pytest.raises(ValueError, match=msg):
+            next(generate_pixel_data(bytestream))
+
+    def test_empty_bot_too_few_fragments(self):
+        \"\"\"Test parsing with too few fragments.\"\"\"
+        ds = dcmread(JP2K_10FRAME_NOBOT)
+        assert 10 == ds.NumberOfFrames
+
+        msg = (
+            r"Unable to parse encapsulated pixel data as the Basic "
+            r"Offset Table is empty and there are fewer fragments then "
+            r"frames; the dataset may be corrupt"
+        )
+        with pytest.raises(ValueError, match=msg):
+            next(generate_pixel_data_frame(ds.PixelData, 20))
+
+    def test_empty_bot_multi_fragments_per_frame(self):
+        \"\"\"Test parsing with multiple fragments per frame.\"\"\"
+        # 4 frames in 6 fragments with JPEG EOI marker
+        bytestream = (
+            b'\xFE\xFF\x00\xE0\x00\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\xD9\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\x00\xFF\xD9'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\xD9\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\xD9\x00'
+        )
+
+        frames = generate_pixel_data(bytestream, 4)
+        for ii in range(4):
+            next(frames)
+
+        with pytest.raises(StopIteration):
+            next(frames)
+
+    def test_empty_bot_no_marker(self):
+        \"\"\"Test parsing not BOT and no final marker with multi fragments.\"\"\"
+        # 4 frames in 6 fragments with JPEG EOI marker (1 missing EOI)
+        bytestream = (
+            b'\xFE\xFF\x00\xE0\x00\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\xD9\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\xD9\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\xFF\xD9'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\x00\x00'
+        )
+
+        frames = generate_pixel_data(bytestream, 4)
+        for ii in range(3):
+            next(frames)
+
+        msg = (
+            r"The end of the encapsulated pixel data has been "
+            r"reached but one or more frame boundaries may have "
+            r"been missed; please confirm that the generated frame "
+            r"data is correct"
+        )
+        with pytest.warns(UserWarning, match=msg):
+            next(frames)
+
+        with pytest.raises(StopIteration):
+            next(frames)
+
+    def test_empty_bot_missing_marker(self):
+        \"\"\"Test parsing not BOT and missing marker with multi fragments.\"\"\"
+        # 4 frames in 6 fragments with JPEG EOI marker (1 missing EOI)
+        bytestream = (
+            b'\xFE\xFF\x00\xE0\x00\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\xD9\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\x00\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\x00\x00'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\xFF\xD9'
+            b'\xFE\xFF\x00\xE0\x04\x00\x00\x00\x01\xFF\xD9\x00'
+        )
+
+        msg = (
+            r"The end of the encapsulated pixel data has been "
+            r"reached but one or more frame boundaries may have "
+            r"been missed; please confirm that the generated frame "
+            r"data is correct"
+        )
+        with pytest.warns(UserWarning, match=msg):
+            ii = 0
+            for frames in generate_pixel_data(bytestream, 4):
+                ii += 1
+
+        assert 3 == ii
+
     def test_bot_single_fragment(self):
         \"\"\"Test a single-frame image where the frame is one fragment\"\"\"
         # 1 frame, 1 fragment long
@@ -932,7 +1162,7 @@ def test_encapsulate_single_fragment_per_frame_bot(self):

         fp = DicomBytesIO(data)
         fp.is_little_endian = True
-        offsets = get_frame_offsets(fp)
+        length, offsets = get_frame_offsets(fp)
         assert offsets == [
             0x0000,  # 0
             0x0eee,  # 3822
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_997(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-997',
        'repo': 'pydicom/pydicom',
        'base_commit': '41e984c8df5533805ae13cbcf419e6c5f63da30c',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_bad_tag", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_bad_length_multiple", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_zero_length", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_multi_frame", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_single_frame", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_not_little_endian", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_item_undefined_length", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_item_sequence_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_item_bad_tag", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_single_fragment_no_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_multi_fragments_no_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_single_fragment_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_multi_fragments_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_not_little_endian", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_item_undefined_length", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_item_sequence_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_item_bad_tag", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_single_fragment_no_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_multi_fragments_no_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_single_fragment_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_multi_fragments_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_not_little_endian", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_empty_bot_single_fragment", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_empty_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_bot_single_fragment", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_multi_frame_one_to_one", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_multi_frame_three_to_one", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_multi_frame_varied_ratio", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_empty_bot_multi_fragments_per_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_single_fragment", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_no_nr_frames_raises", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_too_few_fragments", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_multi_fragments_per_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_no_marker", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_missing_marker", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_bot_single_fragment", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_multi_frame_one_to_one", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_multi_frame_three_to_one", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_multi_frame_varied_ratio", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_empty_bot_single_fragment", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_empty_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_bot_single_fragment", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_multi_frame_one_to_one", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_multi_frame_three_to_one", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_multi_frame_varied_ratio", "pydicom/tests/test_encaps.py::TestDefragmentData::test_defragment", "pydicom/tests/test_encaps.py::TestReadItem::test_item_undefined_length", "pydicom/tests/test_encaps.py::TestReadItem::test_item_sequence_delimiter", "pydicom/tests/test_encaps.py::TestReadItem::test_item_sequence_delimiter_zero_length", "pydicom/tests/test_encaps.py::TestReadItem::test_item_bad_tag", "pydicom/tests/test_encaps.py::TestReadItem::test_single_fragment_no_delimiter", "pydicom/tests/test_encaps.py::TestReadItem::test_multi_fragments_no_delimiter", "pydicom/tests/test_encaps.py::TestReadItem::test_single_fragment_delimiter", "pydicom/tests/test_encaps.py::TestReadItem::test_multi_fragments_delimiter", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_single_fragment_even_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_single_fragment_odd_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_even_fragment_even_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_even_fragment_odd_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_odd_fragments_even_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_odd_fragments_odd_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_too_many_fragments_raises", "pydicom/tests/test_encaps.py::TestEncapsulateFrame::test_single_item", "pydicom/tests/test_encaps.py::TestEncapsulateFrame::test_two_items", "pydicom/tests/test_encaps.py::TestEncapsulate::test_encapsulate_single_fragment_per_frame_no_bot", "pydicom/tests/test_encaps.py::TestEncapsulate::test_encapsulate_single_fragment_per_frame_bot", "pydicom/tests/test_encaps.py::TestEncapsulate::test_encapsulate_bot"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
