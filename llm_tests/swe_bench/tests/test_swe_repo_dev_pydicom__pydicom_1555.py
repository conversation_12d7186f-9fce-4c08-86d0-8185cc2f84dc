# == SWE data pydicom__pydicom_1555 problem statement:
# == Git info: pydicom/pydicom, 9db89e1d8f5e82dc617f9c8cbf303fe23a0632b9

import pytest

problem_statement = r"""
Converting Dicom image to Png
**Describe the issue**
hi, i am trying to convert Dicom image to png but in case of some particular file i am getting this "list out of range error".

**Expected behavior**
dicom image converted to png pne

**Steps To Reproduce**
How to reproduce the issue. Please include:
1. A minimum working code sample
```
from pydicom import dcmread
def read_xray(path, voi_lut = True, fix_monochrome = True):
    dicom = dcmread(path, force=True)

    # VOI LUT (if available by DICOM device) is used to transform raw DICOM data to "human-friendly" view
    if voi_lut:
        data = apply_voi_lut(dicom.pixel_array, dicom)
    else:
        data = dicom.pixel_array

    # depending on this value, X-ray may look inverted - fix that:
    if fix_monochrome and dicom.PhotometricInterpretation == "MONOCHROME1":
        data = np.amax(data) - data

    data = data - np.min(data)
    data = data / np.max(data)
    data = (data * 255).astype(np.uint8)

    return data

img = read_xray('/content/a.5545da1153f57ff8425be6f4bc712c090e7e22efff194da525210c84aba2a947.dcm')
plt.figure(figsize = (12,12))
plt.imshow(img)
```
2. The traceback (if one occurred)
```
IndexError                                Traceback (most recent call last)
<ipython-input-13-6e53d7d16b90> in <module>()
     19     return data
     20
---> 21 img = read_xray('/content/a.5545da1153f57ff8425be6f4bc712c090e7e22efff194da525210c84aba2a947.dcm')
     22 plt.figure(figsize = (12,12))
     23 plt.imshow(img)

2 frames
/usr/local/lib/python3.7/dist-packages/pydicom/multival.py in __getitem__(self, index)
     93         self, index: Union[slice, int]
     94     ) -> Union[MutableSequence[_ItemType], _ItemType]:
---> 95         return self._list[index]
     96
     97     def insert(self, position: int, val: _T) -> None:

IndexError: list index out of range
```

3. Which of the following packages are available and their versions:
  * Numpy : latest as of 29th dec
  * Pillow : latest as of 29th dec
  * JPEG-LS : latest as of 29th dec
  * GDCM : latest as of 29th dec
4. The anonymized DICOM dataset (if possible).
image link : https://drive.google.com/file/d/1j13XTTPCLX-8e7FE--1n5Staxz7GGNWm/view?usp=sharing

**Your environment**
If you're using **pydicom 2 or later**, please use the `pydicom.env_info`
module to gather information about your environment and paste it in the issue:

```bash
$ python -m pydicom.env_info
```

For **pydicom 1.x**, please run the following code snippet and paste the
output.

```python
import platform, sys, pydicom
print(platform.platform(),
      "\nPython", sys.version,
      "\npydicom", pydicom.__version__)
```

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_handler_util.py b/pydicom/tests/test_handler_util.py
--- a/pydicom/tests/test_handler_util.py
+++ b/pydicom/tests/test_handler_util.py
@@ -890,6 +890,10 @@ def test_unchanged(self):
         out = apply_modality_lut(arr, ds)
         assert arr is out

+        ds.ModalityLUTSequence = []
+        out = apply_modality_lut(arr, ds)
+        assert arr is out
+
     def test_lutdata_ow(self):
         \"\"\"Test LUT Data with VR OW.\"\"\"
         ds = dcmread(MOD_16_SEQ)
@@ -1839,6 +1843,10 @@ def test_unchanged(self):
         out = apply_windowing(arr, ds)
         assert [-128, -127, -1, 0, 1, 126, 127] == out.tolist()

+        ds.ModalityLUTSequence = []
+        out = apply_windowing(arr, ds)
+        assert [-128, -127, -1, 0, 1, 126, 127] == out.tolist()
+
     def test_rescale_empty(self):
         \"\"\"Test RescaleSlope and RescaleIntercept being empty.\"\"\"
         ds = dcmread(WIN_12_1F)
@@ -2051,6 +2059,11 @@ def test_unchanged(self):
         out = apply_voi(arr, ds)
         assert [-128, -127, -1, 0, 1, 126, 127] == out.tolist()

+        ds.VOILUTSequence = []
+        out = apply_voi(arr, ds)
+        assert [-128, -127, -1, 0, 1, 126, 127] == out.tolist()
+
+
     def test_voi_lutdata_ow(self):
         \"\"\"Test LUT Data with VR OW.\"\"\"
         ds = Dataset()
@@ -2072,6 +2085,7 @@ def test_voi_lutdata_ow(self):

 @pytest.mark.skipif(not HAVE_NP, reason="Numpy is not available")
 class TestNumpy_ApplyVOILUT:
+    \"\"\"Tests for util.apply_voi_lut()\"\"\"
     def test_unchanged(self):
         \"\"\"Test input array is unchanged if no VOI LUT\"\"\"
         ds = Dataset()
@@ -2082,6 +2096,10 @@ def test_unchanged(self):
         out = apply_voi_lut(arr, ds)
         assert [-128, -127, -1, 0, 1, 126, 127] == out.tolist()

+        ds.VOILUTSequence = []
+        out = apply_voi_lut(arr, ds)
+        assert [-128, -127, -1, 0, 1, 126, 127] == out.tolist()
+
     def test_only_windowing(self):
         \"\"\"Test only windowing operation elements present.\"\"\"
         ds = Dataset()
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1555(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1555',
        'repo': 'pydicom/pydicom',
        'base_commit': '9db89e1d8f5e82dc617f9c8cbf303fe23a0632b9',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_handler_util.py::TestNumpy_ModalityLUT::test_unchanged", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOI::test_unchanged", "pydicom/tests/test_handler_util.py::TestNumpy_ApplyVOILUT::test_unchanged"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
