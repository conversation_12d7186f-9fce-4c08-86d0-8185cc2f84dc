# == SWE data pydicom__pydicom_1458 problem statement:
# == Git info: pydicom/pydicom, 8da0b9b215ebfad5756051c891def88e426787e7

import pytest

problem_statement = r"""
Pixel Representation attribute should be optional for pixel data handler
**Describe the bug**
The NumPy pixel data handler currently [requires the Pixel Representation attribute](https://github.com/pydicom/pydicom/blob/8da0b9b215ebfad5756051c891def88e426787e7/pydicom/pixel_data_handlers/numpy_handler.py#L46). This is problematic, because in case of Float Pixel Data or Double Float Pixel Data the attribute shall be absent. Compare [Floating Point Image Pixel Module Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.24.html) versus [Image Pixel Description Macro Attributes](http://dicom.nema.org/medical/dicom/current/output/chtml/part03/sect_C.7.6.3.html#table_C.7-11c)

**Expected behavior**
I would expect the `Dataset.pixel_array` property to be able to decode a Float Pixel Data or Double Float Pixel Data element without presence of the Pixel Representation element in the metadata.

**Steps To Reproduce**
```python
import numpy as np
from pydicom.dataset import Dataset, FileMetaDataset


ds = Dataset()
ds.file_meta = FileMetaDataset()
ds.file_meta.TransferSyntaxUID = '1.2.840.10008.1.2.1'

ds.BitsAllocated = 32
ds.SamplesPerPixel = 1
ds.Rows = 5
ds.Columns = 5
ds.PhotometricInterpretation = 'MONOCHROME2'

pixel_array = np.zeros((ds.Rows, ds.Columns), dtype=np.float32)
ds.FloatPixelData = pixel_array.flatten().tobytes()

np.array_equal(ds.pixel_array, pixel_array)
```
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_numpy_pixel_data.py b/pydicom/tests/test_numpy_pixel_data.py
--- a/pydicom/tests/test_numpy_pixel_data.py
+++ b/pydicom/tests/test_numpy_pixel_data.py
@@ -26,6 +26,8 @@
 * PlanarConfiguration
 \"\"\"

+from copy import deepcopy
+
 import pytest

 from pydicom import config
@@ -1068,6 +1070,7 @@ def test_endianness_not_set(self):
         ds.Rows = 10
         ds.Columns = 10
         ds.BitsAllocated = 16
+        ds.BitsStored = 16
         ds.PixelRepresentation = 0
         ds.SamplesPerPixel = 1
         ds.PhotometricInterpretation = 'MONOCHROME2'
@@ -1105,16 +1108,60 @@ def test_no_pixel_data_raises(self):
         with pytest.raises(AttributeError, match=msg):
             get_pixeldata(ds)

-    def test_missing_required_elem(self):
+    def test_missing_required_elem_pixel_data_monochrome(self):
         \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"
-        ds = dcmread(EXPL_16_1_1F)
-        del ds.BitsAllocated
+        required_attrs = (
+            'BitsAllocated',
+            'BitsStored',
+            'Rows',
+            'Columns',
+            'SamplesPerPixel',
+            'PhotometricInterpretation',
+            'PixelRepresentation',
+        )
+        for attr in required_attrs:
+            ds = dcmread(EXPL_16_1_1F)
+            delattr(ds, attr)
+            msg = (
+                r"Unable to convert the pixel data as the following required "
+                r"elements are missing from the dataset: {}".format(attr)
+            )
+            with pytest.raises(AttributeError, match=msg):
+                get_pixeldata(ds)
+
+    def test_missing_required_elem_pixel_data_color(self):
+        \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"
+        ds = dcmread(EXPL_8_3_1F)
+        del ds.Rows
+        del ds.Columns
+        msg = (
+            r"Unable to convert the pixel data as the following required "
+            r"elements are missing from the dataset: Rows, Columns"
+        )
+        with pytest.raises(AttributeError, match=msg):
+            get_pixeldata(ds)
+
+    def test_missing_conditionally_required_elem_pixel_data_color(self):
+        \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"
+        ds = dcmread(EXPL_8_3_1F)
+        del ds.PlanarConfiguration
+        msg = (
+            r"Unable to convert the pixel data as the following conditionally "
+            r"required element is missing from the dataset: "
+            r"PlanarConfiguration"
+        )
+        with pytest.raises(AttributeError, match=msg):
+            get_pixeldata(ds)
+
+    def test_missing_required_elem_float_pixel_data_monochrome(self):
+        \"\"\"Tet get_pixeldata raises if dataset missing required element.\"\"\"
+        ds = dcmread(IMPL_32_1_1F)
+        ds.FloatPixelData = ds.PixelData
+        del ds.PixelData
         del ds.Rows
-        del ds.SamplesPerPixel
         msg = (
             r"Unable to convert the pixel data as the following required "
-            r"elements are missing from the dataset: BitsAllocated, Rows, "
-            r"SamplesPerPixel"
+            r"elements are missing from the dataset: Rows"
         )
         with pytest.raises(AttributeError, match=msg):
             get_pixeldata(ds)
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1458(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1458',
        'repo': 'pydicom/pydicom',
        'base_commit': '8da0b9b215ebfad5756051c891def88e426787e7',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_required_elem_pixel_data_monochrome", "pydicom/tests/test_numpy_pixel_data.py::TestNumpy_GetPixelData::test_missing_conditionally_required_elem_pixel_data_color"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
