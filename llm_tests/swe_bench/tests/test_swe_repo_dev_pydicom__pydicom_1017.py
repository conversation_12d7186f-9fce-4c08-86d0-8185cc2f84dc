# == SWE data pydicom__pydicom_1017 problem statement:
# == Git info: pydicom/pydicom, 7241f5d9db0de589b230bb84212fbb643a7c86c3

import pytest

problem_statement = r"""
Add support for missing VRs
Missing: OV, SV, UV


""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filereader.py b/pydicom/tests/test_filereader.py
--- a/pydicom/tests/test_filereader.py
+++ b/pydicom/tests/test_filereader.py
@@ -17,6 +17,7 @@
 from pydicom import config
 from pydicom.dataset import Dataset, FileDataset
 from pydicom.data import get_testdata_files
+from pydicom.datadict import add_dict_entries
 from pydicom.filereader import dcmread, read_dataset
 from pydicom.dataelem import DataElement, DataElement_from_raw
 from pydicom.errors import InvalidDicomError
@@ -837,6 +838,31 @@ def setup(self):
         ds.URNCodeValue = 'http://test.com'  # VR of UR
         ds.RetrieveURL = 'ftp://test.com  '  # Test trailing spaces ignored
         ds.DestinationAE = '    TEST  12    '  # 16 characters max for AE
+        # 8-byte values
+        ds.ExtendedOffsetTable = (  # VR of OV
+            b'\x00\x00\x00\x00\x00\x00\x00\x00'
+            b'\x01\x02\x03\x04\x05\x06\x07\x08'
+        )
+
+        # No public elements with VR of SV or UV yet...
+        add_dict_entries({
+            0xFFFE0001: (
+                'SV', '1', 'SV Element Minimum', '', 'SVElementMinimum'
+            ),
+            0xFFFE0002: (
+                'SV', '1', 'SV Element Maximum', '', 'SVElementMaximum'
+            ),
+            0xFFFE0003: (
+                'UV', '1', 'UV Element Minimum', '', 'UVElementMinimum'
+            ),
+            0xFFFE0004: (
+                'UV', '1', 'UV Element Maximum', '', 'UVElementMaximum'
+            ),
+        })
+        ds.SVElementMinimum = -2**63
+        ds.SVElementMaximum = 2**63 - 1
+        ds.UVElementMinimum = 0
+        ds.UVElementMaximum = 2**64 - 1

         self.fp = BytesIO()  # Implicit little
         file_ds = FileDataset(self.fp, ds)
@@ -939,6 +965,113 @@ def test_read_AE(self):
         ds = dcmread(self.fp, force=True)
         assert 'TEST  12' == ds.DestinationAE

+    def test_read_OV_implicit_little(self):
+        \"\"\"Check reading element with VR of OV encoded as implicit\"\"\"
+        ds = dcmread(self.fp, force=True)
+        val = (
+            b'\x00\x00\x00\x00\x00\x00\x00\x00'
+            b'\x01\x02\x03\x04\x05\x06\x07\x08'
+        )
+        elem = ds['ExtendedOffsetTable']
+        assert 'OV' == elem.VR
+        assert 0x7FE00001 == elem.tag
+        assert val == elem.value
+
+        new = DataElement(0x7FE00001, 'OV', val)
+        assert elem == new
+
+    def test_read_OV_explicit_little(self):
+        \"\"\"Check reading element with VR of OV encoded as explicit\"\"\"
+        ds = dcmread(self.fp_ex, force=True)
+        val = (
+            b'\x00\x00\x00\x00\x00\x00\x00\x00'
+            b'\x01\x02\x03\x04\x05\x06\x07\x08'
+        )
+        elem = ds['ExtendedOffsetTable']
+        assert 'OV' == elem.VR
+        assert 0x7FE00001 == elem.tag
+        assert val == elem.value
+
+        new = DataElement(0x7FE00001, 'OV', val)
+        assert elem == new
+
+    def test_read_SV_implicit_little(self):
+        \"\"\"Check reading element with VR of SV encoded as implicit\"\"\"
+        ds = dcmread(self.fp, force=True)
+        elem = ds['SVElementMinimum']
+        assert 'SV' == elem.VR
+        assert 0xFFFE0001 == elem.tag
+        assert -2**63 == elem.value
+
+        new = DataElement(0xFFFE0001, 'SV', -2**63)
+        assert elem == new
+
+        elem = ds['SVElementMaximum']
+        assert 'SV' == elem.VR
+        assert 0xFFFE0002 == elem.tag
+        assert 2**63 - 1 == elem.value
+
+        new = DataElement(0xFFFE0002, 'SV', 2**63 - 1)
+        assert elem == new
+
+    @pytest.mark.skip("No public elements with VR of SV")
+    def test_read_SV_explicit_little(self):
+        \"\"\"Check reading element with VR of SV encoded as explicit\"\"\"
+        ds = dcmread(self.fp_ex, force=True)
+        elem = ds['SVElementMinimum']
+        assert 'SV' == elem.VR
+        assert 0xFFFE0001 == elem.tag
+        assert -2**63 == elem.value
+
+        new = DataElement(0xFFFE0001, 'SV', -2**63)
+        assert elem == new
+
+        elem = ds['SVElementMaximum']
+        assert 'SV' == elem.VR
+        assert 0xFFFE0002 == elem.tag
+        assert 2**63 - 1 == elem.value
+
+        new = DataElement(0xFFFE0002, 'SV', 2**63 - 1)
+        assert elem == new
+
+    def test_read_UV_implicit_little(self):
+        \"\"\"Check reading element with VR of UV encoded as implicit\"\"\"
+        ds = dcmread(self.fp, force=True)
+        elem = ds['UVElementMinimum']
+        assert 'UV' == elem.VR
+        assert 0xFFFE0003 == elem.tag
+        assert 0 == elem.value
+
+        new = DataElement(0xFFFE0003, 'UV', 0)
+        assert elem == new
+
+        elem = ds['UVElementMaximum']
+        assert 'UV' == elem.VR
+        assert 0xFFFE0004 == elem.tag
+        assert 2**64 - 1 == elem.value
+
+        new = DataElement(0xFFFE0004, 'UV', 2**64 - 1)
+        assert elem == new
+
+    def test_read_UV_explicit_little(self):
+        \"\"\"Check reading element with VR of UV encoded as explicit\"\"\"
+        ds = dcmread(self.fp_ex, force=True)
+        elem = ds['UVElementMinimum']
+        assert 'UV' == elem.VR
+        assert 0xFFFE0003 == elem.tag
+        assert 0 == elem.value
+
+        new = DataElement(0xFFFE0003, 'UV', 0)
+        assert elem == new
+
+        elem = ds['UVElementMaximum']
+        assert 'UV' == elem.VR
+        assert 0xFFFE0004 == elem.tag
+        assert 2**64 - 1 == elem.value
+
+        new = DataElement(0xFFFE0004, 'UV', 2**64 - 1)
+        assert elem == new
+

 class TestDeferredRead(object):
     \"\"\"Test that deferred data element reading (for large size)
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1017(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1017',
        'repo': 'pydicom/pydicom',
        'base_commit': '7241f5d9db0de589b230bb84212fbb643a7c86c3',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OD_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OD_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OL_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OL_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UC_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UC_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UR_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UR_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_AE", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OV_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OV_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_SV_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UV_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UV_explicit_little"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
