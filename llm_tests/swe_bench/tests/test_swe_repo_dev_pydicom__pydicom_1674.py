# == SWE data pydicom__pydicom_1674 problem statement:
# == Git info: pydicom/pydicom, e77e6586fa38e6f7e98efca80d560d0fea8a9669

import pytest

problem_statement = r"""
Codify not generating content sequences correctly
**Describe the bug**
I am trying to generate a radiation dose structure report. I ran Codify on an existing RDSR to generate a template. The sequence content is reproduced but does not seem to be attached  to the base dataset. When I run the generated python file the dicom file it saves has no sequence content information.

**Expected behavior**
I expect the dicom file generated by the python code from Codify to be similar to the original file.

**Steps To Reproduce**
$ python codify X-RayRadiationDoseReport001_ESR.dcm rdsr.py
$ python rsdr.py

I am not able to attached the above files but can supply them.

**Your environment**
module       | version
------       | -------
platform     | Linux-5.18.7-200.fc36.x86_64-x86_64-with-glibc2.35
Python       | 3.10.5 (main, Jun  9 2022, 00:00:00) [GCC 12.1.1 20220507 (Red Hat 12.1.1-1)]
pydicom      | 2.3.0
gdcm         | _module not found_
jpeg_ls      | _module not found_
numpy        | 1.22.4
PIL          | 9.2.0
pylibjpeg    | _module not found_
openjpeg     | _module not found_
libjpeg      | _module not found_

Regards
Alan
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_cli.py b/pydicom/tests/test_cli.py
--- a/pydicom/tests/test_cli.py
+++ b/pydicom/tests/test_cli.py
@@ -1,3 +1,4 @@
+# -*- coding: utf-8 -*-
 # Copyright 2020 pydicom authors. See LICENSE file for details.
 \"\"\"Tests for command-line interface\"\"\"

@@ -138,6 +139,13 @@ def test_codify_data_element(self, capsys):
         with pytest.raises(NotImplementedError):
             main("codify pydicom::rtplan.dcm::RTPlanLabel".split())

+    def test_codify_UTF8(self, capsys):
+        \"\"\"CLI `codify` command creates code with utf-8 characters\"\"\"
+        main(f"codify pydicom::chrFren.dcm".split())
+        out, _ = capsys.readouterr()
+        assert out.startswith("# -*- coding: utf-8 -*-")
+        assert "Buc^Jérôme" in out
+
     def test_help(self, capsys):
         \"\"\"CLI `help` command gives expected output\"\"\"
         # With subcommand
diff --git a/pydicom/tests/test_util.py b/pydicom/tests/test_util.py
--- a/pydicom/tests/test_util.py
+++ b/pydicom/tests/test_util.py
@@ -5,12 +5,11 @@

 import pytest

-from pydicom import config, dcmread
+from pydicom import config, dcmread, Dataset, Sequence
 from pydicom import filereader
 from pydicom._private_dict import private_dictionaries
 from pydicom.data import get_testdata_file
 from pydicom.dataelem import DataElement
-from pydicom.dataset import Dataset
 from pydicom.tag import Tag
 from pydicom.uid import (
     ImplicitVRLittleEndian, ExplicitVRBigEndian, ExplicitVRLittleEndian
@@ -23,6 +22,7 @@
     default_name_filter,
     code_imports,
     code_dataelem,
+    code_dataset,
     main as codify_main,
 )
 from pydicom.util.dump import *
@@ -134,15 +134,34 @@ def test_code_sequence(self):
             "\n"
             "# Control Point Sequence: Control Point 1\n"
             "cp1 = Dataset()\n"
-            "cp1.PatientID = '1234'\n"
-            "cp_sequence.append(cp1)"
+            "cp_sequence.append(cp1)\n"
+            "cp1.PatientID = '1234'"
         )

         assert out == code_dataelem(elem)

-    def test_code_dataset(self):
-        \"\"\"Test utils.codify.code_dataset\"\"\"
-        pass
+    def test_codify_recurring_keyword(self):
+        \"\"\"Test utils.codify.code_dataset with same keyword nested\"\"\"
+        # Create fake Dataset with repeated DICOM keyword nested
+        # (0040, a730)  Content Sequence  1 item(s) ----
+        #    (0040, a040) Value Type                          CS: 'CODE'
+        #    (0040, a730)  Content Sequence  1 item(s) ----
+        #       (0040, a040) Value Type                          CS: 'CODE'
+
+        ds = Dataset()
+        ds.ContentSequence = seq1 = Sequence()
+        seq1.append(Dataset())
+        seq1[0].ValueType = "CODE"
+        seq1[0].ContentSequence = seq2 = Sequence()
+        seq2.append(Dataset())
+        seq2[0].ValueType = "CODE_1"
+        ds_code = code_dataset(ds)
+
+        # normal 1st use of var name
+        assert "content1.ValueType = 'CODE'" in ds_code
+
+        # Nested item of same name should have subscript
+        assert "content1_1.ValueType = 'CODE_1'" in ds_code

     def test_code_file(self, capsys):
         \"\"\"Test utils.codify.code_file\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1674(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1674',
        'repo': 'pydicom/pydicom',
        'base_commit': 'e77e6586fa38e6f7e98efca80d560d0fea8a9669',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_cli.py::TestCLIcall::test_codify_UTF8", "pydicom/tests/test_util.py::TestCodify::test_code_sequence", "pydicom/tests/test_util.py::TestCodify::test_codify_recurring_keyword"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
