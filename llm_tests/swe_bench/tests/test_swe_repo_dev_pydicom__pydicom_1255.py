# == SWE data pydicom__pydicom_1255 problem statement:
# == Git info: pydicom/pydicom, 59be4993000cc8af2cd9a4176f15398b6a7875d7

import pytest

problem_statement = r"""
Mypy errors
**Describe the bug**
Several of the type hints are problematic and result in mypy errors.

One example:

```none
cat << EOF > /tmp/test.py
from pydicom import Dataset, dcmread

dataset = Dataset()
dataset.Rows = 10
dataset.Columns = 20
dataset.NumberOfFrames = "5"

assert int(dataset.NumberOfFrames) == 5

filename = '/tmp/test.dcm'
dataset.save_as(str(filename))

dataset = dcmread(filename)

assert int(dataset.NumberOfFrames) == 5
EOF
```

```none
mypy /tmp/test.py
/tmp/test.py:15: error: No overload variant of "int" matches argument type "object"
/tmp/test.py:15: note: Possible overload variant:
/tmp/test.py:15: note:     def int(self, x: Union[str, bytes, SupportsInt, _SupportsIndex] = ...) -> int
/tmp/test.py:15: note:     <1 more non-matching overload not shown>
Found 1 error in 1 file (checked 1 source file)
```

**Expected behavior**
Mypy should not report any errors.

**Steps To Reproduce**
See above

**Your environment**
```none
python -m pydicom.env_info
module       | version
------       | -------
platform     | macOS-10.15.6-x86_64-i386-64bit
Python       | 3.8.6 (default, Oct  8 2020, 14:06:32)  [Clang 12.0.0 (clang-1200.0.32.2)]
pydicom      | 2.1.0
gdcm         | _module not found_
jpeg_ls      | _module not found_
numpy        | 1.19.3
PIL          | 8.0.1
```
ImportError: cannot import name 'NoReturn'
**Describe the bug**
throw following excetion when import pydicom package:
```
xxx/python3.6/site-packages/pydicom/filebase.py in <module>
5 from struct import unpack, pack
      6 from types import TracebackType
----> 7 from typing import (
      8     Tuple, Optional, NoReturn, BinaryIO, Callable, Type, Union, cast, TextIO,
      9     TYPE_CHECKING, Any

ImportError: cannot import name 'NoReturn'
```

**Expected behavior**
imort pydicom sucessfully

**Steps To Reproduce**
How to reproduce the issue. Please include a minimum working code sample, the
traceback (if any) and the anonymized DICOM dataset (if relevant).

**Your environment**
python:3.6.0
pydicom:2.1

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_json.py b/pydicom/tests/test_json.py
--- a/pydicom/tests/test_json.py
+++ b/pydicom/tests/test_json.py
@@ -354,3 +354,25 @@ def bulk_data_reader(tag, vr, value):
         ds = Dataset().from_json(json.dumps(json_data), bulk_data_reader)

         assert b'xyzzy' == ds[0x00091002].value
+
+    def test_bulk_data_reader_is_called_within_SQ(self):
+        def bulk_data_reader(_):
+            return b'xyzzy'
+
+        json_data = {
+            "003a0200": {
+                "vr": "SQ",
+                "Value": [
+                    {
+                        "54001010": {
+                            "vr": "OW",
+                            "BulkDataURI": "https://a.dummy.url"
+                        }
+                    }
+                ]
+            }
+        }
+
+        ds = Dataset().from_json(json.dumps(json_data), bulk_data_reader)
+
+        assert b'xyzzy' == ds[0x003a0200].value[0][0x54001010].value
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1255(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1255',
        'repo': 'pydicom/pydicom',
        'base_commit': '59be4993000cc8af2cd9a4176f15398b6a7875d7',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_json.py::TestBinary::test_bulk_data_reader_is_called_within_SQ"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
