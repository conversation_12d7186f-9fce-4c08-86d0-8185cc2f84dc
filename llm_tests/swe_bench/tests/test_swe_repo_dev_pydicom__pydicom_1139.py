# == SWE data pydicom__pydicom_1139 problem statement:
# == Git info: pydicom/pydicom, b9fb05c177b685bf683f7f57b2d57374eb7d882d

import pytest

problem_statement = r"""
Make PersonName3 iterable
```python
from pydicom import Dataset

ds = Dataset()
ds.PatientName = 'SomeName'

'S' in ds.PatientName
```
```
Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
TypeError: argument of type 'PersonName3' is not iterable
```

I'm not really sure if this is intentional or if PN elements should support `str` methods. And yes I know I can `str(ds.PatientName)` but it's a bit silly, especially when I keep having to write exceptions to my element iterators just for PN elements.
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_valuerep.py b/pydicom/tests/test_valuerep.py
--- a/pydicom/tests/test_valuerep.py
+++ b/pydicom/tests/test_valuerep.py
@@ -427,6 +427,62 @@ def test_hash(self):
         )
         assert hash(pn1) == hash(pn2)

+    def test_next(self):
+        \"\"\"Test that the next function works on it's own\"\"\"
+        # Test getting the first character
+        pn1 = PersonName("John^Doe^^Dr", encodings=default_encoding)
+        pn1_itr = iter(pn1)
+        assert next(pn1_itr) == "J"
+
+        # Test getting multiple characters
+        pn2 = PersonName(
+            "Yamada^Tarou=山田^太郎=やまだ^たろう", [default_encoding, "iso2022_jp"]
+        )
+        pn2_itr = iter(pn2)
+        assert next(pn2_itr) == "Y"
+        assert next(pn2_itr) == "a"
+
+        # Test getting all characters
+        pn3 = PersonName("SomeName")
+        pn3_itr = iter(pn3)
+        assert next(pn3_itr) == "S"
+        assert next(pn3_itr) == "o"
+        assert next(pn3_itr) == "m"
+        assert next(pn3_itr) == "e"
+        assert next(pn3_itr) == "N"
+        assert next(pn3_itr) == "a"
+        assert next(pn3_itr) == "m"
+        assert next(pn3_itr) == "e"
+
+        # Attempting to get next characeter should stop the iteration
+        # I.e. next can only start once
+        with pytest.raises(StopIteration):
+            next(pn3_itr)
+
+        # Test that next() doesn't work without instantiating an iterator
+        pn4 = PersonName("SomeName")
+        with pytest.raises(AttributeError):
+            next(pn4)
+
+    def test_iterator(self):
+        \"\"\"Test that iterators can be corretly constructed\"\"\"
+        name_str = "John^Doe^^Dr"
+        pn1 = PersonName(name_str)
+
+        for i, c in enumerate(pn1):
+            assert name_str[i] == c
+
+        # Ensure that multiple iterators can be created on the same variable
+        for i, c in enumerate(pn1):
+            assert name_str[i] == c
+
+    def test_contains(self):
+        \"\"\"Test that characters can be check if they are within the name\"\"\"
+        pn1 = PersonName("John^Doe")
+        assert ("J" in pn1) == True
+        assert ("o" in pn1) == True
+        assert ("x" in pn1) == False
+

 class TestDateTime:
     \"\"\"Unit tests for DA, DT, TM conversion to datetime objects\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1139(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1139',
        'repo': 'pydicom/pydicom',
        'base_commit': 'b9fb05c177b685bf683f7f57b2d57374eb7d882d',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_valuerep.py::TestPersonName::test_next", "pydicom/tests/test_valuerep.py::TestPersonName::test_iterator", "pydicom/tests/test_valuerep.py::TestPersonName::test_contains"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
