# == SWE data pydicom__pydicom_1194 problem statement:
# == Git info: pydicom/pydicom, 5e70c1dfe09820023fec519dac4c51bebcb7f60d

import pytest

problem_statement = r"""
Error decoding dataset with ambiguous VR element when the value is None
Hi all,
    I used the storescu in pynetdicom 1.5.3 to send the dicom ct files(both on mac and ubuntu):
**python storescu.py 192.168.1.120 9002 ~/Downloads/test/**
(I also tried https://pydicom.github.io/pynetdicom/stable/examples/storage.html#storage-scu)
but it throwed errors:

_E: Failed to encode the supplied Dataset
E: Store failed: /Users/<USER>/Downloads/test/CT_S1_118.dcm
E: Failed to encode the supplied Dataset
Traceback (most recent call last):
  File "storescu.py", line 283, in main
    status = assoc.send_c_store(ds, ii)
  File "/Users/<USER>/.pyenv/versions/3.8.2/lib/python3.8/site-packages/pynetdicom/association.py", line 1736, in send_c_store
    raise ValueError('Failed to encode the supplied Dataset')
ValueError: Failed to encode the supplied Dataset_

But I used to send same files with storescu in dcm4che successfully.
File attached.

[test.zip](https://github.com/pydicom/pynetdicom/files/5258867/test.zip)

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filewriter.py b/pydicom/tests/test_filewriter.py
--- a/pydicom/tests/test_filewriter.py
+++ b/pydicom/tests/test_filewriter.py
@@ -1054,7 +1054,7 @@ def test_not_ambiguous_raw_data_element(self):
         elem = RawDataElement(0x60003000, 'OB', 1, b'\x00', 0, True, True)
         out = correct_ambiguous_vr_element(elem, Dataset(), True)
         assert out == elem
-        assert type(out) == RawDataElement
+        assert isinstance(out, RawDataElement)

     def test_correct_ambiguous_data_element(self):
         \"\"\"Test correct ambiguous US/SS element\"\"\"
@@ -1086,10 +1086,28 @@ def test_correct_ambiguous_raw_data_element(self):
         ds[0x00280120] = elem
         ds.PixelRepresentation = 0
         out = correct_ambiguous_vr_element(elem, ds, True)
-        assert type(out) == DataElement
+        assert isinstance(out, DataElement)
         assert out.VR == 'US'
         assert out.value == 0xfffe

+    def test_empty_value(self):
+        \"\"\"Regression test for #1193: empty value raises exception.\"\"\"
+        ds = Dataset()
+        elem = RawDataElement(0x00280106, 'US or SS', 0, None, 0, True, True)
+        ds[0x00280106] = elem
+        out = correct_ambiguous_vr_element(elem, ds, True)
+        assert isinstance(out, DataElement)
+        assert out.VR == 'US'
+
+        ds.LUTDescriptor = [1, 1, 1]
+        elem = RawDataElement(0x00283006, 'US or SS', 0, None, 0, True, True)
+        assert out.value is None
+        ds[0x00283006] = elem
+        out = correct_ambiguous_vr_element(elem, ds, True)
+        assert isinstance(out, DataElement)
+        assert out.VR == 'US'
+        assert out.value is None
+

 class TestWriteAmbiguousVR:
     \"\"\"Attempt to write data elements with ambiguous VR.\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1194(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1194',
        'repo': 'pydicom/pydicom',
        'base_commit': '5e70c1dfe09820023fec519dac4c51bebcb7f60d',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_empty_value"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
