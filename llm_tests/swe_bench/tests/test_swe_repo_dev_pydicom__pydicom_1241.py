# == SWE data pydicom__pydicom_1241 problem statement:
# == Git info: pydicom/pydicom, 9d69811e539774f296c2f289839147e741251716

import pytest

problem_statement = r"""
Add support for Extended Offset Table to encaps module
[CP1818](http://webcache.googleusercontent.com/search?q=cache:xeWXtrAs9G4J:ftp://medical.nema.org/medical/dicom/final/cp1818_ft_whenoffsettabletoosmall.pdf) added the use of an Extended Offset Table for encapsulated pixel data when the Basic Offset Table isn't suitable.
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_encaps.py b/pydicom/tests/test_encaps.py
--- a/pydicom/tests/test_encaps.py
+++ b/pydicom/tests/test_encaps.py
@@ -1,6 +1,8 @@
-# Copyright 2008-2018 pydicom authors. See LICENSE file for details.
+# Copyright 2008-2020 pydicom authors. See LICENSE file for details.
 \"\"\"Test for encaps.py\"\"\"

+from struct import unpack
+
 import pytest

 from pydicom import dcmread
@@ -16,7 +18,8 @@
     read_item,
     fragment_frame,
     itemise_frame,
-    encapsulate
+    encapsulate,
+    encapsulate_extended
 )
 from pydicom.filebase import DicomBytesIO

@@ -36,7 +39,7 @@ def test_bad_tag(self):
         fp.is_little_endian = True
         with pytest.raises(ValueError,
                            match=r"Unexpected tag '\(fffe, e100\)' when "
-                                 r"parsing the Basic Table Offset item."):
+                                 r"parsing the Basic Table Offset item"):
             get_frame_offsets(fp)

     def test_bad_length_multiple(self):
@@ -49,7 +52,7 @@ def test_bad_length_multiple(self):
         fp.is_little_endian = True
         with pytest.raises(ValueError,
                            match="The length of the Basic Offset Table item"
-                                 " is not a multiple of 4."):
+                                 " is not a multiple of 4"):
             get_frame_offsets(fp)

     def test_zero_length(self):
@@ -138,7 +141,7 @@ def test_item_bad_tag(self):
         fp.is_little_endian = True
         msg = (
             r"Unexpected tag '\(0010, 0010\)' at offset 12 when parsing the "
-            r"encapsulated pixel data fragment items."
+            r"encapsulated pixel data fragment items"
         )
         with pytest.raises(ValueError, match=msg):
             get_nr_fragments(fp)
@@ -212,7 +215,7 @@ def test_item_undefined_length(self):
         with pytest.raises(ValueError,
                            match="Undefined item length at offset 4 when "
                                  "parsing the encapsulated pixel data "
-                                 "fragments."):
+                                 "fragments"):
             next(fragments)
         pytest.raises(StopIteration, next, fragments)

@@ -249,8 +252,7 @@ def test_item_bad_tag(self):
         with pytest.raises(ValueError,
                            match=r"Unexpected tag '\(0010, 0010\)' at offset "
                                  r"12 when parsing the encapsulated pixel "
-                                 r"data "
-                                 r"fragment items."):
+                                 r"data fragment items"):
             next(fragments)
         pytest.raises(StopIteration, next, fragments)

@@ -1199,3 +1201,63 @@ def test_encapsulate_bot(self):
             b'\xfe\xff\x00\xe0'  # Next item tag
             b'\xe6\x0e\x00\x00'  # Next item length
         )
+
+    def test_encapsulate_bot_large_raises(self):
+        \"\"\"Test exception raised if too much pixel data for BOT.\"\"\"
+
+        class FakeBytes(bytes):
+            length = -1
+
+            def __len__(self):
+                return self.length
+
+            def __getitem__(self, s):
+                return b'\x00' * 5
+
+        frame_a = FakeBytes()
+        frame_a.length = 2**32 - 1 - 8  # 8 for first BOT item tag/length
+        frame_b = FakeBytes()
+        frame_b.length = 10
+        data = encapsulate([frame_a, frame_b], has_bot=True)
+
+        frame_a.length = 2**32 - 1 - 7
+        msg = (
+            r"The total length of the encapsulated frame data \(4294967296 "
+            r"bytes\) will be greater than the maximum allowed by the Basic "
+        )
+        with pytest.raises(ValueError, match=msg):
+            encapsulate([frame_a, frame_b], has_bot=True)
+
+
+class TestEncapsulateExtended:
+    \"\"\"Tests for encaps.encapsulate_extended.\"\"\"
+    def test_encapsulate(self):
+        ds = dcmread(JP2K_10FRAME_NOBOT)
+        frames = decode_data_sequence(ds.PixelData)
+        assert len(frames) == 10
+
+        out = encapsulate_extended(frames)
+        # Pixel Data encapsulated OK
+        assert isinstance(out[0], bytes)
+        test_frames = decode_data_sequence(out[0])
+        for a, b in zip(test_frames, frames):
+            assert a == b
+
+        # Extended Offset Table is OK
+        assert isinstance(out[1], bytes)
+        assert [
+            0x0000,  # 0
+            0x0eee,  # 3822
+            0x1df6,  # 7670
+            0x2cf8,  # 11512
+            0x3bfc,  # 15356
+            0x4ade,  # 19166
+            0x59a2,  # 22946
+            0x6834,  # 26676
+            0x76e2,  # 30434
+            0x8594  # 34196
+        ] == list(unpack('<10Q', out[1]))
+
+        # Extended Offset Table Lengths are OK
+        assert isinstance(out[2], bytes)
+        assert [len(f) for f in frames] == list(unpack('<10Q', out[2]))
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1241(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1241',
        'repo': 'pydicom/pydicom',
        'base_commit': '9d69811e539774f296c2f289839147e741251716',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_bad_tag", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_bad_length_multiple", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_zero_length", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_multi_frame", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_single_frame", "pydicom/tests/test_encaps.py::TestGetFrameOffsets::test_not_little_endian", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_item_undefined_length", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_item_sequence_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_item_bad_tag", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_single_fragment_no_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_multi_fragments_no_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_single_fragment_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_multi_fragments_delimiter", "pydicom/tests/test_encaps.py::TestGetNrFragments::test_not_little_endian", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_item_undefined_length", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_item_sequence_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_item_bad_tag", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_single_fragment_no_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_multi_fragments_no_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_single_fragment_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_multi_fragments_delimiter", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFragment::test_not_little_endian", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_empty_bot_single_fragment", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_empty_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_bot_single_fragment", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_multi_frame_one_to_one", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_multi_frame_three_to_one", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_multi_frame_varied_ratio", "pydicom/tests/test_encaps.py::TestGeneratePixelDataFrames::test_empty_bot_multi_fragments_per_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_single_fragment", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_no_nr_frames_raises", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_too_few_fragments", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_multi_fragments_per_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_no_marker", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_empty_bot_missing_marker", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_bot_single_fragment", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_multi_frame_one_to_one", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_multi_frame_three_to_one", "pydicom/tests/test_encaps.py::TestGeneratePixelData::test_multi_frame_varied_ratio", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_empty_bot_single_fragment", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_empty_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_bot_single_fragment", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_bot_triple_fragment_single_frame", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_multi_frame_one_to_one", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_multi_frame_three_to_one", "pydicom/tests/test_encaps.py::TestDecodeDataSequence::test_multi_frame_varied_ratio", "pydicom/tests/test_encaps.py::TestDefragmentData::test_defragment", "pydicom/tests/test_encaps.py::TestReadItem::test_item_undefined_length", "pydicom/tests/test_encaps.py::TestReadItem::test_item_sequence_delimiter", "pydicom/tests/test_encaps.py::TestReadItem::test_item_sequence_delimiter_zero_length", "pydicom/tests/test_encaps.py::TestReadItem::test_item_bad_tag", "pydicom/tests/test_encaps.py::TestReadItem::test_single_fragment_no_delimiter", "pydicom/tests/test_encaps.py::TestReadItem::test_multi_fragments_no_delimiter", "pydicom/tests/test_encaps.py::TestReadItem::test_single_fragment_delimiter", "pydicom/tests/test_encaps.py::TestReadItem::test_multi_fragments_delimiter", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_single_fragment_even_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_single_fragment_odd_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_even_fragment_even_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_even_fragment_odd_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_odd_fragments_even_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_odd_fragments_odd_data", "pydicom/tests/test_encaps.py::TestFragmentFrame::test_too_many_fragments_raises", "pydicom/tests/test_encaps.py::TestEncapsulateFrame::test_single_item", "pydicom/tests/test_encaps.py::TestEncapsulateFrame::test_two_items", "pydicom/tests/test_encaps.py::TestEncapsulate::test_encapsulate_single_fragment_per_frame_no_bot", "pydicom/tests/test_encaps.py::TestEncapsulate::test_encapsulate_single_fragment_per_frame_bot", "pydicom/tests/test_encaps.py::TestEncapsulate::test_encapsulate_bot", "pydicom/tests/test_encaps.py::TestEncapsulate::test_encapsulate_bot_large_raises", "pydicom/tests/test_encaps.py::TestEncapsulateExtended::test_encapsulate"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
