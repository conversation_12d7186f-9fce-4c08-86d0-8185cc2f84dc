# == SWE data pydicom__pydicom_800 problem statement:
# == Git info: pydicom/pydicom, 2f3586b6f67383b1ec0c24c4772e65119c3f5261

import pytest

problem_statement = r"""
The function generate_uid() generates non-conforming “2.25 .” DICOM UIDs
<!-- Instructions For Filing a Bug: https://github.com/pydicom/pydicom/blob/master/CONTRIBUTING.md#filing-bugs -->

#### Description
It seems there was already a discussion about this function in the past (#125), but the current implementation generates non-conforming DICOM UIDs when called with prefix ‘none’ to trigger that the function generate_uid() should generate a UUID derived UID.

The DICOM Standard requires (see DICOM PS 3.5, B.2 that when a UUID derived UID is constructed it should be in the format “2.25.” + uuid(in its decimal representation string representation)
For example a UUID of f81d4fae-7dec-11d0-a765-00a0c91e6bf6 should become 2.25.329800735698586629295641978511506172918

The current implementation extends the uuid part to the remaining 59 characters. By not following the DICOM formatting rule, receiving systems that are processing DICOM instances created with this library are not capable of converting the generated “2.25” UID back to a UUID. Due to the extra sha512 operation on the UUID, the variant and version info of the UUID are also lost.

#### Steps/Code to Reproduce
- call generate_uid() to generate a "2.25." DICOM UID

#### Expected Results
A conforming unique DICOM UID is returned.

#### Actual Results
Non conforming UID is returned.
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_uid.py b/pydicom/tests/test_uid.py
--- a/pydicom/tests/test_uid.py
+++ b/pydicom/tests/test_uid.py
@@ -1,6 +1,8 @@
 # Copyright 2008-2018 pydicom authors. See LICENSE file for details.
 \"\"\"Test suite for uid.py\"\"\"

+import uuid
+
 import pytest

 from pydicom.uid import UID, generate_uid, PYDICOM_ROOT_UID, JPEGLSLossy
@@ -57,6 +59,24 @@ def test_entropy_src_custom(self):
         assert uid == rf
         assert len(uid) == 64

+    def test_none(self):
+        \"\"\"Test generate_uid(None).\"\"\"
+        uid = generate_uid(prefix=None)
+        # Check prefix
+        assert '2.25.' == uid[:5]
+        # Check UUID suffix
+        as_uuid = uuid.UUID(int=int(uid[5:]))
+        assert isinstance(as_uuid, uuid.UUID)
+        assert as_uuid.version == 4
+        assert as_uuid.variant == uuid.RFC_4122
+
+    def test_none_iterate(self):
+        \"\"\"Test generate_uid(None) generates valid UIDs.\"\"\"
+        # Generate random UIDs, if a bad method then should eventually fail
+        for ii in range(100000):
+            uid = generate_uid(None)
+            assert uid.is_valid
+

 class TestUID(object):
     \"\"\"Test DICOM UIDs\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_800(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-800',
        'repo': 'pydicom/pydicom',
        'base_commit': '2f3586b6f67383b1ec0c24c4772e65119c3f5261',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_uid.py::TestGenerateUID::test_none"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
