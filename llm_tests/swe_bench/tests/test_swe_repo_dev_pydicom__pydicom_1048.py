# == SWE data pydicom__pydicom_1048 problem statement:
# == Git info: pydicom/pydicom, 00c248441ffb8b7d46c6d855b723e696a8f5aada

import pytest

problem_statement = r"""
dcmread cannot handle pathlib.Path objects
**Describe the bug**
The `dcmread()` currently fails when passed an instance of `pathlib.Path`. The problem is the following line:
https://github.com/pydicom/pydicom/blob/8b0bbaf92d7a8218ceb94dedbee3a0463c5123e3/pydicom/filereader.py#L832

**Expected behavior**
`dcmread()` should open and read the file to which the `pathlib.Path` object points.

The line above should probably be:
```python
if isinstance(fp, (str, Path)):
````

**Steps To Reproduce**
```python
from pathlib import Path
from pydicom.filereader import dcmread

dcm_filepath = Path('path/to/file')
dcmread(dcm_filepath)
```
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filereader.py b/pydicom/tests/test_filereader.py
--- a/pydicom/tests/test_filereader.py
+++ b/pydicom/tests/test_filereader.py
@@ -7,6 +7,7 @@
 from io import BytesIO
 import os
 import shutil
+from pathlib import Path
 from struct import unpack
 import sys
 import tempfile
@@ -114,6 +115,10 @@ def test_UTF8_filename(self):
         os.remove(utf8_filename)
         assert ds is not None

+    def test_pathlib_path_filename(self):
+        \"\"\"Check that file can be read using pathlib.Path\"\"\"
+        ds = dcmread(Path(priv_SQ_name))
+
     def test_RTPlan(self):
         \"\"\"Returns correct values for sample data elements in test
         RT Plan file.
diff --git a/pydicom/tests/test_fileutil.py b/pydicom/tests/test_fileutil.py
new file mode 100644
--- /dev/null
+++ b/pydicom/tests/test_fileutil.py
@@ -0,0 +1,37 @@
+# Copyright 2008-2020 pydicom authors. See LICENSE file for details.
+\"\"\"Test suite for util functions\"\"\"
+import sys
+from io import BytesIO
+from pathlib import Path
+
+import pytest
+
+from pydicom.fileutil import path_from_pathlike
+
+
+class PathLike:
+    \"\"\"Minimal example for path-like object\"\"\"
+    def __init__(self, path: str):
+        self.path = path
+
+    def __fspath__(self):
+        return self.path
+
+
+class TestPathFromPathLike:
+    \"\"\"Test the fileutil module\"\"\"
+
+    def test_non_pathlike_is_returned_unaltered(self):
+        assert 'test.dcm' == path_from_pathlike('test.dcm')
+        assert path_from_pathlike(None) is None
+        file_like = BytesIO()
+        assert file_like == path_from_pathlike(file_like)
+        assert 42 == path_from_pathlike(42)
+
+    def test_pathlib_path(self):
+        assert 'test.dcm' == path_from_pathlike(Path('test.dcm'))
+
+    @pytest.mark.skipif(sys.version_info < (3, 6),
+                        reason="Path-like objects introduced in Python 3.6")
+    def test_path_like(self):
+        assert 'test.dcm' == path_from_pathlike(PathLike('test.dcm'))
diff --git a/pydicom/tests/test_filewriter.py b/pydicom/tests/test_filewriter.py
--- a/pydicom/tests/test_filewriter.py
+++ b/pydicom/tests/test_filewriter.py
@@ -6,6 +6,7 @@
 from datetime import date, datetime, time, timedelta, timezone
 from io import BytesIO
 import os
+from pathlib import Path
 from platform import python_implementation

 from struct import unpack
@@ -137,6 +138,14 @@ def testJPEG2000(self):
            them identical (JPEG2K file).\"\"\"
         self.compare(jpeg_name)

+    def test_pathlib_path_filename(self):
+        \"\"\"Check that file can be written using pathlib.Path\"\"\"
+        ds = dcmread(Path(ct_name))
+        ds.save_as(self.file_out, write_like_original=True)
+        self.file_out.seek(0)
+        ds1 = dcmread(self.file_out)
+        assert ds.PatientName == ds1.PatientName
+
     def testListItemWriteBack(self):
         \"\"\"Change item in a list and confirm
           it is written to file\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1048(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1048',
        'repo': 'pydicom/pydicom',
        'base_commit': '00c248441ffb8b7d46c6d855b723e696a8f5aada',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filereader.py::TestReader::test_empty_numbers_tag", "pydicom/tests/test_filereader.py::TestReader::test_UTF8_filename", "pydicom/tests/test_filereader.py::TestReader::test_pathlib_path_filename", "pydicom/tests/test_filereader.py::TestReader::test_RTPlan", "pydicom/tests/test_filereader.py::TestReader::test_RTDose", "pydicom/tests/test_filereader.py::TestReader::test_CT", "pydicom/tests/test_filereader.py::TestReader::test_CT_PixelData", "pydicom/tests/test_filereader.py::TestReader::test_no_force", "pydicom/tests/test_filereader.py::TestReader::test_RTStruct", "pydicom/tests/test_filereader.py::TestReader::test_dir", "pydicom/tests/test_filereader.py::TestReader::test_MR", "pydicom/tests/test_filereader.py::TestReader::test_deflate", "pydicom/tests/test_filereader.py::TestReader::test_no_pixels_read", "pydicom/tests/test_filereader.py::TestReader::test_specific_tags", "pydicom/tests/test_filereader.py::TestReader::test_specific_tags_with_unknown_length_SQ", "pydicom/tests/test_filereader.py::TestReader::test_specific_tags_with_unknown_length_tag", "pydicom/tests/test_filereader.py::TestReader::test_tag_with_unknown_length_tag_too_short", "pydicom/tests/test_filereader.py::TestReader::test_private_SQ", "pydicom/tests/test_filereader.py::TestReader::test_nested_private_SQ", "pydicom/tests/test_filereader.py::TestReader::test_no_meta_group_length", "pydicom/tests/test_filereader.py::TestReader::test_no_transfer_syntax_in_meta", "pydicom/tests/test_filereader.py::TestReader::test_explicit_VR_little_endian_no_meta", "pydicom/tests/test_filereader.py::TestReader::test_explicit_VR_big_endian_no_meta", "pydicom/tests/test_filereader.py::TestReader::test_planar_config", "pydicom/tests/test_filereader.py::TestReader::test_correct_ambiguous_vr", "pydicom/tests/test_filereader.py::TestReader::test_correct_ambiguous_explicit_vr", "pydicom/tests/test_filereader.py::TestReader::test_correct_ambiguous_vr_compressed", "pydicom/tests/test_filereader.py::TestReader::test_long_specific_char_set", "pydicom/tests/test_filereader.py::TestReader::test_no_preamble_file_meta_dataset", "pydicom/tests/test_filereader.py::TestReader::test_no_preamble_command_group_dataset", "pydicom/tests/test_filereader.py::TestReader::test_group_length_wrong", "pydicom/tests/test_filereader.py::TestReader::test_preamble_command_meta_no_dataset", "pydicom/tests/test_filereader.py::TestReader::test_preamble_meta_no_dataset", "pydicom/tests/test_filereader.py::TestReader::test_preamble_commandset_no_dataset", "pydicom/tests/test_filereader.py::TestReader::test_meta_no_dataset", "pydicom/tests/test_filereader.py::TestReader::test_commandset_no_dataset", "pydicom/tests/test_filereader.py::TestReader::test_file_meta_dataset_implicit_vr", "pydicom/tests/test_filereader.py::TestReader::test_no_dataset", "pydicom/tests/test_filereader.py::TestReader::test_empty_file", "pydicom/tests/test_filereader.py::TestReader::test_empty_specific_character_set", "pydicom/tests/test_filereader.py::TestReader::test_dcmread_does_not_raise", "pydicom/tests/test_filereader.py::TestReader::test_lut_descriptor", "pydicom/tests/test_filereader.py::TestIncorrectVR::test_implicit_vr_expected_explicit_used", "pydicom/tests/test_filereader.py::TestIncorrectVR::test_implicit_vr_expected_explicit_used_strict", "pydicom/tests/test_filereader.py::TestIncorrectVR::test_explicit_vr_expected_implicit_used", "pydicom/tests/test_filereader.py::TestIncorrectVR::test_explicit_vr_expected_implicit_used_strict", "pydicom/tests/test_filereader.py::TestIncorrectVR::test_seq_item_looks_like_explicit_VR", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[\\x00A-0x00", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[@A-0x40", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[[A-0x5b", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[`A-0x60", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[{A-0x7b", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[\\xffA-0xff", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[A\\x00-0x41", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[Z\\x00-0x5a", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[a\\x00-0x61", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[z\\x00-0x7a", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[\\x00Z-0x00", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[\\x00a-0x00", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[\\x00z-0x00", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[AA-AA]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[AZ-AZ]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[ZA-ZA]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[ZZ-ZZ]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[Aa-Aa]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[Az-Az]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[aA-aA]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[aZ-aZ]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[aa-aa]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[az-az]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[Za-Za]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[Zz-Zz]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[zA-zA]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[zZ-zZ]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[za-za]", "pydicom/tests/test_filereader.py::TestUnknownVR::test_fail_decode_msg[zz-zz]", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OD_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OD_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OL_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OL_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UC_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UC_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UR_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UR_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_AE", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OV_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_OV_explicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_SV_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UV_implicit_little", "pydicom/tests/test_filereader.py::TestReadDataElement::test_read_UV_explicit_little", "pydicom/tests/test_filereader.py::TestDeferredRead::test_time_check", "pydicom/tests/test_filereader.py::TestDeferredRead::test_file_exists", "pydicom/tests/test_filereader.py::TestDeferredRead::test_values_identical", "pydicom/tests/test_filereader.py::TestDeferredRead::test_zipped_deferred", "pydicom/tests/test_filereader.py::TestDeferredRead::test_filelike_deferred", "pydicom/tests/test_filereader.py::TestReadTruncatedFile::testReadFileWithMissingPixelData", "pydicom/tests/test_filereader.py::TestReadTruncatedFile::testReadFileWithMissingPixelDataArray", "pydicom/tests/test_filereader.py::TestFileLike::test_read_file_given_file_object", "pydicom/tests/test_filereader.py::TestFileLike::test_read_file_given_file_like_object", "pydicom/tests/test_filereader.py::TestDataElementGenerator::test_little_endian_explicit", "pydicom/tests/test_filereader.py::TestDataElementGenerator::test_little_endian_implicit", "pydicom/tests/test_filereader.py::TestDataElementGenerator::test_big_endian_explicit", "pydicom/tests/test_fileutil.py::TestPathFromPathLike::test_non_pathlike_is_returned_unaltered", "pydicom/tests/test_fileutil.py::TestPathFromPathLike::test_pathlib_path", "pydicom/tests/test_fileutil.py::TestPathFromPathLike::test_path_like", "pydicom/tests/test_filewriter.py::TestWriteFile::testRTPlan", "pydicom/tests/test_filewriter.py::TestWriteFile::testRTDose", "pydicom/tests/test_filewriter.py::TestWriteFile::testCT", "pydicom/tests/test_filewriter.py::TestWriteFile::testMR", "pydicom/tests/test_filewriter.py::TestWriteFile::testUnicode", "pydicom/tests/test_filewriter.py::TestWriteFile::testMultiPN", "pydicom/tests/test_filewriter.py::TestWriteFile::testJPEG2000", "pydicom/tests/test_filewriter.py::TestWriteFile::test_pathlib_path_filename", "pydicom/tests/test_filewriter.py::TestWriteFile::testListItemWriteBack", "pydicom/tests/test_filewriter.py::TestWriteFile::testwrite_short_uid", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_no_ts", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_double_filemeta", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_ffff_ffff", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_removes_grouplength", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_empty_sequence", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testRTPlan", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testRTDose", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testCT", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testMR", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testUnicode", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testMultiPN", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testJPEG2000", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_pathlib_path_filename", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testListItemWriteBack", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::testwrite_short_uid", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_no_ts", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_double_filemeta", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_ffff_ffff", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_removes_grouplength", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_empty_sequence", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_multivalue_DA", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_empty_AT", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_empty_LO", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_DA", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_multi_DA", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_TM", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_multi_TM", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_DT", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_multi_DT", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_ascii_vr_with_padding", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_OD_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_OD_explicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_OL_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_OL_explicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UC_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UC_explicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UR_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UR_explicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_UN_implicit_little", "pydicom/tests/test_filewriter.py::TestWriteDataElement::test_write_unknown_vr_raises", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_pixel_representation_vm_one", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_pixel_representation_vm_three", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_pixel_data", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_waveform_bits_allocated", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_lut_descriptor", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_overlay", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_sequence", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_write_new_ambiguous", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_explicit_using_attribute", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_explicit_using_index", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_implicit_using_attribute", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_implicit_using_index", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_not_ambiguous", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_not_ambiguous_raw_data_element", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_correct_ambiguous_data_element", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVRElement::test_correct_ambiguous_raw_data_element", "pydicom/tests/test_filewriter.py::TestWriteAmbiguousVR::test_write_explicit_vr_raises", "pydicom/tests/test_filewriter.py::TestWriteAmbiguousVR::test_write_explicit_vr_little_endian", "pydicom/tests/test_filewriter.py::TestWriteAmbiguousVR::test_write_explicit_vr_big_endian", "pydicom/tests/test_filewriter.py::TestScratchWrite::testImpl_LE_deflen_write", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_preamble_default", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_preamble_custom", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_no_preamble", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_none_preamble", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_bad_preamble", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_prefix", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_prefix_none", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_ds_changed", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_raw_elements_preserved_implicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_raw_elements_preserved_explicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_implicit_to_explicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_write_dataset", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_write_dataset_with_explicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_implicit_to_explicit_vr_using_destination", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_explicit_to_implicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_big_to_little_endian", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_little_to_big_endian", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_changed_character_set", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_transfer_syntax_added", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_private_tag_vr_from_implicit_data", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_convert_rgb_from_implicit_to_explicit_vr", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_transfer_syntax_not_added", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_transfer_syntax_raises", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_media_storage_sop_class_uid_added", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_write_no_file_meta", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_raise_no_file_meta", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_add_file_meta", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_standard", "pydicom/tests/test_filewriter.py::TestWriteToStandard::test_commandset_no_written", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_bad_elements", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_missing_elements", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_group_length", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_group_length_updated", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_version", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_implementation_version_name_length", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_implementation_class_uid_length", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoToStandard::test_filelike_position", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_default", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_custom", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_no_preamble", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_ds_unchanged", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_file_meta_unchanged", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_filemeta_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_filemeta_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_commandset_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_commandset_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_commandset_filemeta_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_commandset_filemeta_dataset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_commandset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_commandset_filemeta", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_commandset", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_preamble_commandset_filemeta", "pydicom/tests/test_filewriter.py::TestWriteNonStandard::test_read_write_identical", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_transfer_syntax_not_added", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_bad_elements", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_missing_elements", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_group_length_updated", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_filelike_position", "pydicom/tests/test_filewriter.py::TestWriteFileMetaInfoNonStandard::test_meta_unchanged", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_write_empty_value", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_write_list", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_write_singleton", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_exception", "pydicom/tests/test_filewriter.py::TestWriteNumbers::test_write_big_endian", "pydicom/tests/test_filewriter.py::TestWritePN::test_no_encoding", "pydicom/tests/test_filewriter.py::TestWritePN::test_single_byte_multi_charset_groups", "pydicom/tests/test_filewriter.py::TestWritePN::test_single_byte_multi_charset_values", "pydicom/tests/test_filewriter.py::TestWriteText::test_no_encoding", "pydicom/tests/test_filewriter.py::TestWriteText::test_single_byte_multi_charset_text", "pydicom/tests/test_filewriter.py::TestWriteText::test_encode_mixed_charsets_text", "pydicom/tests/test_filewriter.py::TestWriteText::test_single_byte_multi_charset_text_multivalue", "pydicom/tests/test_filewriter.py::TestWriteText::test_invalid_encoding", "pydicom/tests/test_filewriter.py::TestWriteText::test_invalid_encoding_enforce_standard", "pydicom/tests/test_filewriter.py::TestWriteText::test_single_value_with_delimiters", "pydicom/tests/test_filewriter.py::TestWriteDT::test_format_dt", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_little_endian_correct_data", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_big_endian_correct_data", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_little_endian_incorrect_data", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_big_endian_incorrect_data", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_writing_to_gzip", "pydicom/tests/test_filewriter.py::TestWriteUndefinedLengthPixelData::test_writing_too_big_data_in_explicit_encoding"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
