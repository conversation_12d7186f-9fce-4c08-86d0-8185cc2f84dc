# == SWE data pydicom__pydicom_938 problem statement:
# == Git info: pydicom/pydicom, 6d8ef0bfcec983e5f8bd8a2e359ff318fe9fcf65

import pytest

problem_statement = r"""
[python 3.8] failing tests: various issues but "max recursion depth reached" seems to be one
#### Description
Fedora is beginning to test python packages against python 3.8. Pydicom builds but tests fail with errors.

#### Steps/Code to Reproduce

```
python setup.py build
python setup.py install
pytest
```

The complete build log is attached. It includes the complete build process. The root log is also attached. These are the versions of other python libraries that are in use:

```
python3-dateutil-1:2.8.0-5.fc32.noarch
python3-devel-3.8.0~b3-4.fc32.x86_64
python3-numpy-1:1.17.0-3.fc32.x86_64
python3-numpydoc-0.9.1-3.fc32.noarch
python3-pytest-4.6.5-3.fc32.noarch
python3-setuptools-41.0.1-8.fc32.noarch
python3-six-1.12.0-5.fc32.noarch
```

[build-log.txt](https://github.com/pydicom/pydicom/files/3527558/build-log.txt)
[root-log.txt](https://github.com/pydicom/pydicom/files/3527559/root-log.txt)

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_valuerep.py b/pydicom/tests/test_valuerep.py
--- a/pydicom/tests/test_valuerep.py
+++ b/pydicom/tests/test_valuerep.py
@@ -90,7 +90,6 @@ def test_pickling(self):

 class TestDS(object):
     \"\"\"Unit tests for DS values\"\"\"
-
     def test_empty_value(self):
         assert DS(None) is None
         assert '' == DS('')
@@ -106,7 +105,6 @@ def test_float_values(self):

 class TestDSfloat(object):
     \"\"\"Unit tests for pickling DSfloat\"\"\"
-
     def test_pickling(self):
         # Check that a pickled DSFloat is read back properly
         x = pydicom.valuerep.DSfloat(9.0)
@@ -116,10 +114,25 @@ def test_pickling(self):
         assert x.real == x2.real
         assert x.original_string == x2.original_string

+    def test_str(self):
+        \"\"\"Test DSfloat.__str__().\"\"\"
+        val = pydicom.valuerep.DSfloat(1.1)
+        assert '1.1' == str(val)
+
+        val = pydicom.valuerep.DSfloat('1.1')
+        assert '1.1' == str(val)
+
+    def test_repr(self):
+        \"\"\"Test DSfloat.__repr__().\"\"\"
+        val = pydicom.valuerep.DSfloat(1.1)
+        assert '"1.1"' == repr(val)
+
+        val = pydicom.valuerep.DSfloat('1.1')
+        assert '"1.1"' == repr(val)
+

 class TestDSdecimal(object):
     \"\"\"Unit tests for pickling DSdecimal\"\"\"
-
     def test_pickling(self):
         # Check that a pickled DSdecimal is read back properly
         # DSdecimal actually prefers original_string when
@@ -142,7 +155,6 @@ def test_float_value(self):

 class TestIS(object):
     \"\"\"Unit tests for IS\"\"\"
-
     def test_empty_value(self):
         assert IS(None) is None
         assert '' == IS('')
@@ -182,6 +194,22 @@ def test_overflow(self):
             pydicom.valuerep.IS(3103050000)
         config.enforce_valid_values = original_flag

+    def test_str(self):
+        \"\"\"Test IS.__str__().\"\"\"
+        val = pydicom.valuerep.IS(1)
+        assert '1' == str(val)
+
+        val = pydicom.valuerep.IS('1')
+        assert '1' == str(val)
+
+    def test_repr(self):
+        \"\"\"Test IS.__repr__().\"\"\"
+        val = pydicom.valuerep.IS(1)
+        assert '"1"' == repr(val)
+
+        val = pydicom.valuerep.IS('1')
+        assert '"1"' == repr(val)
+

 class TestBadValueRead(object):
     \"\"\"Unit tests for handling a bad value for a VR
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_938(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-938',
        'repo': 'pydicom/pydicom',
        'base_commit': '6d8ef0bfcec983e5f8bd8a2e359ff318fe9fcf65',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_valuerep.py::TestDSfloat::test_str", "pydicom/tests/test_valuerep.py::TestDSfloat::test_repr", "pydicom/tests/test_valuerep.py::TestIS::test_str", "pydicom/tests/test_valuerep.py::TestIS::test_repr"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
