# == SWE data pydicom__pydicom_809 problem statement:
# == Git info: pydicom/pydicom, 356a51ab4bc54fd18950041ebc44dbfa1a425a10

import pytest

problem_statement = r"""
"Printing" of certain dicom files fails once, but works the second time
<!-- Instructions For Filing a Bug: https://github.com/pydicom/pydicom/blob/master/CONTRIBUTING.md#filing-bugs -->

#### Description
"Printing" of certain dicom files (see [example](https://github.com/pydicom/pydicom/files/2865551/dicom_exception.zip)) fails once, but not the second time

#### Steps/Code to Reproduce
```python
from pydicom import read_file

a = read_file('...')
print(a)
# triggers exception: AttributeError: With tag (0028, 3000) got exception: Failed to resolve ambiguous VR for tag (0028, 3002): 'Dataset' object has no attribute 'PixelRepresentation'

# try same thing again...
print(a)
# just works...
```

#### Versions
Behaviour as described above at least on:
```
Linux-4.18.0-15-generic-x86_64-with-Ubuntu-18.10-cosmic
('Python', '2.7.15+ (default, Oct  2 2018, 22:12:08) \n[GCC 8.2.0]')
('numpy', '1.14.5')
('pydicom', '1.3.0.dev0')
```
and


```
('pydicom', '1.2.2')
```

Works as expected on:
```
Linux-4.18.0-15-generic-x86_64-with-Ubuntu-18.10-cosmic
('Python', '2.7.15+ (default, Oct  2 2018, 22:12:08) \n[GCC 8.2.0]')
('pydicom', '1.0.1')
```
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filewriter.py b/pydicom/tests/test_filewriter.py
--- a/pydicom/tests/test_filewriter.py
+++ b/pydicom/tests/test_filewriter.py
@@ -864,6 +864,47 @@ def test_write_new_ambiguous(self):
         assert ds[0x00283002].VR == 'US'
         assert ds.LUTDescriptor == [1, 0]

+    def test_ambiguous_element_in_sequence_explicit(self):
+        \"\"\"Test that writing a sequence with an ambiguous element
+        as explicit transfer syntax works.\"\"\"
+        # regression test for #804
+        ds = Dataset()
+        ds.PixelRepresentation = 0
+        ds.ModalityLUTSequence = [Dataset()]
+        ds.ModalityLUTSequence[0].LUTDescriptor = [0, 0, 16]
+        ds.ModalityLUTSequence[0].LUTExplanation = None
+        ds.ModalityLUTSequence[0].ModalityLUTType = 'US'  # US = unspecified
+        ds.ModalityLUTSequence[0].LUTData = b'\x0000\x149a\x1f1c\xc2637'
+
+        ds.is_little_endian = True
+        ds.is_implicit_VR = False
+        fp = BytesIO()
+        ds.save_as(fp, write_like_original=True)
+
+        ds = dcmread(fp, force=True)
+        assert 'US' == ds.ModalityLUTSequence[0][0x00283002].VR
+
+    def test_ambiguous_element_in_sequence_implicit(self):
+        \"\"\"Test that reading a sequence with an ambiguous element
+        from a file with implicit transfer syntax works.\"\"\"
+        # regression test for #804
+        ds = Dataset()
+        ds.PixelRepresentation = 0
+        ds.ModalityLUTSequence = [Dataset()]
+        ds.ModalityLUTSequence[0].LUTDescriptor = [0, 0, 16]
+        ds.ModalityLUTSequence[0].LUTExplanation = None
+        ds.ModalityLUTSequence[0].ModalityLUTType = 'US'  # US = unspecified
+        ds.ModalityLUTSequence[0].LUTData = b'\x0000\x149a\x1f1c\xc2637'
+
+        ds.is_little_endian = True
+        ds.is_implicit_VR = True
+        fp = BytesIO()
+        ds.save_as(fp, write_like_original=True)
+        ds = dcmread(fp, force=True)
+        # we first have to access the value to trigger correcting the VR
+        assert 16 == ds.ModalityLUTSequence[0].LUTDescriptor[2]
+        assert 'US' == ds.ModalityLUTSequence[0][0x00283002].VR
+

 class TestCorrectAmbiguousVRElement(object):
     \"\"\"Test filewriter.correct_ambiguous_vr_element\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_809(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-809',
        'repo': 'pydicom/pydicom',
        'base_commit': '356a51ab4bc54fd18950041ebc44dbfa1a425a10',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_explicit", "pydicom/tests/test_filewriter.py::TestCorrectAmbiguousVR::test_ambiguous_element_in_sequence_implicit"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
