# == SWE data pydicom__pydicom_1633 problem statement:
# == Git info: pydicom/pydicom, 98ac88706e7ab17cd279c94949ac6af4e87f341d

import pytest

problem_statement = r"""
OverflowError "VR of 'DS' must be <= 16 characters long" triggered when element is 16 characters long
**Describe the bug**

`OverflowError` triggered while accessing `PixelData`, which the values compliant with the standard. In the sample referenced in the example below, we have this, which satisfies DS VR:

```
(0028,0030) DS [.002006091181818\.002006091181818]      #  34, 2 PixelSpacing
```

But nevertheless the error is triggered while trying to access `PixelData`:

```
OverflowError: Values for elements with a VR of 'DS' must be <= 16 characters long,
but the float provided requires > 16 characters to be accurately represented. Use a
smaller string, set 'config.settings.reading_validation_mode' to 'WARN' to override
the length check, or explicitly construct a DS object with 'auto_format' set to True
```

**Expected behavior**

`OverflowError` does not get triggered.

**Steps To Reproduce**

Follow the steps of this Colab notebook: https://colab.research.google.com/drive/1FcSgjBKazh0YN-jlJYdID0YUTh90CAvZ?usp=sharing

**Your environment**

```
module       | version
------       | -------
platform     | Linux-5.4.144+-x86_64-with-Ubuntu-18.04-bionic
Python       | 3.7.13 (default, Mar 16 2022, 17:37:17)  [GCC 7.5.0]
pydicom      | 2.3.0
gdcm         | _module not found_
jpeg_ls      | _module not found_
numpy        | 1.21.5
PIL          | 9.1.0
pylibjpeg    | _module not found_
openjpeg     | _module not found_
libjpeg      | _module not found_
```

Related issue: https://github.com/imi-bigpicture/wsidicom/issues/49

cc: @DanielaSchacherer @dclunie @hackermd
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_valuerep.py b/pydicom/tests/test_valuerep.py
--- a/pydicom/tests/test_valuerep.py
+++ b/pydicom/tests/test_valuerep.py
@@ -603,6 +603,13 @@ def test_enforce_valid_values_length(self):
             valuerep.DSfloat('3.141592653589793',
                              validation_mode=config.RAISE)

+    def test_handle_missing_leading_zero(self):
+        \"\"\"Test that no error is raised with maximum length DS string
+        without leading zero.\"\"\"
+        # Regression test for #1632
+        valuerep.DSfloat(".002006091181818",
+                         validation_mode=config.RAISE)
+
     def test_DSfloat_auto_format(self):
         \"\"\"Test creating a value using DSfloat copies auto_format\"\"\"
         x = DSfloat(math.pi, auto_format=True)
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1633(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1633',
        'repo': 'pydicom/pydicom',
        'base_commit': '98ac88706e7ab17cd279c94949ac6af4e87f341d',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_valuerep.py::TestDSfloat::test_handle_missing_leading_zero"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
