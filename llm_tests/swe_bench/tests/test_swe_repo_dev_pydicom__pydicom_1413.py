# == SWE data pydicom__pydicom_1413 problem statement:
# == Git info: pydicom/pydicom, f909c76e31f759246cec3708dadd173c5d6e84b1

import pytest

problem_statement = r"""
Error : a bytes-like object is required, not 'MultiValue'
Hello,

I am getting following error while updating the tag LongTrianglePointIndexList (0066,0040),
**TypeError: a bytes-like object is required, not 'MultiValue'**

I noticed that the error  gets produced only when the VR is given as "OL" , works fine with "OB", "OF" etc.

sample code (assume 'lineSeq' is the dicom dataset sequence):
```python
import pydicom
import array
data=list(range(1,10))
data=array.array('H', indexData).tostring()  # to convert to unsigned short
lineSeq.add_new(0x00660040, 'OL', data)
ds.save_as("mydicom")
```
outcome: **TypeError: a bytes-like object is required, not 'MultiValue'**

using version - 2.0.0.0

Any help is appreciated.

Thank you
""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_valuerep.py b/pydicom/tests/test_valuerep.py
--- a/pydicom/tests/test_valuerep.py
+++ b/pydicom/tests/test_valuerep.py
@@ -1546,3 +1546,16 @@ def test_set_value(vr, pytype, vm0, vmN, keyword):
     elem = ds[keyword]
     assert elem.value == list(vmN)
     assert list(vmN) == elem.value
+
+
+@pytest.mark.parametrize("vr, pytype, vm0, vmN, keyword", VALUE_REFERENCE)
+def test_assigning_bytes(vr, pytype, vm0, vmN, keyword):
+    \"\"\"Test that byte VRs are excluded from the backslash check.\"\"\"
+    if pytype == bytes:
+        ds = Dataset()
+        value = b"\x00\x01" + b"\\" + b"\x02\x03"
+        setattr(ds, keyword, value)
+        elem = ds[keyword]
+        assert elem.VR == vr
+        assert elem.value == value
+        assert elem.VM == 1
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1413(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1413',
        'repo': 'pydicom/pydicom',
        'base_commit': 'f909c76e31f759246cec3708dadd173c5d6e84b1',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_valuerep.py::test_assigning_bytes[OD-bytes-vm017-vmN17-DoubleFloatPixelData]", "pydicom/tests/test_valuerep.py::test_assigning_bytes[OL-bytes-vm019-vmN19-TrackPointIndexList]", "pydicom/tests/test_valuerep.py::test_assigning_bytes[OV-bytes-vm020-vmN20-SelectorOVValue]"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
