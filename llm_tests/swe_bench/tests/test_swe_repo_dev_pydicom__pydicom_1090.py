# == SWE data pydicom__pydicom_1090 problem statement:
# == Git info: pydicom/pydicom, 5098c9147fadcb3e5918487036867931435adeb8

import pytest

problem_statement = r"""
Write deflated content when called Transfer Syntax is Deflated Explicit VR Little Endian
**Describe the bug**
After using `dcmread` to read a deflated .dcm file created from pydicom's [CT_small.dcm sample](https://github.com/pydicom/pydicom/blob/v1.4.2/pydicom/data/test_files/CT_small.dcm), with the following file meta information
```
(0002, 0000) File Meta Information Group Length  UL: 178
(0002, 0001) File Meta Information Version       OB: b'\x00\x01'
(0002, 0002) Media Storage SOP Class UID         UI: CT Image Storage
(0002, 0003) Media Storage SOP Instance UID      UI: 1.3.6.1.4.1.5962.1.1.1.1.1.20040119072730.12322
(0002, 0010) Transfer Syntax UID                 UI: Deflated Explicit VR Little Endian
(0002, 0012) Implementation Class UID            UI: 1.2.40.0.13.1.1
(0002, 0013) Implementation Version Name         SH: 'dcm4che-2.0'
```

I use `save_as` to save the file. The output file has an unaltered file meta information section, but the group 8 elements and beyond are not written in deflated format, instead appearing to be LEE. In particular, the specific character set element is easily readable from a hex representation of the file, rather than appearing as gobbledygook like one would expect from a deflated stream.

**Expected behavior**
The bulk of the DCM to be written as Deflated Explicit VR Little Endian or the Transfer Syntax UID to be saved with a value that reflects the actual format of the DCM

**Steps To Reproduce**
```python
❯ py
>>> # CT_small_deflated.dcm is CT_small.dcm, deflated using dcm2dcm
>>> ds = pydicom.dcmread("CT_small_deflated.dcm")

>>> ds.save_as("ds_like_orig.dcm", write_like_original=True)
>>> pydicom.dcmread("ds_like_orig.dcm")
Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38-32\lib\site-packages\pydicom\filereader.py", line 869, in dcmread
    dataset = read_partial(fp, stop_when, defer_size=defer_size,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38-32\lib\site-packages\pydicom\filereader.py", line 729, in read_partial
    unzipped = zlib.decompress(zipped, -zlib.MAX_WBITS)
zlib.error: Error -3 while decompressing data: invalid stored block lengths

>>> ds.save_as("ds_not_like_orig.dcm", write_like_original=False)
>>> pydicom.dcmread("ds_not_like_orig.dcm")
Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38-32\lib\site-packages\pydicom\filereader.py", line 869, in dcmread
    dataset = read_partial(fp, stop_when, defer_size=defer_size,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38-32\lib\site-packages\pydicom\filereader.py", line 729, in read_partial
    unzipped = zlib.decompress(zipped, -zlib.MAX_WBITS)
zlib.error: Error -3 while decompressing data: invalid stored block lengths
```

**Your environment**
Please run the following and paste the output.
```powershell
❯ py -c "import platform; print(platform.platform())"
Windows-10-10.0.18362-SP0

❯ py -c "import sys; print('Python ', sys.version)"
Python  3.8.1 (tags/v3.8.1:1b293b6, Dec 18 2019, 22:39:24) [MSC v.1916 32 bit (Intel)]

❯ py -c "import pydicom; print('pydicom ', pydicom.__version__)"
pydicom  1.4.2
```

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_filewriter.py b/pydicom/tests/test_filewriter.py
--- a/pydicom/tests/test_filewriter.py
+++ b/pydicom/tests/test_filewriter.py
@@ -11,6 +11,7 @@

 from struct import unpack
 from tempfile import TemporaryFile
+import zlib

 import pytest

@@ -49,6 +50,7 @@

 unicode_name = get_charset_files("chrH31.dcm")[0]
 multiPN_name = get_charset_files("chrFrenMulti.dcm")[0]
+deflate_name = get_testdata_file("image_dfl.dcm")

 base_version = '.'.join(str(i) for i in __version_info__)

@@ -77,6 +79,18 @@ def bytes_identical(a_bytes, b_bytes):
         return False, pos  # False if not identical, position of 1st diff


+def as_assertable(dataset):
+    \"\"\"Copy the elements in a Dataset (including the file_meta, if any)
+       to a set that can be safely compared using pytest's assert.
+       (Datasets can't be so compared because DataElements are not
+       hashable.)\"\"\"
+    safe_dict = dict((str(elem.tag) + " " + elem.keyword, elem.value)
+                     for elem in dataset)
+    if hasattr(dataset, "file_meta"):
+        safe_dict.update(as_assertable(dataset.file_meta))
+    return safe_dict
+
+
 class TestWriteFile(object):
     def setup(self):
         self.file_out = TemporaryFile('w+b')
@@ -222,6 +236,41 @@ def test_write_empty_sequence(self):
         ds = read_file(self.file_out)
         assert ds.PerformedProcedureCodeSequence == []

+    def test_write_deflated_retains_elements(self):
+        \"\"\"Read a Deflated Explicit VR Little Endian file, write it,
+           and then read the output, to verify that the written file
+           contains the same data.
+           \"\"\"
+        original = read_file(deflate_name)
+        original.save_as(self.file_out)
+
+        self.file_out.seek(0)
+        rewritten = read_file(self.file_out)
+
+        assert as_assertable(rewritten) == as_assertable(original)
+
+    def test_write_deflated_deflates_post_file_meta(self):
+        \"\"\"Read a Deflated Explicit VR Little Endian file, write it,
+           and then check the bytes in the output, to verify that the
+           written file is deflated past the file meta information.
+           \"\"\"
+        original = read_file(deflate_name)
+        original.save_as(self.file_out)
+
+        first_byte_past_file_meta = 0x14e
+        with open(deflate_name, "rb") as original_file:
+            original_file.seek(first_byte_past_file_meta)
+            original_post_meta_file_bytes = original_file.read()
+        unzipped_original = zlib.decompress(original_post_meta_file_bytes,
+                                            -zlib.MAX_WBITS)
+
+        self.file_out.seek(first_byte_past_file_meta)
+        rewritten_post_meta_file_bytes = self.file_out.read()
+        unzipped_rewritten = zlib.decompress(rewritten_post_meta_file_bytes,
+                                             -zlib.MAX_WBITS)
+
+        assert unzipped_rewritten == unzipped_original
+

 class TestScratchWriteDateTime(TestWriteFile):
     \"\"\"Write and reread simple or multi-value DA/DT/TM data elements\"\"\"
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1090(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1090',
        'repo': 'pydicom/pydicom',
        'base_commit': '5098c9147fadcb3e5918487036867931435adeb8',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_filewriter.py::TestWriteFile::test_write_deflated_retains_elements", "pydicom/tests/test_filewriter.py::TestWriteFile::test_write_deflated_deflates_post_file_meta", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_deflated_retains_elements", "pydicom/tests/test_filewriter.py::TestScratchWriteDateTime::test_write_deflated_deflates_post_file_meta"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
