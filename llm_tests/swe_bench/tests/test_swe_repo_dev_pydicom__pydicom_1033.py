# == SWE data pydicom__pydicom_1033 problem statement:
# == Git info: pydicom/pydicom, 701c1062bf66de2b29df68fa5540be6009943885

import pytest

problem_statement = r"""
to_json does not work with binary data in pixel_array
**Describe the issue**
Loading a dicom file and then performing a to_json() on it does not work with binary data in pixel_array.



**Expected behavior**
I would have expected that a base64 conversion is first performed on the binary data and then encoded to json.

**Steps To Reproduce**
How to reproduce the issue. Please include:
1. A minimum working code sample

import pydicom
ds = pydicom.dcmread('path_to_file')
output = ds.to_json()


2. The traceback (if one occurred)

Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
  File "/.virtualenvs/my_env/lib/python3.7/site-packages/pydicom/dataset.py", line 2003, in to_json
    dump_handler=dump_handler
  File "/.virtualenvs/my_env/lib/python3.7/site-packages/pydicom/dataset.py", line 1889, in _data_element_to_json
    binary_value = data_element.value.encode('utf-8')
AttributeError: 'bytes' object has no attribute 'encode'


3. Which of the following packages are available and their versions:
  * Numpy
numpy==1.17.2
  * Pillow
Pillow==6.1.0
  * JPEG-LS
  * GDCM
4. The anonymized DICOM dataset (if possible).

**Your environment**
Please run the following and paste the output.
```bash
$ python -c "import platform; print(platform.platform())"
Darwin-19.2.0-x86_64-i386-64bit
$ python -c "import sys; print('Python ', sys.version)"
Python  3.7.6 (default, Dec 30 2019, 19:38:26)
[Clang 11.0.0 (clang-1100.0.33.16)]
$ python -c "import pydicom; print('pydicom ', pydicom.__version__)"
pydicom  1.3.0
```

""" # noqa: E501
test_patch = r"""
diff --git a/pydicom/tests/test_json.py b/pydicom/tests/test_json.py
--- a/pydicom/tests/test_json.py
+++ b/pydicom/tests/test_json.py
@@ -1,12 +1,11 @@
 # -*- coding: utf-8 -*-
 # Copyright 2008-2019 pydicom authors. See LICENSE file for details.
 import json
-import sys

 import pytest

 from pydicom import dcmread, compat
-from pydicom.data import get_testdata_files
+from pydicom.data import get_testdata_file
 from pydicom.dataelem import DataElement
 from pydicom.dataset import Dataset
 from pydicom.tag import Tag, BaseTag
@@ -15,7 +14,7 @@

 class TestPersonName(object):
     def test_json_pn_from_file(self):
-        with open(get_testdata_files("test_PN.json")[0]) as s:
+        with open(get_testdata_file("test_PN.json")) as s:
             ds = Dataset.from_json(s.read())
         assert isinstance(ds[0x00080090].value,
                           (PersonNameUnicode, PersonName3))
@@ -144,7 +143,7 @@ def test_from_json(self):
         assert 0x000910AF == ds[0x00091001].value
         assert [0x00100010, 0x00100020, 0x00100030] == ds[0x00091002].value

-    def test_invalid_json(self):
+    def test_invalid_value_in_json(self):
         ds_json = ('{"00091001": {"vr": "AT", "Value": ["000910AG"]}, '
                    '"00091002": {"vr": "AT", "Value": ["00100010"]}}')
         with pytest.warns(UserWarning, match='Invalid value "000910AG" for '
@@ -153,15 +152,24 @@ def test_invalid_json(self):
             assert ds[0x00091001].value is None
             assert 0x00100010 == ds[0x00091002].value

+    def test_invalid_tag_in_json(self):
+        ds_json = ('{"000910AG": {"vr": "AT", "Value": ["00091000"]}, '
+                   '"00091002": {"vr": "AT", "Value": ["00100010"]}}')
+        with pytest.raises(ValueError, match='Data element "000910AG" could '
+                                             'not be loaded from JSON:'):
+            ds = Dataset.from_json(ds_json)
+            assert ds[0x00091001].value is None
+            assert 0x00100010 == ds[0x00091002].value
+

 class TestDataSetToJson(object):
     def test_json_from_dicom_file(self):
-        ds1 = Dataset(dcmread(get_testdata_files("CT_small.dcm")[0]))
-        ds_json = ds1.to_json(bulk_data_threshold=100000)
+        ds1 = Dataset(dcmread(get_testdata_file("CT_small.dcm")))
+        ds_json = ds1.to_json()
         ds2 = Dataset.from_json(ds_json)
         assert ds1 == ds2

-        ds_json = ds1.to_json_dict(bulk_data_threshold=100000)
+        ds_json = ds1.to_json_dict()
         ds2 = Dataset.from_json(ds_json)
         assert ds1 == ds2

@@ -205,7 +213,7 @@ def test_roundtrip(self):
         ds.add_new(0x00091101, 'SH', 'Version2')
         ds.add_new(0x00091102, 'US', 2)

-        json_string = ds.to_json(bulk_data_threshold=100)
+        json_string = ds.to_json()
         json_model = json.loads(json_string)

         assert json_model['00080005']['Value'] == ['ISO_IR 100']
@@ -223,7 +231,7 @@ def test_roundtrip(self):
         ds2 = Dataset.from_json(json_model)
         assert ds == ds2

-        json_model2 = ds.to_json_dict(bulk_data_threshold=100)
+        json_model2 = ds.to_json_dict()
         if compat.in_py2:
             # in Python 2, the encoding of this is slightly different
             # (single vs double quotation marks)
@@ -274,7 +282,7 @@ def test_sort_order(self):

 class TestSequence(object):
     def test_nested_sequences(self):
-        test1_json = get_testdata_files("test1.json")[0]
+        test1_json = get_testdata_file("test1.json")
         with open(test1_json) as f:
             with pytest.warns(UserWarning,
                               match='no bulk data URI handler provided '):
@@ -288,7 +296,7 @@ class TestBinary(object):
     def test_inline_binary(self):
         ds = Dataset()
         ds.add_new(0x00091002, 'OB', b'BinaryContent')
-        ds_json = ds.to_json_dict(bulk_data_threshold=20)
+        ds_json = ds.to_json_dict()
         assert "00091002" in ds_json
         assert "QmluYXJ5Q29udGVudA==" == ds_json["00091002"]["InlineBinary"]
         ds1 = Dataset.from_json(ds_json)
""" # noqa: E501


@pytest.mark.asyncio
async def test_single_repo_pydicom__pydicom_1033(create_swe_test_data, run_swe_whole_process):
    swe_data = {
        'instance_id': 'pydicom__pydicom-1033',
        'repo': 'pydicom/pydicom',
        'base_commit': '701c1062bf66de2b29df68fa5540be6009943885',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_json.py::TestDataSetToJson::test_json_from_dicom_file", "pydicom/tests/test_json.py::TestDataSetToJson::test_roundtrip", "pydicom/tests/test_json.py::TestBinary::test_inline_binary"]' # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data["problem_statement"],
        codezone_id="695395448911204352",
        codezone_description=swe_data["problem_statement"],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data["problem_statement"],
        goal_detail=swe_data["problem_statement"],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data["test_patch"]
    )

    await run_swe_whole_process(test_data)
