import os
import asyncio
import json
from datasets import load_dataset
from functools import partial
from datetime import datetime
from heracles.agent_workspace.paas_sdk.utils import (
    get_playground_id,
    bind_playground_info,
    stop_playground,
    get_environments,
    create_codezone,
    fork_codezone,
    import_codezone_file,
)
from heracles.core.schema.test import MockSocketIOServer
from heracles.server.clacky.playground import Playground
from heracles.server.clacky.playground_channel import PlaygroundChannel
from heracles.server.clacky.playground_manager import PlaygroundManager
from heracles.core.logger import heracles_logger as logger

socketio_server = MockSocketIOServer()
playground_manager = PlaygroundManager()
soft_timeout = 120
hard_timeout = 0

dataset = load_dataset('princeton-nlp/SWE-bench_Lite')


async def setup_codezone():
    res = await get_environments()
    env_id = ''
    for d in res['data']:
        if d['name'].startswith('Python 3.11'):
            env_id = d['id']
            break

    res = await create_codezone(env_id)

    codezone_id = res['data']['id']
    res = await get_playground_id(codezone_id)
    playground_id = res['data']['id']
    res = {'env_id': env_id, 'codezone_id': codezone_id, 'playground_id': playground_id}
    return res


async def run_func_call_sequentially(cmds, playground):
    for cmd in cmds:
        await playground.func_call('agent_terminal_with_result', cmd, soft_timeout=soft_timeout, hard_timeout=hard_timeout)


async def setup_base_env(root_playground_id):
    playground = Playground(root_playground_id, socketio_server)
    playground_manager.add_playground(playground)
    playground_channel = PlaygroundChannel(f'sid-{root_playground_id}', socketio_server, playground_manager, root_playground_id)
    await playground_channel.start()
    await playground.wait_for_ok(reason='swe base setup')
    playground_info = await bind_playground_info(playground.playground_id)
    new_codezone_id = playground_info['data']['codeZoneId']
    logger.info(f'new_codezone_id: {new_codezone_id}, playground_id: {playground.playground_id}')
    cmds = [
        'sudo apt update',
        'sudo apt install -y wget git build-essential libffi-dev libtiff-dev python3 python3-pip python-is-python3 jq curl locales locales-all tzdata',  # noqa: E501
        'sudo rm -rf /var/lib/apt/lists/*',
        'cd ~',
        'wget "https://repo.anaconda.com/miniconda/Miniconda3-py311_23.11.0-2-Linux-x86_64.sh" -O miniconda.sh',
        'bash miniconda.sh -b -p ~/miniconda3',
        'echo PATH=~/miniconda3/bin:$PATH >> ~/.bashrc',
        'source ~/.bashrc',
        'conda init --all',
        'conda config --append channels conda-forge',
        'sudo adduser --disabled-password --gecos "dog" nonroot',
    ]
    try:
        await run_func_call_sequentially(cmds, playground)
    finally:
        await playground.disconnect_ide_server()
        await stop_playground(playground.playground_id)
    res = {'codezone_id': new_codezone_id, 'playground_id': playground.playground_id}
    return res


async def generate_codezone_for_single_instance(data, root_codezone_id):
    instance_id = data['instance_id']
    logger.info(f'create codezone of swe instance: {instance_id}')
    playground: Playground | None = None
    new_codezone_id = None
    try:
        setup_scripts_dir = f'llm_tests/swe_bench/sweb_lite_setup_scripts/{instance_id}'

        code_zone_response = await fork_codezone(root_codezone_id)
        new_codezone_id = code_zone_response['data']['id']
        response = await get_playground_id(new_codezone_id)
        playground_id = response['data']['id']
        playground = Playground(playground_id, socketio_server)
        playground_manager.add_playground(playground)
        playground_channel = PlaygroundChannel(f'sid-{playground_id}', socketio_server, playground_manager, playground_id)
        await playground_channel.start()
        if not playground:
            raise Exception('playground is None')
        await playground.wait_for_ok(reason='swe setup')
        playground_info = await bind_playground_info(playground.playground_id)
        new_codezone_id = playground_info['data']['codeZoneId']
        logger.info(f'new_codezone_id: {new_codezone_id}, playground_id: {playground.playground_id}')

        await playground.func_call(
            'agent_terminal_with_result', 'mkdir /home/<USER>/testbed', soft_timeout=soft_timeout, hard_timeout=hard_timeout
        )
        logger.info('start setup')
        setup_env_script_name = 'setup_env.sh'
        setup_env_script_path = os.path.join(setup_scripts_dir, setup_env_script_name)
        setup_env_script_content = open(setup_env_script_path).read()
        setup_env_script_content = setup_env_script_content.replace('/opt/miniconda3', '~/miniconda3')
        setup_env_script_content = setup_env_script_content.replace('/testbed', '/home/<USER>/testbed')
        await import_codezone_file(new_codezone_id, 'setup_env.sh', setup_env_script_content)
        await playground.func_call(
            'agent_terminal_with_result', 'mv setup_env.sh /home/<USER>/', soft_timeout=soft_timeout, hard_timeout=hard_timeout
        )

        setup_repo_script_name = 'setup_repo.sh'
        setup_repo_script_path = os.path.join(setup_scripts_dir, setup_repo_script_name)
        setup_repo_script_content = open(setup_repo_script_path).read()
        setup_repo_script_content = setup_repo_script_content.replace('/opt/miniconda3', '~/miniconda3')
        setup_repo_script_content = setup_repo_script_content.replace('/testbed', '/home/<USER>/testbed')
        await import_codezone_file(new_codezone_id, 'setup_repo.sh', setup_repo_script_content)
        await playground.func_call(
            'agent_terminal_with_result', 'mv setup_repo.sh /home/<USER>/', soft_timeout=soft_timeout, hard_timeout=hard_timeout
        )

        eval_script_name = 'eval.sh'
        eval_script_path = os.path.join(setup_scripts_dir, eval_script_name)
        eval_script_content = open(eval_script_path).read()
        eval_script_content = eval_script_content.replace('/opt/miniconda3', '~/miniconda3')
        eval_script_content = eval_script_content.replace('/testbed', '/home/<USER>/testbed')
        await import_codezone_file(new_codezone_id, 'eval.sh', eval_script_content)

        cmds = [
            'export QT_XCB_GL_INTEGRATION=none',
            'mv eval.sh /home/<USER>/',
            '/bin/bash /home/<USER>/setup_env.sh',
            'source ~/miniconda3/etc/profile.d/conda.sh && conda activate testbed',
            '/bin/bash /home/<USER>/setup_repo.sh',
            'rm -rf /home/<USER>/app',
            'mv /home/<USER>/testbed /home/<USER>/app',
            'cd /home/<USER>/app',
        ]
        await run_func_call_sequentially(cmds, playground)

        res = {
            instance_id: {
                'staging': {
                    'codezone_id': new_codezone_id,
                    'playground_id': playground.playground_id,
                    'environment_ver_id': '403710022024036354',
                    'environment_name': 'swe',
                    'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                }
            }
        }
        return res
    except Exception:
        import traceback

        logger.info(instance_id)
        logger.info(traceback.format_exc())
        if playground:
            playground_id = playground.playground_id
        else:
            playground_id = None
        res = {
            instance_id: {
                'staging': {
                    'codezone_id': new_codezone_id,
                    'playground_id': playground_id,
                    'environment_name': 'failed',
                    'failed_reason': traceback.format_exc(),
                }
            }
        }
        return res
    finally:
        if playground:
            await playground.disconnect_ide_server()
            await stop_playground(playground.playground_id)
        logger.info(f'done codezone of swe instance: {instance_id}')


def sync_generate_codezone_for_single_instance(data, root_codezone_id):
    return asyncio.run(generate_codezone_for_single_instance(data, root_codezone_id))


def run_setup_parallel(codezones, worker=1):
    from multiprocessing import Pool

    root_codezone_id = codezones['base_codezone_env']['staging']['codezone_id']
    missing_data = []
    for data in list(dataset['test']):
        if data['instance_id'] not in codezones:
            missing_data.append(data)
    with Pool(processes=worker) as pool:
        json_data = pool.map(partial(sync_generate_codezone_for_single_instance, root_codezone_id=root_codezone_id), missing_data)
    return json_data


if __name__ == '__main__':
    codezones = {}
    codezones_json_path = 'llm_tests/datasets/swe/codezones.json'
    if os.path.isfile(codezones_json_path):
        with open(codezones_json_path) as f:
            data = json.load(f)
            codezones = data
    if 'base_codezone_env' not in codezones:
        logger.info('base codezone env not found, need to setup all instance')
        codezones = {}
        env_dict = asyncio.run(setup_codezone())
        setup_env_dict = asyncio.run(setup_base_env(env_dict['playground_id']))
        codezones.update(
            {
                'base_codezone_env': {
                    'staging': {
                        'codezone_id': setup_env_dict['codezone_id'],
                        'playground_id': setup_env_dict['playground_id'],
                        'environment_ver_id': env_dict['env_id'],
                        'environment_name': 'swe base env',
                        'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    }
                }
            }
        )
    json_data = run_setup_parallel(codezones, worker=1)
    failed_setup = {}
    for res in json_data:
        key = list(res.keys())[0]
        if res[key]['staging']['environment_name'] == 'failed':
            failed_setup.update({key: res.pop(key)})
    codezones.update(json_data)
    with open('llm_tests/datasets/swe/codezones.json', 'w') as f:
        json.dump(codezones, f, indent=4)
    with open('failed_setup.json', 'w') as f:
        json.dump(failed_setup, f, indent=4)
