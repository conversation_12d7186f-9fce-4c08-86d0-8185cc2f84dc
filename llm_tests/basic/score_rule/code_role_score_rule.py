from typing import Literal, cast
from llm_tests.basic.score_rule import ScoreRule
from llm_tests.utils import score_result
from llm_tests.utils.schema import ScoreModel
from heracles.core.schema.task import ActionType
from heracles.agent_roles.code_role.utils import check_syntax, SUPPORTED_EXTENSIONS, SupportedExtension

class CodeRoleScoreRule(ScoreRule):
    weights = [1]

    def __init__(self, workspace, task_action):
        super().__init__(workspace)
        self.task_action = task_action

    async def rule_no_syntax_errors(self) -> ScoreModel:
        """
        规则: action 执行结果是否存在语法错误
        """
        if self.task_action.action == ActionType.RUN_COMMAND:
            return ScoreModel(value=1.0, comment="RUN_COMMAND无需判断")
        extension = cast(SupportedExtension, self.task_action.action_object.path.split('.')[-1].lower())
        if extension not in SUPPORTED_EXTENSIONS:
            return ScoreModel(value=1.0, comment="非代码文件，无需判断")
        new_file_content = await self.workspace.tools.read_file_content(self.task_action.action_object.path)
        line_count = new_file_content.count('\n')
        self.workspace.logger.warning(f"line of new_file_content: {line_count}")
        new_errors = await check_syntax(new_file_content, extension)
        if new_errors:
            self.errors.append(f"存在 {len(new_errors)} 个语法错误")
            return ScoreModel(value=0.0, comment=f"存在 {len(new_errors)} 个语法错误")
        return ScoreModel(value=1.0, comment="无语法错误")

    async def rule_no_formatting_errors(self) -> ScoreModel:
        """
        规则: action 执行结果是否满足需求，是否存在格式问题
        """
        if self.task_action.result == "success":
            return ScoreModel(value=1.0, comment="run_command 类型无需判断，且没有错误")

        if self.task_action.result == "No change made for file":
            return ScoreModel(value=0.0, comment="No change made for file")

        if '+```' in self.task_action.result:
            # 读取文件内容，防止原文内容存在```代码块格式
            file_content = await self.workspace.tools.read_file_content(self.task_action.action_object.path)
            if file_content.startswith('```'):
                return ScoreModel(value=0.0, comment="错误添加```代码块格式")

        if 'ORIGINAL_FILE_CONTENT' in self.task_action.result:
            return ScoreModel(value=0.0, comment="错误添加`ORIGINAL_FILE_CONTENT`标签")

        if 'FULL_FILE_CONTENT' in self.task_action.result:
            return ScoreModel(value=0.0, comment="错误添加`FULL_FILE_CONTENT`标签")
        return ScoreModel(value=1.0, comment="无格式错误")

    async def rule_match_requirements(self) -> ScoreModel:
        """
        规则: 是否满足原始需求
        """
        if not (self.task_action.action == ActionType.ADD_FILE or self.task_action.action == ActionType.MODIFY_FILE):
            return ScoreModel(value=1.0, comment="无需验证代码修改效果")
        expect = (
            f"{CODE_VALIDATION_PROMPT}\n"
            "## Requirements\n"
            f"{self.task_action.action_object.detailed_requirement}"
        )
        result = f"# file_path: {self.task_action.action_object.path}\n" + self.task_action.result
        score_model = await score_result(self.workspace, expect, result)
        if score_model.value <= 0.1:
            self.errors.append("不满足原始需求")
        return score_model

    def _get_rule_funcs(self, rule_set: Literal['all', 'syntax']):
        if rule_set == "all":
            rule_funcs = self._get_all_rules()
        elif rule_set == "syntax":
            rule_funcs = [self.rule_no_syntax_errors]
        else:
            raise ValueError(f"未知的规则集: {rule_set}")

        return rule_funcs

    def _actual_result_for_expectations(self):
        return self.task_action.result


CODE_VALIDATION_PROMPT = """
Your task is to review the code changes marked with '+' and '-' and literally assess whether the changes meet the Requirements;
Refrain from speculating on potential risks, robustness, code quality, capability reductions, or unintended consequences not explicitly stated in the Requirements.

Report only the most critical issues caused by CHANGED codes:
- Ignore any potential issues in the unchanged code lines or removed lines marked with '-';
- Severe formatting errors: Identify only major indentation issues or code block formatting errors that could directly impact functionality.
  - Count indentation and report issues if the new code lines are clearly inconsistent with the surrounding context and could lead to syntax errors or logical errors.
- Laziness: No placeholders like TODO or PASS in the new code changes marked with '+' in the 'DIFF_FILE_CONTENT', Do not report on removed placeholders or TODOs.

Ignore minor issues such as:
- Disregard all issues in lines marked with '-', including removed placeholders or TODOs.
- Missing/Extra blank lines or Small syntax changes
- Indentation or formatting issues that don't affect functionality
- Changes in attribute order in HTML, XML, or other markup languages
"""    # noqa: E501
