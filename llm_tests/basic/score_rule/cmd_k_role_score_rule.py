from typing import Literal, Callable
from llm_tests.basic.score_rule import ScoreRule
from llm_tests.utils.schema import ScoreModel

class CmdKRoleScoreRule(ScoreRule):
    weights = [1]

    def __init__(self, workspace, result, expectations=None):
        super().__init__(workspace)
        self.result = result
        self.expectations = expectations

    async def rule_no_code_block(self) -> ScoreModel:
        """
        规则: 是否满足所有期望要点
        """
        if '```' in self.result:
            return ScoreModel(value=0.0, comment="错误添加```代码块格式")
        return ScoreModel(value=1.0, comment="没有错误添加```代码块格式")

    def _get_rule_funcs(self, rule_set: Literal['all']) -> list[Callable]:
        if rule_set == "all":
            rule_funcs = self._get_all_rules()
        return rule_funcs

    def _actual_result_for_expectations(self):
        return self.result
