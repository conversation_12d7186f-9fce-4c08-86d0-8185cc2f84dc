from typing import Literal, Callable
from llm_tests.basic.score_rule import ScoreRule
from llm_tests.utils.schema import ScoreModel
from heracles.agent_roles.role_action import AgentRoleCreateSpecAction, AgentRoleCreateStepAction

class ChatRoleScoreRule(ScoreRule):
    weights = [1]

    def __init__(self, workspace, result, expectations=None):
        super().__init__(workspace)
        self.action_result = result
        self.expectations = expectations

    async def rule_create_spec_action(self) -> ScoreModel:
        """
        规则: 返回内容是'AgentRoleCreateSpecAction'类型
        """
        if isinstance(self.action_result, AgentRoleCreateSpecAction):
            score_model = ScoreModel(value=1.0, comment="创建了 Spec Action")
        else:
            score_model = ScoreModel(value=0.0, comment="没有创建 Spec Action")
            self.errors.append(f"{self.__doc__}，得分: {score_model.value}说明：{score_model.comment}")
        return score_model

    async def rule_create_step_action(self) -> ScoreModel:
        """
        规则: 返回内容是'AgentRoleCreateStepAction'类型
        """
        if isinstance(self.action_result, AgentRoleCreateStepAction):
            score_model = ScoreModel(value=1.0, comment="创建了 Step Action")
        else:
            score_model = ScoreModel(value=0.0, comment="没有创建 Step Action")
            self.errors.append(f"{self.__doc__}，得分: {score_model.value}说明：{score_model.comment}")
        return score_model

    def _get_rule_funcs(self, rule_set: Literal['all', 'task_request', 'step_request']) -> list[Callable]:
        if rule_set == "all":
            rule_funcs = self._get_all_rules()
        elif rule_set == "task_request":
            rule_funcs = [
                self.rule_create_spec_action,
            ]
        elif rule_set == "step_request":
            rule_funcs = [
                self.rule_create_step_action,
            ]
        return rule_funcs

    def _actual_result_for_expectations(self):
        if isinstance(self.action_result, AgentRoleCreateSpecAction):
            return f"goal: {self.action_result.goal}\ngoal_detail: {self.action_result.goal_detail}"
        if isinstance(self.action_result, AgentRoleCreateStepAction):
            return (
                f"goal: {self.action_result.goal}\n"
                f"plan_draft: {self.action_result.plan_draft}\n"
                f"references: {self.action_result.references}"
            )
        return self.action_result.result
