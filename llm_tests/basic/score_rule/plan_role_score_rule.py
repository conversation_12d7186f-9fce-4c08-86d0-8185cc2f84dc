from typing import Literal, Callable
from heracles.core.schema.task import ActionType,Task
from llm_tests.basic.score_rule import ScoreRule
from llm_tests.utils.schema import ScoreModel

class PlanRoleScoreRule(ScoreRule):
    weights = [1, 1, 1]

    def __init__(self, workspace, result=None, expectations=None):
        super().__init__(workspace)
        self.expectations = expectations or []
        self.result = result
        if isinstance(self.result, Task):
            self.expectations += [
                'steps 是对于整体任务的规划，彼此之间逻辑上独立且没有重复的部分',
                'steps 之间允许彼此依赖，存在依赖关系时，前后顺序应该合理，或者并不实际影响最终结果',
            ]

    async def rule_actions_path_should_not_duplicate(self) -> ScoreModel:
        "规则: actions 中的 path 不重复"

        file_path_modify_count_map: dict = {}
        for step in self.result.task_steps:
            for action in step.task_actions:
                if action.action in [ActionType.ADD_FILE, ActionType.MODIFY_FILE]:
                    if action.action_object.path not in file_path_modify_count_map:
                        file_path_modify_count_map[action.action_object.path] = 0
                    file_path_modify_count_map[action.action_object.path] += 1

        for path, count in file_path_modify_count_map.items():
            if count > 1:
                score_model = ScoreModel(value=0.0, comment=f'存在重复的 path：{path}')
                self.errors.append(f"{self.rule_actions_path_should_not_duplicate.__doc__}不通过，说明：{score_model.comment}")
        score_model = ScoreModel(value=1.0, comment=f'{self.rule_actions_path_should_not_duplicate.__doc__}')
        return score_model

    def _get_rule_funcs(self, rule_set: Literal['all', 'model_generation']) -> list[Callable]:
        if rule_set == "all":
            rule_funcs = self._get_all_rules()
        elif rule_set == "model_generation":
            rule_funcs = [
                self.rule_actions_path_should_not_duplicate
            ]
        return rule_funcs

    def _actual_result_for_expectations(self):
        if isinstance(self.result, Task):
            return self.result.pretty_print()
        return self.result
