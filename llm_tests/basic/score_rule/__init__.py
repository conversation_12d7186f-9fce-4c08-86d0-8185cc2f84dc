import pytest
import asyncio
from typing import Optional, List
from abc import ABC, abstractmethod
from llm_tests.utils import score_result
from llm_tests.utils.schema import ScoreModel, FileContentAssert, APIAssert

class ScoreRule(ABC):
    def __init__(self, workspace):
        self.errors = []
        self.scores = []
        self.weights = []
        self.expectations: Optional[List[str | FileContentAssert | APIAssert]] = None
        self.final_score = None
        self.workspace = workspace
        self.extra_info = {}

    @abstractmethod
    def _get_rule_funcs(self, rule_set):
        """
        根据规则集获取规则方法
        """
        pass

    @abstractmethod
    def _actual_result_for_expectations(self):
        """
        获取实际结果用于期望点评分
        """
        pass

    async def rule_match_expectations(self) -> ScoreModel:
        """
        规则: 是否满足所有期望要点
        """
        if self.expectations is None:
            return ScoreModel(value=1.0, comment="没有指定期望要点")
        expectations = ''
        for expectation in self.expectations:
            if isinstance(expectation, str):
                expectations += "- " + expectation + "\n"
            elif isinstance(expectation, FileContentAssert):
                try:
                    await expectation.assert_file(self.workspace)
                except AssertionError as e:
                    self.errors.append(str(e))
            elif isinstance(expectation, APIAssert):
                try:
                    await expectation.assert_response(self.workspace)
                except AssertionError as e:
                    self.errors.append(str(e))
        if expectations != '':
            score_model = await score_result(self.workspace, expectations, self._actual_result_for_expectations())
            if score_model.value <= 0.8:
                self.errors.append(f"无法通过{self.rule_match_expectations.__doc__}得分: {score_model.value}说明：{score_model.comment}")  # noqa
        else:
            if self.errors:
                score_model = ScoreModel(value=0.0, comment=f"Assertion Failed: {self.errors}")
            else:
                score_model = ScoreModel(value=1.0, comment="所有期望断言均通过")
        return score_model

    def calculate_weighted_score(self) -> ScoreModel:
        """
        计算加权平均分
        """
        if len(self.weights) != len(self.scores):
            self.weights = [1] * len(self.scores)

        weighted_sum = sum(score.value * weight for score, weight in zip(self.scores, self.weights))
        total_weight = sum(self.weights)
        comment = "评分说明:\n" + "\n- ".join(score.comment for score in self.scores)

        score_value = weighted_sum / total_weight if total_weight != 0 else 0
        score_value = round(score_value * 100) / 100  # 四舍五入到小数点后两位
        return ScoreModel(value=score_value, comment=comment)

    async def execute_rules_and_score(self, rule_set: Optional[str] = None) -> ScoreModel:
        """
        执行所有规则并计算加权平均分
        """
        rule_funcs = []
        if rule_set is not None:
            rule_funcs = self._get_rule_funcs(rule_set)

        if self.expectations is not None and self.rule_match_expectations not in rule_funcs:
            self.workspace.logger.info(f"增加规则：{self.rule_match_expectations.__doc__}")
            rule_funcs.append(self.rule_match_expectations)
        if self.expectations is None and self.rule_match_expectations in rule_funcs:
            rule_funcs.remove(self.rule_match_expectations)

        final_score = await self._score_rules(rule_funcs)
        return final_score

    async def _score_rules(self, rule_funcs: list) -> ScoreModel:
        for rule_func in rule_funcs:
            self.workspace.logger.info(f"执行规则: {rule_func.__doc__}")
            score = await rule_func()
            self.scores.append(score)
        self.final_score = self.calculate_weighted_score()
        self.print_results_summary()
        if self.errors:
            pytest.fail(f"{self.__class__.__name__} 规则不通过({self.workspace.playground.playground_id})：" + "\n".join(self.errors))
        await asyncio.sleep(2)

        return self.final_score

    def print_results_summary(self):
        self.workspace.logger.info(f"\n\n{self.__class__.__name__} 规则执行结果:\n")
        for i, score in enumerate(self.scores):
            color = "\033[92m" if score.value > 0.5 else "\033[91m"  # 绿色表示高分，红色表示低分
            self.workspace.logger.info(f"{color}评分要点 {i + 1}:\n{score.comment}\n分数：{score.value}\033[0m\n")
        if self.final_score:
            self.workspace.logger.warning(f"\n最终评分: {self.final_score.value}")

        if self.errors:
            self.workspace.logger.error("\n".join(self.errors))
            pytest.fail(f"{self.__class__.__name__} 规则不通过({self.workspace.playground.playground_id})：" + "\n".join(self.errors))

    def _get_all_rules(self):
        """
        获取所有规则方法
        """
        return [getattr(self, method) for method in dir(self) if method.startswith('rule_') and callable(getattr(self, method))]
