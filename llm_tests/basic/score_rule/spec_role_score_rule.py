from typing import Literal
from heracles.core.schema.spec import Spec
from llm_tests.basic.score_rule import ScoreRule
from llm_tests.utils.schema import ScoreModel

class SpecRoleScoreRule(ScoreRule):
    weights = [1, 1, 1]

    def __init__(self, workspace, result=None, expectations=None):
        super().__init__(workspace)
        if result is None:
            raise ValueError("result 不能为空")
        self.result = result
        self.expectations = expectations or []
        if isinstance(self.result, Spec):
            self.expectations += [
                "current_list 字段的内容是对项目的现状(现有功能或文件状态的说明)进行描述"
            ]

    async def rule_proposed_list_is_not_empty(self) -> ScoreModel:
        """
        规则: 判断 proposed_list 的值不为空
        """
        if not self.result.proposed_list:
            score_model = ScoreModel(value=0.0, comment="proposed_list 的值为空")
        else:
            score_model = ScoreModel(value=1.0, comment="proposed_list 的值不为空")
        if score_model.value <= 0.1:
            self.errors.append(f"规则: proposed_list 的值不为空 ，得分: {score_model.value}说明：{score_model.comment}")
        return score_model

    async def rule_task_scale_return(self) -> ScoreModel:
        "规则: 判断 `clarified`、`task_scale`的值是否正确"

        proposed_filechange_list = self.result.proposed_filechange_list

        task_scale_mapping = {
            "micro": len(proposed_filechange_list) <= 3,
            "small": len(proposed_filechange_list) <= 10,
            "medium": len(proposed_filechange_list) <= 20
        }

        for scale, condition in task_scale_mapping.items():
            if condition:
                score_model = ScoreModel(value=1.0, comment=f"规则检查通过: task_scale = {scale}")
                break
        else:
            score_model = ScoreModel(value=0.0, comment="规则检查未通过: task_scale 不符合预期")
            self.errors.append(f"规则: `clarified`、`task_scale`的值是否正确返回，得分: {score_model.value}说明：{score_model.comment}")
        return score_model

    def _get_rule_funcs(self, rule_set: Literal['all']):
        if rule_set == "all":
            rule_funcs = self._get_all_rules()
        else:
            raise ValueError(f"未知的规则集: {rule_set}")

        return rule_funcs

    def _actual_result_for_expectations(self):
        if isinstance(self.result, Spec):
            return self.result.dict()
        return self.result
