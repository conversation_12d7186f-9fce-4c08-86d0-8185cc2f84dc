from typing import Literal, Callable
from llm_tests.basic.score_rule import ScoreRule
from llm_tests.utils import score_result
from llm_tests.utils.schema import ScoreModel

class SummaryRoleScoreRule(ScoreRule):
    weights = [1]

    def __init__(self, workspace, result, expectations=None):
        super().__init__(workspace)
        self.result = result
        self.expectations = expectations

    async def rule_match_expectations(self) -> ScoreModel:
        """
        规则: 是否满足所有期望要点
        """
        if self.expectations is None:
            return ScoreModel(value=0.0, comment="没有指定期望要点")
        expectations = "".join("- " + str(expectation) + "\n" for expectation in self.expectations)
        score_model = await score_result(self.workspace, expectations, self.result)
        if score_model.value <= 0.1:
            self.errors.append(f"{self.rule_match_expectations.__doc__}得分: {score_model.value}说明：{score_model.comment}")
        return score_model

    def _get_rule_funcs(self, rule_set: Literal['all', 'task_request']) -> list[Callable]:
        if rule_set == "all":
            rule_funcs = self._get_all_rules()
        return rule_funcs

    def _actual_result_for_expectations(self):
        return self.result
