from typing import Literal, Callable
from llm_tests.basic.score_rule import ScoreRule

class CheckRoleScoreRule(ScoreRule):
    weights = [1]

    def __init__(self, workspace, result, expectations=None):
        super().__init__(workspace)
        self.result = result
        self.expectations = expectations or []

    def _get_rule_funcs(self, rule_set: Literal['all']) -> list[Callable]:
        if rule_set == "all":
            rule_funcs = self._get_all_rules()
        return rule_funcs

    def _actual_result_for_expectations(self):
        return self.result
