import pytest
import json

from heracles.agent_roles.plan_role import <PERSON><PERSON><PERSON>
from heracles.core.schema.task import Task
from heracles.core.schema.task import ActionType, TaskStep, TaskAction, FileActionObject
from heracles.core.schema import FileSnippet


@pytest.mark.asyncio  # 2/10 0/10
async def test_plan_role_for_repeat_read_file_when_make_additional_step(create_role, create_test_data, mocker):
    test_data = create_test_data(
        title='PlanRole: fix repeat file - make_additional_step',
        description='Test if PlanRole avoids redundant file reads when File Snippets are provided',
        project_state_id='fadeOut-game-main',
        goal='Enhance game win feedback with sound and visual elements',
        goal_detail=(
            'Implement a more engaging win condition by adding sound effects and visual feedback. '
            'Replace alert dialogs with an integrated DOM element with css class "winner-display" to display the winner, ' # noqa
            'and add sound effects for a better user experience.'
        ),
        proposed_list=[
            'Add a winner display element to index.html for visual feedback',
            'Implement sound effects and winner display logic in script.js',
        ],
    )

    make_additional_step_param = {
        'goal': 'Modify script.js to enhance game win feedback with sound and visual elements',
        'plan_draft': '1. add sound effects to the game\n2. add winner display logic',
        'references': ['file://script.js:1-116', 'file://index.html:1-44'],
    }

    task = Task(
        title=test_data.title,
        description=test_data.goal_detail,
    )
    task_step = TaskStep(
        title=test_data.proposed_list[0],
    )
    task_action = TaskAction(
        title='Implement winner display element in index.html',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='index.html',
            detailed_requirement='Add a DOM element for displaying the winner message',
            references=[
                'Add a DOM element for displaying the winner message',
            ],
        ),
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    plan_role, context = await create_role(test_data, PlanRole)

    context.workspace.set_task(task)
    plan_role.workspace.task = task

    mock_function_call = mocker.patch.object(plan_role, '_function_call', wraps=plan_role._function_call)
    await plan_role.make_additional_step(make_additional_step_param)

    for call in mock_function_call.call_args_list:
        fn = call.args[0].function.name
        if fn == 'read_file':
            arg0 = json.loads(call.args[0].function.arguments).get('path')
            assert arg0 not in ['script.js', 'index.html'], f'Repeat read file: {arg0}'


@pytest.mark.asyncio  # 0/10 0/10
async def test_plan_role_for_repeat_read_file_when_generate_task_plan(create_role, create_test_data, mocker):
    test_data = create_test_data(
        title='PlanRole: fix repeat file - generate_task_plan',
        description='Test if PlanRole avoids redundant file reads when File Snippets are provided',
        project_state_id='fadeOut-game-main',
        goal='Enhance game win feedback with sound and visual elements',
        goal_detail=(
            'Implement a more engaging win condition by adding sound effects and visual feedback. '
            'Replace alert dialogs with an integrated DOM element to display the winner, '
            'and add sound effects for a better user experience.'
        ),
        proposed_list=[
            'Add a winner display element to index.html for visual feedback',
            'Implement sound effects and winner display logic in script.js',
        ],
    )

    plan_role, context = await create_role(test_data, PlanRole)

    related_file_snippets: list[FileSnippet] = [
        FileSnippet(
            path=fp.replace('file://', ''),
            content=await context.workspace.tools.read_file(fp.replace('file://', ''), should_read_entire_file=True),
            row_start=-1,
            row_end=-1,
        )
        for fp in ['file://script.js', 'file://index.html']
    ]

    think_result = await plan_role._think_about_plan(
        goal=test_data.goal,
        goal_detail=test_data.goal_detail,
        proposed_list=test_data.proposed_list,
        related_snippets=related_file_snippets,
    )

    mock_function_call = mocker.patch.object(plan_role, '_function_call', wraps=plan_role._function_call)

    await plan_role._generate_task_plan(
        goal=test_data.goal,
        goal_detail=test_data.goal_detail,
        proposed_list=test_data.proposed_list,
        related_snippets=related_file_snippets,
        think_result=think_result,
    )

    for call in mock_function_call.call_args_list:
        fn = call.args[0].function.name
        if fn == 'read_file':
            arg0 = json.loads(call.args[0].function.arguments).get('path')
            assert arg0 not in ['script.js', 'index.html'], f'Repeat read file: {arg0}'
