import pytest

from heracles.agent_roles.plan_role import Plan<PERSON><PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, CommandActionObject, CommandLifetimeType

@pytest.mark.asyncio
async def test_plan_role_for_action_path_when_add_step(create_role, create_test_data, mocker):
    test_data = create_test_data(
        title='PlanRole: 增加step时正确认知path',
        description='Test if PlanR<PERSON> correctly understands the path of the action is always relative to the project root',
        project_state_id='blank-project-main',
        goal='创建一个hacker news落地首页',
        goal_detail='创建一个hacker news落地首页,要求美观',
    )

    plan_role, context = await create_role(test_data, PlanRole)
    task = Task(
        title="创建一个hacker news落地首页",
        description="创建一个hacker news落地首页,要求美观",
    )
    task_step = TaskStep(
        title="初始化项目"
    )
    task_action = TaskAction(
        title='初始化项目',
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command='npx create-next-app hacker-news-homepage --ts --eslint --use-npm --tailwind --app --src-dir --import-alias "@/*" --skip-git', # noqa
            lifetime=CommandLifetimeType.SHORT,
        )
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    plan_role.workspace.task = task

    make_additional_step_param = {
        'goal': '增加一个header组件',
        'plan_draft': '创建 Header 组件，使用 NavigationMenu 和 Button 组件。包含 HackerNews 品牌文字、简单的导航链接 (如 "首页") 和主题切换按钮，使用 useTheme hook 实现主题切换功能。',  # noqa
        'references': [],
    }

    await plan_role.make_additional_step(make_additional_step_param)






@pytest.mark.asyncio
async def test_plan_role_for_action_path_when_make_plan(create_role, create_test_data, mocker):
    test_data = create_test_data(
        title='PlanRole: make_plan时正确认知path',
        description='Test if PlanRole correctly understands the path of the action is always relative to the project root',
        project_state_id='blank-project-main',
        goal='开发一个介绍 HackerNews 的网站首页',
        goal_detail="""开发一个漂亮美观的介绍 HackerNews 的网站首页，首页需要包含 HackerNews 的介绍、新闻列表等内容, 使用nextjs, shadcn, tailwindcss技术栈.
初始化一个新的 Next.js 项目并配置 TypeScript。
配置 Tailwind CSS 用于样式设计。
安装并配置 shadcn/ui 组件库。
创建首页组件（app/page.tsx）。
在首页中设计并实现 HackerNews 介绍部分。
在首页中设计并实现一个用于显示 HackerNews 新闻列表的部分。
实现数据抓取逻辑以获取 HackerNews 文章（可能使用公开 API）。
使用 Tailwind CSS 和 shadcn/ui 美化首页，使其漂亮美观。""",  # noqa
        proposed_list=[],
    )


    plan_role, context = await create_role(test_data, PlanRole)

    await plan_role._generate_task_plan(
        goal=test_data.goal,
        goal_detail=test_data.goal_detail,
        proposed_list=[],
        related_snippets=[],
        think_result= [
            'Create a Next.js project with TypeScript using the command npx create-next-app@latest hackernews-intro --typescript with Tailwind CSS configured during setup', # noqa
            'Install shadcn/ui using npx shadcn@latest init and configure it for the project with necessary components',
            'Create a modern, responsive layout structure with header, hero section, and content areas in app/page.tsx for the HackerNews introduction', # noqa
            'Implement the website header with navigation links and HackerNews branding using shadcn/ui components like NavigationMenu and Button', # noqa
            'Develop a hero section highlighting what HackerNews is, its history, and its importance in the tech community', # noqa
            'Create a section explaining key features and sections of HackerNews (top stories, new stories, ask, show, jobs)', # noqa
            'Implement a news list component to display recent HackerNews articles using the official Hacker News API (https://github.com/HackerNews/API)', # noqa
            'Fetch data from HackerNews API endpoints: /v0/topstories.json to get IDs and then /v0/item/{id}.json for individual stories', # noqa
            'Use React hooks for data fetching with proper loading states and error handling',
            'Apply Tailwind CSS styling with shadcn/ui components for a cohesive and modern design',
            'Create responsive design that works well on mobile, tablet and desktop screens',
            'Implement a dark mode toggle using shadcn/ui theming capabilities',
            'Set up proper TypeScript interfaces for API responses and component props',
            'Create a .1024 file with run_command: "npm run dev" and dependency_command: "npm install" for project execution', # noqa
            'Add a proper .gitignore file to exclude node_modules, .next, and other unnecessary files',
        ],
    )
