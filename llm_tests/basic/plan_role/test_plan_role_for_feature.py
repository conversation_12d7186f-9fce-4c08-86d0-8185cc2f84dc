import pytest
from llm_tests.basic.score_rule.plan_role_score_rule import PlanRoleScoreRule
from heracles.agent_roles.plan_role import PlanRole


@pytest.mark.asyncio
async def test_plan_role_html_play_music(create_role, create_test_data):
    test_data = create_test_data(
        title='PlanRole: fadeout-game',
        description='HTML环境,剪头石头布小游戏',
        project_state_id='fadeOut-game-main',
        goal='在胜利（玩家赢）时，添加声音效果',
        goal_detail='玩家赢时，播放 assset 目录下提供的 music.mp3 音乐',
        proposed_list=["Modify 'index.html' to include an audio element for the victory sound.",
        "Update 'script.js' to play the victory sound when the player wins."],
        expectations=[
            "1. Modified 'index.html' to include an audio element for the victory sound.",
            "2. Updated 'script.js' to play the victory sound when the player wins."
        ]
    )

    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(test_data.goal, test_data.goal_detail, test_data.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_go_blog_upload_image_feature(create_role, create_test_data):

    test_data = create_test_data(
        title="PlanRole: go-blog/实现图片上传功能",
        description="在 Go 项目 go-blog 中实现图片上传功能",
        project_state_id='go-blog-main',
        goal="实现图片上传功能",
        goal_detail="""目前的图片上传功能没有实现，应该在处理文章创建时处理图片上传，并将图片保存到服务器""",
        expectations=[
            "Added file upload support in the article creation form in `templates/new_article.html`.",
            "Updated the article handler in `handlers.article.go` to handle file uploads.",
            "Saved uploaded files to the `public/uploads` directory.",
            "Updated routing in `routes.go` to include new file upload handling."
        ]
    )

    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(test_data.goal, test_data.goal_detail, test_data.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score('model_generation')
    context.score_model = final_score
    return final_score
