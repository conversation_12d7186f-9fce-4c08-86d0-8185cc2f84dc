import pytest

from heracles.agent_roles.plan_role import <PERSON><PERSON><PERSON>
from heracles.core.schema.task import Task
from heracles.core.schema.task import ActionType, TaskStep, TaskAction, FileActionObject

@pytest.mark.asyncio
async def test_plan_role_for_bind_middleware_to_environment(create_role, create_test_data, mocker):
    test_data = create_test_data(
        title='PlanRole: bind middleware to environment',
        description='Test if PlanRole can bind middleware to environment',
        project_state_id='fadeOut-game-main',
        goal='实现使用MySQL数据库存储数据',
        goal_detail=(
            'Implement a more engaging win condition by adding sound effects and visual feedback. '
            'Replace alert dialogs with an integrated DOM element with css class "winner-display" to display the winner, ' # noqa
            'and add sound effects for a better user experience.'
            'use mysql 5.7 to store data'
        ),
        proposed_list=[
            'Add a winner display element to index.html for visual feedback',
            'Implement sound effects and winner display logic in script.js',
            'bind middleware to environment',
        ],
    )

    make_additional_step_param = {
        'goal': 'modify script.js to use mysql 5.7 to store data',
        'plan_draft': '1. bind middleware to environment\n2. modify script.js to implement mysql 5.7 to store data',
        'references': [],
    }

    task = Task(
        title=test_data.title,
        description=test_data.goal_detail,
    )
    task_step = TaskStep(
        title=test_data.proposed_list[0],
    )
    task_action1 = TaskAction(
        title='Implement winner display element in index.html',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='index.html',
            detailed_requirement='Add a DOM element for displaying the winner message',
            references=[
                'Add a DOM element for displaying the winner message',
            ],
        ),
    )
    task_action2 = TaskAction(
        title='Modify script.js to enhance game win feedback with sound and visual elements',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='config/script.js',
            detailed_requirement='Replace alert dialogs with an integrated DOM element with css class "winner-display" to display the winner, and add sound effects for a better user experience.', # noqa: E501
            references=[
                'Replace alert dialogs with an integrated DOM element with css class "winner-display" to display the winner, and add sound effects for a better user experience.', # noqa: E501
            ],
        ),
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action1)
    task_step.add_task_action(task_action2)

    plan_role, context = await create_role(test_data, PlanRole)

    context.workspace.set_task(task)
    plan_role.workspace.task = task

    await plan_role.make_additional_step(make_additional_step_param)
