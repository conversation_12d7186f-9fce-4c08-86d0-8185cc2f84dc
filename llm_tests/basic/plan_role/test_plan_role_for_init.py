import pytest
from llm_tests.basic.score_rule.plan_role_score_rule import PlanRoleScoreRule
from heracles.agent_roles.plan_role import PlanRole
from heracles.agent_roles.spec_role import SpecRole

@pytest.mark.asyncio
async def test_plan_role_html_initialize_environment(create_role, create_test_data):
    test_data = create_test_data(
        title='PlanRole: fadeout-game/初始化环境',
        description='HTML环境,剪头石头布小游戏',
        project_state_id='fadeOut-game-main',
        goal='Initialize the Development Environment',
        expectations=[
            "修改 '.1024' 文件: 使用 browser-sync 来运行项目。没有要求设置 dependency_command 的描述",
            "规划了 Action 来修改 '.gitignore' 文件",
            "不应存在 npm install 等 action='run_command' 的 task_actions (无需考虑潜在风险问题)"
        ]
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)
    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(spec.goal, spec.goal_detail, spec.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score


@pytest.mark.asyncio
async def test_plan_role_gin_initialize_environment(create_role, create_test_data):
    test_data = create_test_data(
        title="PlanRole: gin-gorm-todo-app/初始化环境",
        description="为 Go 项目 gin-gorm-todo-app 初始化环境",
        project_state_id="gin-gorm-todo-app-main",
        goal='Initialize the Development Environment',
        expectations=[
            "规划了 Action 运行 go get, go mod tidy 或 go mod download 安装项目依赖",
            "规划了 Action 来修改 '.1024' 文件: 使用 go run main.go 运行项目，使用 go get, go mod tidy 或 go mod download 安装依赖",
            "规划了 Action 来修改 '.gitignore' 文件"
        ]
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)
    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(spec.goal, spec.goal_detail, spec.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_plan_role_next_initialize_environment(create_role, create_test_data):
    test_data = create_test_data(
        title="PlanRole: Nextra: Docs Starter Kit/初始化环境",
        description="为 Next.js 项目 Nextra 初始化开发环境",
        project_state_id='nextra-docs-template-main',
        goal='Initialize the Development Environment',
        expectations=[
            "没有规划额外 Action 去安装 pnpm 本身(因为 pnpm 已经存在)"
            "规划了 Action 使用 pnpm 安装项目依赖，而不是 npm 或 yarn 等其他包管理器，且对应的 lifetime 是 long 类型",
            "修改 '.1024' 文件: 说明了 run_command 需要使用 pnpm 命令（而不是 npm 或 yarn 等其他），使用 pnpm install 安装依赖",
            "修改 '.gitignore' 文件",
        ]
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)
    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(spec.goal, spec.goal_detail, spec.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_plan_role_express_blog_initialize_environment(create_role, create_test_data):
    test_data = create_test_data(
        title="完整项目: express_blog/初始化环境",
        description="为 Express 项目 express_blog 初始化开发环境",
        project_state_id="express-blog-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            "规划了 Action 通过 npm 先安装 yarn",
            "规划了 Action 运行 yarn install",
        ],
    )
    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)
    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(spec.goal, spec.goal_detail, spec.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_plan_role_ragbot_initialize_environment(create_role, create_test_data):
    test_data = create_test_data(
        title="PlanRole: Next.js RAGBot Starter/初始化环境",
        description="为 Next.js 项目 RAGBot Starter 初始化开发环境",
        project_state_id="ragbot-starter-main",
        goal='Initialize the Development Environment',
        expectations=[
            "正确使用了 npm 安装项目依赖，而不是 pnpm 或 yarn 等其他包管理器，且对应的 lifetime 是 long 类型",
            "修改 '.1024' 文件: 说明了 run_command 需要使用 npm 命令（而不是 pnpm 或 yarn 等其他），使用 npm install 安装依赖",
            "修改 '.gitignore' 文件",
        ]
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)
    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(spec.goal, spec.goal_detail, spec.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_plan_role_rails_wblog_init(create_role, create_test_data):
    test_data = create_test_data(
        title="PlanRole: rails wblog/init",
        project_state_id="wblog-main",
        goal="Initialize the Development Environment",
        middlewares=["postgres"],
        expectations=[
            "修改 '.1024' 文件: 使用 rails s 或 bin/dev 运行项目，使用 bundle install 安装依赖",
            "规划了 Action 来修改 '.gitignore' 文件",
            "规划了 Action 运行 bundle install",
            "规划了 Action 运行 npm install -g yarn",
            "规划了 Action 运行 yarn install",
            "规划了 Action 对 `config/application.yml.example` 和 `config/database.yml.example` 文件进行复制",
            "规划了 Action 对 `config/database.yml` 文件进行修改",
        ]
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)
    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(spec.goal, spec.goal_detail, spec.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score('all')
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_plan_role_internal_chat_init(create_role, create_test_data):
    test_data = create_test_data(
        title="Plan Role: internal chat/init",
        project_state_id="internal-chat-main",
        goal="Initialize the Development Environment",
        expectations=[
            "修改 '.1024' 文件: 使用 cd server && npm run start 运行项目",
            "规划了 Action 来修改 '.gitignore' 文件",
            "规划了 Action 运行 cd server && npm install 安装依赖"
        ]
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)
    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(spec.goal, spec.goal_detail, spec.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score('all')
    context.score_model = final_score
    return final_score
