import pytest
from llm_tests.basic.score_rule.plan_role_score_rule import PlanRoleScoreRule
from heracles.agent_roles.plan_role import PlanRole


@pytest.mark.asyncio
async def test_plan_role_go_blog_translate_readme(create_role, create_test_data):
    test_data = create_test_data(
        title="PlanRole: go-blog/增加 README 中文版本",
        description="在 Go 项目 go-blog 中增加 README 中文版本",
        project_state_id='go-blog-main',
        goal="增加 README 中文版本",
        goal_detail="""创建新文件，并将 README.md 文件内容翻译到 README.zh-CN.md 文件。""",
        proposed_list=[
            "创建新文件并翻译 README.md 文件内容到 README.zh-CN.md 文件。"
        ],
        expectations=[
            "`add_file` 类型的 action 创建了 'README.zh-CN.md' 文件",
            "对应 action 中的 references 包含 'file://README.md'",
        ]
    )

    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(test_data.goal, test_data.goal_detail, test_data.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score("model_generation")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_plan_role_html_rename_files(create_role, create_test_data):
    test_data = create_test_data(
        title='PlanRole: fadeout-game/重命名文件',
        description='HTML环境,剪头石头布小游戏',
        project_state_id='fadeOut-game-main',
        goal='Rename files in assets to English',
        goal_detail="make sure all files in assets are in English",    # noqa: E501
        proposed_list=[
            'rename all files with chinese names to english',
        ],
        expectations=[
            "使用了 `move_file` Action 来重命名文件",
            "剪刀、石头、布三个 png 文件都进行了修改"
        ]
    )

    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(test_data.goal, test_data.goal_detail, test_data.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_plan_role_go_blog_refactor(create_role, create_test_data):
    test_data = create_test_data(
        title="PlanRole: go-blog/优化代码结构",
        description="在 Go 项目 go-blog 中优化代码结构",
        project_state_id='go-blog-main',
        goal="优化代码结构",
        goal_detail="""将代码分成更小的模块和包，以提高可维护性。例如，可以将用户和文章的处理逻辑分别放在不同的包中""",
        proposed_list=[
            "创建新的包结构以分别处理用户和文章的逻辑。",
            "将handlers.user.go、models.user.go的代码迁移到新的用户包。",
            "将handlers.article.go、models.article.go的代码迁移到新的文章包。",
            "更新routes.go文件以使用新的包路径。",
            "修改main.go文件以支持新的模块化包结构。"
        ],
        expectations=[
            "规划了 Action 来修改 'routes.go' 文件",
            "规划了 Action 来修改 'main.go' 文件",
            "规划了 Action 来迁移文件，并修改了包声明",
        ]
    )

    plan_role, context = await create_role(test_data, PlanRole)
    task = await plan_role.run(test_data.goal, test_data.goal_detail, test_data.proposed_list)
    context.workspace.set_task(task)

    final_score = await PlanRoleScoreRule(plan_role.workspace, task, test_data.expectations).execute_rules_and_score("model_generation")
    context.score_model = final_score
    return final_score
