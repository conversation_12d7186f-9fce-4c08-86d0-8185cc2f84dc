import pytest
import asyncio

@pytest.mark.asyncio
async def test_check_role_check_error(create_workspace, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='CheckRole:check_error测试',
        description='',
        is_web_service=True,
        project_state_id='full-stack-todolist-with-error'
    )
    workspace = await create_workspace(test_basic_data.codezone_id)
    check_role = workspace.playground.agent_controller.check_role

    logger = workspace.logger

    await workspace.smart_detect.set_status('monitoring_errors')
    await check_role.run_and_check_project()
    # 收集并分析错误信息
    await asyncio.sleep(10)

    # if test_basic_data.is_web_service:
    #     await wait_for(clacky_ports_detected, timeout=30, interval=1)
    #     local_url = await workspace.tools.local_url()
    #     logger.warning(f'-> clacky ports detected (waited {wait_seconds} seconds), will curl {local_url}')
    #     await workspace.tools.run_cmd(f'curl {local_url}')

    errors = workspace.smart_detect.errors
    if len(errors) > 0:
        for error in errors:
            logger.error(f'-> step: <PERSON><PERSON> failed with error found: \nref_id: {error.ref_id}\n\n{error.content}')
            pytest.fail('RUN failed with error found')
    else:
        logger.warning('-> clacky terminal log analysis finished, no error found')
