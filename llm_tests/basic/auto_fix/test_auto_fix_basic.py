import pytest
import re
from heracles.core.schema.task import Task
from llm_tests.basic.score_rule.smoke_test_score_rule import SmokeTestScoreRule

@pytest.mark.asyncio
async def test_autofix_no_error(create_workspace, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        is_web_service=True,
        expectations=[
            "1024文件有run_command",
            "项目能够运行",
        ],
    )
    workspace = await create_workspace(test_data.codezone_id)
    await workspace.smart_detect.set_status('monitoring_errors')
    task = Task(title='nextjs-fastapi初始化环境', description='')
    workspace.set_task(task)
    controller = workspace.playground.agent_controller
    controller.task_state.set_state("working")
    await controller.check_and_fix_errors()
    smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)
    score_model = await smoke_test_score_rule.execute_rules_and_score('all')
    return score_model


@pytest.mark.asyncio
async def test_autofix_no_1024(create_workspace, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            "1024文件有run_command",
            "项目能够运行",
        ],
        is_web_service=True
    )
    workspace = await create_workspace(test_data.codezone_id)
    await workspace.smart_detect.set_status('monitoring_errors')
    task = Task(title='nextjs-fastapi初始化环境', description='')
    workspace.set_task(task)
    controller = workspace.playground.agent_controller
    controller.task_state.set_state("working")

    await workspace.tools.delete_file('.1024')

    await controller.check_and_fix_errors()
    smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)
    score_model = await smoke_test_score_rule.execute_rules_and_score('all')
    return score_model

@pytest.mark.asyncio
async def test_autofix_wrong_1024(create_workspace, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        is_web_service=True,
        expectations=[
            "1024文件有run_command",
            "项目能够运行",
        ],
    )
    workspace = await create_workspace(test_data.codezone_id)
    await workspace.smart_detect.set_status('monitoring_errors')
    task = Task(title='nextjs-fastapi初始化环境', description='')
    workspace.set_task(task)
    controller = workspace.playground.agent_controller
    controller.task_state.set_state("working")

    content = await workspace.tools.read_file_content('.1024')
    modified_content = re.sub(r'run_command:.*?(?=\n|$)', "run_command: './yyyy'", content)
    await workspace.tools.write_file('.1024', modified_content)

    await controller.check_and_fix_errors()
    smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)
    score_model = await smoke_test_score_rule.execute_rules_and_score('all')
    return score_model

@pytest.mark.asyncio
async def test_autofix_missing_import(create_workspace, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        is_web_service=True,
        expectations=[
            "./app/page.tsx有正确的import"
            "项目能够运行",
        ],
    )
    workspace = await create_workspace(test_data.codezone_id)
    await workspace.smart_detect.set_status('monitoring_errors')
    task = Task(title='nextjs-fastapi初始化环境', description='')
    workspace.set_task(task)
    controller = workspace.playground.agent_controller
    controller.task_state.set_state("working")

    content = await workspace.tools.read_file_content('./app/page.tsx')
    modified_content = re.sub(r'import.*?(?=\n|$)', "", content)
    await workspace.tools.write_file('./app/page.tsx', modified_content)

    await controller.check_and_fix_errors()
    smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)
    score_model = await smoke_test_score_rule.execute_rules_and_score('all')
    return score_model

@pytest.mark.asyncio
async def test_autofix_lint_javascript(create_workspace, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        is_web_service=True,
        expectations=[
            "./app/page.tsx没有语法错误"
            "项目能够运行",
        ],
    )
    workspace = await create_workspace(test_data.codezone_id)
    await workspace.smart_detect.set_status('monitoring_errors')
    task = Task(title='nextjs-fastapi初始化环境', description='')
    workspace.set_task(task)
    controller = workspace.playground.agent_controller
    controller.task_state.set_state("working")

    content = await workspace.tools.read_file_content('./app/page.tsx')
    modified_content = content.replace('import Link from "next/link";', "import link from \"next/link\";")
    await workspace.tools.write_file('./app/page.tsx', modified_content)

    await controller.check_and_fix_errors()
    smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)
    score_model = await smoke_test_score_rule.execute_rules_and_score('all')
    return score_model

@pytest.mark.asyncio
async def test_autofix_lint_python(create_workspace, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        is_web_service=True,
        expectations=[
            "./api/index.py没有语法错误"
            "项目能够运行",
        ],
    )
    workspace = await create_workspace(test_data.codezone_id)
    await workspace.smart_detect.set_status('monitoring_errors')
    task = Task(title='nextjs-fastapi初始化环境', description='')
    workspace.set_task(task)
    controller = workspace.playground.agent_controller
    controller.task_state.set_state("working")

    replaced_file_content = """from fastapi import FastAPI
from fastapii.responses import JSONResponse

print "start"
### Create FastAPI instance with custom docs and openapi url
app = FastAPI(docs_ url="/api/py/docs", openapi_url="/api/py/openapi.json")

@app.get("/api/py/helloFastApi")
def hello_fast_api():
    return {message": "Hello from FastAPI"}

@app.get("/api/py/hello2")
def hello2():
return {message": "Hello from FastAPI"}

@app.get("/api/py/hello3")
def hello3():
    """
    await workspace.tools.write_file('./api/index.py', replaced_file_content)
    await workspace.smart_detect.set_status('monitoring_errors')
    content = await workspace.tools.read_file_content('.1024')
    modified_content = re.sub(r'run_command:.*?(?=\n|$)', "run_command: 'python3 -m uvicorn api.index:app --reload'", content)
    await workspace.tools.write_file('.1024', modified_content)

    await controller.check_and_fix_errors()
    smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)
    score_model = await smoke_test_score_rule.execute_rules_and_score('all')
    return score_model

@pytest.mark.asyncio
async def test_autofix_dep_not_install(create_workspace, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        is_web_service=True,
        expectations=[
            "安装了fastapi"
            "项目能够运行",
        ],
    )
    workspace = await create_workspace(test_data.codezone_id)
    await workspace.smart_detect.set_status('monitoring_errors')
    task = Task(title='nextjs-fastapi初始化环境', description='')
    workspace.set_task(task)
    controller = workspace.playground.agent_controller
    controller.task_state.set_state("working")

    await workspace.tools.run_cmd("pip uninstall fastapi --yes")
    content = await workspace.tools.read_file_content('.1024')
    modified_content = re.sub(r'run_command:.*?(?=\n|$)', "run_command: 'python3 -m uvicorn api.index:app --reload'", content)
    await workspace.tools.write_file('.1024', modified_content)

    await controller.check_and_fix_errors()
    smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)
    score_model = await smoke_test_score_rule.execute_rules_and_score('all')
    return score_model
