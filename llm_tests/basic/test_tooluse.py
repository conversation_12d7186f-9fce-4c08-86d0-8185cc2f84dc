import pytest

from heracles.agent_roles.plan_role import PlanRole


@pytest.mark.asyncio  # 0/10 0/10
async def test_response_truncate_when_cmd_return_too_many_lines(create_role, create_test_data, mocker):
    test_data = create_test_data(
        title='ToolUse: fix run_cmd return too much data',
        description='Test if PlanRole avoids redundant file reads when File Snippets are provided',
        project_state_id='fadeOut-game-main',
        goal='Enhance game win feedback with sound and visual elements',
        goal_detail=(
            'Implement a more engaging win condition by adding sound effects and visual feedback. '
            'Replace alert dialogs with an integrated DOM element to display the winner, '
            'and add sound effects for a better user experience.'
        ),
        proposed_list=[
            'Add a winner display element to index.html for visual feedback',
            'Implement sound effects and winner display logic in script.js',
        ],
    )

    plan_role, context = await create_role(test_data, PlanRole)
    result = await context.workspace.tools.run_cmd(
        """for i in {1..510}; do echo "Line $i: This is a sample line of text that contains some information about the current system. Current time is $(date)"; done | cat""",  # noqa
        timeout=60,
    )
    assert len(result.splitlines()) < 510, 'run_cmd 结果行数超过 500 行, 请检查'
