import pytest
from heracles.agent_roles.analyze_role import <PERSON><PERSON><PERSON><PERSON><PERSON>
from llm_tests.basic.score_rule.analyze_role_score_rule import AnalyzeRoleScoreRule

@pytest.mark.asyncio
async def test_analyze_role_project_basic_info(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='AnalyzeRole:basic测试',
        description='',
        project_state_id='ragbot-starter-main'
    )
    _, context = await create_role(test_basic_data, AnalyzeRole)
    await context.workspace.wait_for_done_status()
    project_knowledge = context.workspace.project_knowledge
    assert project_knowledge.basic_info_list[0].language.value == 'typescript'
    assert project_knowledge.basic_info_list[0].runtime.value == 'nodejs'
    assert project_knowledge.basic_info_list[0].framework.value == 'nextjs'
    assert project_knowledge.middlewares == [], '不应该识别到任何中间件'

@pytest.mark.asyncio
async def test_analyze_role_node_env_not_matching_but_compatible(create_role, create_test_basic_data, mocker):
    "项目使用了 20.8.10 版本，提供了 20.8.20 版本，基本兼容，直接使用 Node.js 20 版本即可"

    test_basic_data = create_test_basic_data(
        title='AnalyzeRole: Next.js 环境不匹配',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            "reason 中提到了项目使用 node 20.8.10 版本",
            "environments 中给到 Node.js 20 环境或者 Node.js Basic Environment 环境的 score 分数最高",
            "middlewares 中给到所有中间件的 score 都是 0"
        ]
    )
    env_response = {
        'success': True,
        'data': [
            {'id': '2', 'name': 'Node.js 16', 'runtime': 'node v16.19.1 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []},  # noqa
            {'id': '3', 'name': 'Node.js 20', 'runtime': 'node v20.8.20 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []},  # noqa
            {'id': '4', 'name': 'Node.js Basic Environment (nvm 0.41.1)', 'runtime': 'Managed by nvm 0.41.1 with node v20.1.0 activated, and node v14.21.3, node v16.19.1, node v18.14.1, node v20.8.10, node v22.1.0 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []}  # noqa
        ]
    }
    middleware_response = {
        'success': True,
        'data': [{'id': '1', 'name': 'MySQL8.0'}, {'id': '2', 'name': 'Redis6.2.6'}, {'id': '3', 'name': 'Postgres9.6'}, {'id': '4', 'name': 'Mongo5.0.2'}] # noqa
    }
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_environments', mocker.AsyncMock(return_value=env_response))
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_paas_middlewares', mocker.AsyncMock(return_value=middleware_response))
    analyze_role, context = await create_role(test_basic_data, AnalyzeRole)
    result = await analyze_role.analyze_project_environment()

    score_rule = AnalyzeRoleScoreRule(context.workspace, result, test_basic_data.expectations)
    final_score = await score_rule.execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_analyze_role_node_env_not_matching_but_nvm_provided(create_role, create_test_basic_data, mocker):
    "项目使用了 20.8.10 版本，nvm 提供了 20.8.10 版本，完全兼容，直接 use 即可"

    test_basic_data = create_test_basic_data(
        title='AnalyzeRole: Next.js 环境不匹配',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            "instructions 中没有使用 nvm 安装对应 node 版本，但使用了 nvm use 命令切换到 20.8.10 版本",
            "reason 中提到了项目使用 node 20.8.10 版本",
            "environments 中给到 nvm 环境的 score 分数最高",
        ]
    )
    env_response = {
        'success': True,
        'data': [
            {'id': '2', 'name': 'Node.js 16', 'runtime': 'node v16.19.1 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []},  # noqa
            {'id': '3', 'name': 'Node.js 18', 'runtime': 'node v18.14.1 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []},  # noqa
            {'id': '4', 'name': 'Node.js Basic Environment (nvm 0.41.1)', 'runtime': 'Managed by nvm 0.41.1 with node v20.1.0 activated, and node v14.21.3, node v16.19.1, node v18.14.1, node v20.8.10, node v22.1.0 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []}  # noqa
        ]
    }
    middleware_response = {
        'success': True,
        'data': [{'id': '1', 'name': 'MySQL8.0'}, {'id': '2', 'name': 'Redis6.2.6'}, {'id': '3', 'name': 'Postgres9.6'}, {'id': '4', 'name': 'Mongo5.0.2'}] # noqa
    }
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_environments', mocker.AsyncMock(return_value=env_response))
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_paas_middlewares', mocker.AsyncMock(return_value=middleware_response))
    analyze_role, context = await create_role(test_basic_data, AnalyzeRole)
    result = await analyze_role.analyze_project_environment()

    score_rule = AnalyzeRoleScoreRule(context.workspace, result, test_basic_data.expectations)
    final_score = await score_rule.execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_analyze_role_node_env_not_matching_and_nvm_compatible_version_provided(create_role, create_test_basic_data, mocker):
    "虽然项目使用了 20.8.10 版本，但因为 nvm 提供了 20.8.20 版本，基本兼容，不需要另外安装，而直接 use 即可"

    test_basic_data = create_test_basic_data(
        title='AnalyzeRole: Next.js 环境不匹配',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            "instructions 中没有使用 nvm 安装对应 node 版本，但使用了 nvm use 命令切换到 20.8.20 版本",
            "reason 中提到了项目使用 node ^20.8.10 版本",
            "environments 中给到 nvm 环境的 score 分数最高",
            "middlewares 中给到所有中间件的 score 都是 0"
        ]
    )
    env_response = {
        'success': True,
        'data': [
            {'id': '2', 'name': 'Node.js 16', 'runtime': 'node v16.19.1 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []},  # noqa
            {'id': '3', 'name': 'Node.js 18', 'runtime': 'node v18.14.1 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []},  # noqa
            {'id': '4', 'name': 'Node.js Basic Environment (nvm 0.41.1)', 'runtime': 'Managed by nvm 0.41.1 with node v20.1.0 activated, and node v14.21.3, node v16.19.1, node v18.14.1, node v20.8.20, node v22.1.0 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []}  # noqa
        ]
    }
    middleware_response = {
        'success': True,
        'data': [{'id': '1', 'name': 'MySQL8.0'}, {'id': '2', 'name': 'Redis6.2.6'}, {'id': '3', 'name': 'Postgres9.6'}, {'id': '4', 'name': 'Mongo5.0.2'}] # noqa
    }
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_environments', mocker.AsyncMock(return_value=env_response))
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_paas_middlewares', mocker.AsyncMock(return_value=middleware_response))
    analyze_role, context = await create_role(test_basic_data, AnalyzeRole)
    result = await analyze_role.analyze_project_environment()

    score_rule = AnalyzeRoleScoreRule(context.workspace, result, test_basic_data.expectations)
    final_score = await score_rule.execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
@pytest.mark.unstable(reason='middlewares 会识别 MongoDB')
async def test_analyze_role_node_env_not_matching_and_nvm_not_provided(create_role, create_test_basic_data, mocker):
    "虽然项目使用了 20.8.10 版本，但因为 nvm 没有提供 20 版本，需要另外安装 node 20 版本"

    test_basic_data = create_test_basic_data(
        title='AnalyzeRole: Next.js 环境不匹配',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            "instructions 中使用 nvm 安装 node 20.8 版本(而不是 20.8.10)，并 nvm use 命令切换到 20.8 版本",
            "reason 中提到了项目使用 node ^20.8.10 版本",
            "environments 中给到 nvm 环境的 score 分数最高",
            "middlewares 中给到所有中间件的 score 都是 0"
        ]
    )
    env_response = {
        'success': True,
        'data': [
            {'id': '2', 'name': 'Node.js 16', 'runtime': 'node v16.19.1 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []},  # noqa
            {'id': '3', 'name': 'Node.js 18', 'runtime': 'node v18.14.1 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []},  # noqa
            {'id': '4', 'name': 'Node.js Basic Environment (nvm 0.41.1)', 'runtime': 'Managed by nvm 0.41.1 with node v18.1.0 activated, and node v14.21.3, node v16.19.1 already installed, based on Ubuntu 22.04.4', 'language': 'nodejs', 'packageManagers': []}  # noqa
        ]
    }
    middleware_response = {
        'success': True,
        'data': [{'id': '1', 'name': 'MySQL8.0'}, {'id': '2', 'name': 'Redis6.2.6'}, {'id': '3', 'name': 'Postgres9.6'}, {'id': '4', 'name': 'Mongo5.0.2'}] # noqa
    }
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_environments', mocker.AsyncMock(return_value=env_response))
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_paas_middlewares', mocker.AsyncMock(return_value=middleware_response))
    analyze_role, context = await create_role(test_basic_data, AnalyzeRole)
    result = await analyze_role.analyze_project_environment()

    score_rule = AnalyzeRoleScoreRule(context.workspace, result, test_basic_data.expectations)
    final_score = await score_rule.execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_analyze_role_wblog_environment_perfectly_matching(create_role, create_test_basic_data, mocker):
    test_basic_data = create_test_basic_data(
        title='AnalyzeRole: wblog 完美匹配',
        description='',
        project_state_id='wblog-main',
        expectations=[
            "reason 中提到了项目使用 ruby 3.1.2 版本",
            "environments 中给到 Ruby 3.1.2 环境的 score 是 1.0",
            "middlewares 中给到 Postgres9.6 的 score 是 1.0，且其他几项都是 0 分",
            "instructions 为空",
        ]
    )
    env_response = {
        'success': True,
        'data': [
            {'id': '2', 'name': 'Ruby 3.2.1', 'runtime': 'ruby 3.2.1', 'language': 'Ruby', 'packageManagers': []},
            {'id': '3', 'name': 'Ruby 3.1.2', 'runtime': 'ruby 3.1.2', 'language': 'Ruby', 'packageManagers': []},
            {'id': '4', 'name': 'Ruby Basic Environment (rbenv 1.3.0)', 'runtime': 'Managed by rbenv 1.3.0 with ruby 3.1.2 activated, and ruby 2.7.6, ruby 3.0.4 already installed, based on Ubuntu 22.04.4', 'language': 'Ruby', 'packageManagers': []}  # noqa
        ]
    }
    middleware_response = {
        'success': True,
        'data': [
            {'id': '1', 'name': 'MySQL8.0', 'code': 'MySQL', 'version': '8.0', 'isDefaultVersion': 0},
            {'id': '1-2', 'name': 'MySQL5.6', 'code': 'MySQL', 'version': '5.6', 'isDefaultVersion': 1},
            {'id': '2', 'name': 'Redis6.2.6', 'code': 'Redis', 'version': '6.2.6', 'isDefaultVersion': 1},
            {'id': '3', 'name': 'Postgres9.6', 'code': 'Postgres', 'version': '9.6', 'isDefaultVersion': 1},
            {'id': '4', 'name': 'Mongo5.0.2', 'code': 'Mongo', 'version': '5.0.2', 'isDefaultVersion': 1}
        ]
    }
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_environments', mocker.AsyncMock(return_value=env_response))
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_paas_middlewares', mocker.AsyncMock(return_value=middleware_response))
    analyze_role, context = await create_role(test_basic_data, AnalyzeRole)
    result = await analyze_role.analyze_project_environment()

    score_rule = AnalyzeRoleScoreRule(context.workspace, result, test_basic_data.expectations)
    final_score = await score_rule.execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_analyze_role_wblog_environment_not_matching(create_role, create_test_basic_data, mocker):
    test_basic_data = create_test_basic_data(
        title='AnalyzeRole: wblog 不匹配',
        description='',
        project_state_id='wblog-main',
        expectations=[
            "reason 中提到了项目使用 ruby 3.1.2 版本",
            "middlewares 中给到 Postgres9.6 的 score 是 1.0，且其他几项都是 0 分",
            "instructions 不能为空，且引导用户使用 rbenv 安装 ruby 3.1.2 版本",
        ]
    )
    env_response = {
        'success': True,
        'data': [
            {'id': '2', 'name': 'Ruby 3.2.1', 'runtime': 'ruby 3.2.1', 'language': 'Ruby', 'packageManagers': []},
            {'id': '4', 'name': 'Ruby Basic Environment (rbenv 1.3.0)', 'runtime': 'Managed by rbenv 1.3.0 with ruby 3.0.4 activated, and ruby 2.7.6 already installed, based on Ubuntu 22.04.4', 'language': 'Ruby', 'packageManagers': []}  # noqa
        ]
    }
    middleware_response = {
        'success': True,
        'data': [
            {'id': '1', 'name': 'MySQL8.0', 'code': 'MySQL', 'version': '8.0', 'isDefaultVersion': 0},
            {'id': '1-2', 'name': 'MySQL5.6', 'code': 'MySQL', 'version': '5.6', 'isDefaultVersion': 1},
            {'id': '2', 'name': 'Redis6.2.6', 'code': 'Redis', 'version': '6.2.6', 'isDefaultVersion': 1},
            {'id': '3', 'name': 'Postgres9.6', 'code': 'Postgres', 'version': '9.6', 'isDefaultVersion': 1},
            {'id': '4', 'name': 'Mongo5.0.2', 'code': 'Mongo', 'version': '5.0.2', 'isDefaultVersion': 1}
        ]
    }
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_environments', mocker.AsyncMock(return_value=env_response))
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_paas_middlewares', mocker.AsyncMock(return_value=middleware_response))
    analyze_role, context = await create_role(test_basic_data, AnalyzeRole)
    result = await analyze_role.analyze_project_environment()

    score_rule = AnalyzeRoleScoreRule(context.workspace, result, test_basic_data.expectations)
    final_score = await score_rule.execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_analyze_role_go_todo_environment_perfectly_matching(create_role, create_test_basic_data, mocker):
    test_basic_data = create_test_basic_data(
        title='AnalyzeRole: Go-todo 匹配最新版本',
        description='',
        project_state_id='gin-gorm-todo-app-main',
        expectations=[
            "reason 中提到了未分析到项目使用的 go 具体版本",
            "environments 中给到 Go 1.22 环境的 score 是 1.0",
            "middlewares 中给到 MySQL5.6 的 score 是 1.0，且其他几项都是 0 分",
            "instructions 为空",
        ]
    )
    env_response = {
        'success': True,
        'data': [
            {'id': '2', 'name': 'Go 1.22', 'runtime': 'go1.22.5', 'language': 'Go', 'packageManagers': []},
            {'id': '3', 'name': 'Go 1.13', 'runtime': 'go1.13', 'language': 'Go', 'packageManagers': []},
            {'id': '4', 'name': 'Gvm', 'runtime': 'go1.13', 'language': 'Go', 'packageManagers': []}
        ]
    }
    middleware_response = {
        'success': True,
        'data': [
            {'id': '1', 'name': 'MySQL8.0', 'code': 'MySQL', 'version': '8.0', 'isDefaultVersion': 0},
            {'id': '1-2', 'name': 'MySQL5.6', 'code': 'MySQL', 'version': '5.6', 'isDefaultVersion': 1},
            {'id': '2', 'name': 'Redis6.2.6', 'code': 'Redis', 'version': '6.2.6', 'isDefaultVersion': 1},
            {'id': '3', 'name': 'Postgres9.6', 'code': 'Postgres', 'version': '9.6', 'isDefaultVersion': 1},
            {'id': '4', 'name': 'Mongo5.0.2', 'code': 'Mongo', 'version': '5.0.2', 'isDefaultVersion': 1}
        ]
    }
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_environments', mocker.AsyncMock(return_value=env_response))
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_paas_middlewares', mocker.AsyncMock(return_value=middleware_response))
    analyze_role, context = await create_role(test_basic_data, AnalyzeRole)
    result = await analyze_role.analyze_project_environment()

    score_rule = AnalyzeRoleScoreRule(context.workspace, result, test_basic_data.expectations)
    final_score = await score_rule.execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_analyze_role_go_todo_environment_not_matching(create_role, create_test_basic_data, mocker):
    test_basic_data = create_test_basic_data(
        title='AnalyzeRole: Go-todo 不匹配',
        description='',
        project_state_id='gin-gorm-todo-app-main',
        expectations=[
            "reason 中提到了未分析到项目使用的 go 具体版本",
            "middlewares 中给到 MySQL8.0 的 score 是 1.0，且其他几项都是 0 分",
            "environments 中给到 Go 1.21 环境的 score 分数最高",
            "instructions 为空",
        ]
    )
    env_response = {
        'success': True,
        'data': [
            {'id': '1', 'name': 'Python 3.9', 'runtime': 'python3.9', 'language': 'Python', 'packageManagers': ['pip']},
            {'id': '2', 'name': 'Go 1.21', 'runtime': 'go1.21', 'language': 'Go', 'packageManagers': []},
            {'id': '3', 'name': 'Go 1.13', 'runtime': 'go1.13', 'language': 'Go', 'packageManagers': []},
            {'id': '4', 'name': 'Gvm', 'runtime': 'go1.13', 'language': 'Go', 'packageManagers': []}
        ]
    }
    middleware_response = {
        'success': True,
        'data': [{'id': '1', 'name': 'MySQL8.0'}, {'id': '2', 'name': 'Redis6.2.6'}, {'id': '3', 'name': 'Postgres9.6'}, {'id': '4', 'name': 'Mongo5.0.2'}] # noqa
    }
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_environments', mocker.AsyncMock(return_value=env_response))
    mocker.patch('heracles.agent_roles.analyze_role.utils.get_paas_middlewares', mocker.AsyncMock(return_value=middleware_response))
    analyze_role, context = await create_role(test_basic_data, AnalyzeRole)
    result = await analyze_role.analyze_project_environment()

    score_rule = AnalyzeRoleScoreRule(context.workspace, result, test_basic_data.expectations)
    final_score = await score_rule.execute_rules_and_score("all")
    context.score_model = final_score
    return final_score
