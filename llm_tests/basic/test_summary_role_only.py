import pytest
from heracles.core.schema.task import Task
from heracles.agent_roles.summary_role import Summary<PERSON><PERSON>
from llm_tests.basic.score_rule.summary_role_score_rule import SummaryRoleScoreRule


@pytest.mark.asyncio
async def test_summary_role(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='[Plan] Initialize Development Environment',
        description='Set up the development environment for the FastAPI Admin project by configuring necessary files and installing dependencies', # noqa: E501
        project_state_id='fastapi-admin-main'
    )

    summary_role, context = await create_role(test_basic_data, SummaryRole)

    # extracted from true plan_role result
    test_task_dict = {
        'id': None,
        'title': 'Initialize Development Environment',
        'description': 'Set up the development environment for the FastAPI Admin project by configuring necessary files and installing dependencies', # noqa: E501
        'think_result': "1. Project is a Python FastAPI admin dashboard using TortoiseORM with Redis as a dependency\n2. Package manager pip is available in the environment\n3. Need to modify .1024 file to configure run_command using docker-compose\n4. Need to update .gitignore to include Clacky-specific files (.1024*, !.1024, .breakpoints)\n5. Need to create .env file with DATABASE_URL and REDIS_URL as shown in README.md\n6. The project can be run with 'docker-compose up -d --build' command\n7. Required Redis middleware is mentioned in the README.md\n8. After setup, application can be initialized by visiting http://localhost:8000/admin/init\n9. No need to install additional Python package managers as pip is already available\n10. Project dependencies can be installed with 'pip install fastapi-admin'", # noqa: E501
        'task_steps': [
            {
                'id': '1',
                'title': 'Install Project Dependencies',
                'task_actions': [
                    {
                        'id': '1-1',
                        'action': 'run_command',
                        'status': 'inited',
                        'result': None,
                        'action_object': {'command': 'pip install -e .', 'lifetime': 'long'},
                    },
                    {
                        'id': '1-2',
                        'action': 'run_command',
                        'status': 'inited',
                        'result': None,
                        'action_object': {'command': 'pip install fastapi-admin', 'lifetime': 'long'},
                    },
                ],
                'turn': 1,
            },
            {
                'id': '2',
                'title': 'Configure .1024 File',
                'task_actions': [
                    {
                        'id': '2-1',
                        'action': 'modify_file',
                        'status': 'inited',
                        'result': None,
                        'action_object': {
                            'path': '.1024',
                            'target': '',
                            'detailed_requirement': "- Set the project name to 'fastapi-admin'.\n- Set the run_command to use docker-compose.\n- Define the preview_url to access the application.", # noqa: E501
                            'references': ['file://examples/main.py:1-98', 'playbook://handling-1024-file'],
                            'snapshot_uuid': None,
                        },
                    }
                ],
                'turn': 1,
            },
            {
                'id': '3',
                'title': 'Update .gitignore File',
                'task_actions': [
                    {
                        'id': '3-1',
                        'action': 'modify_file',
                        'status': 'inited',
                        'result': None,
                        'action_object': {
                            'path': '.gitignore',
                            'target': '',
                            'detailed_requirement': 'Add Clacky-specific entries to the .gitignore file:\n- Add .1024* with exception for the main .1024 file\n- Add .breakpoints\n- Keep all existing entries', # noqa: E501
                            'references': ['playbook://handling-gitignore-file'],
                            'snapshot_uuid': None,
                        },
                    }
                ],
                'turn': 1,
            },
            {
                'id': '4',
                'title': 'Create Environment Configuration',
                'task_actions': [
                    {
                        'id': '4-1',
                        'action': 'add_file',
                        'status': 'inited',
                        'result': None,
                        'action_object': {
                            'path': '.env',
                            'target': '',
                            'detailed_requirement': 'Create a .env file with the following environment variables:\n- DATABASE_URL: Connection string for the database (use PostgreSQL format)\n- REDIS_URL: Connection string for Redis\n- Set appropriate default development values for local testing', # noqa: E501
                            'references': [
                                'file://examples/main.py:34-37',
                                'file://examples/main.py:80-92',
                                'playbook://add-environment-variables-for-databases-and-other-middlewares',
                            ],
                            'snapshot_uuid': None,
                        },
                    }
                ],
                'turn': 1,
            },
            {
                'id': '5',
                'title': 'Start Development Environment',
                'task_actions': [
                    {
                        'id': '5-1',
                        'action': 'run_command',
                        'status': 'inited',
                        'result': None,
                        'action_object': {'command': 'docker-compose up -d --build', 'lifetime': 'long'},
                    }
                ],
                'turn': 1,
            },
        ],
        'current_turn': 1,
    }
    task = Task.model_validate(test_task_dict)
    context.workspace.set_task(task)
    playbook = await summary_role.run()
    result = {'task': test_task_dict, 'playbook': playbook.model_dump(mode='json')}
    expectations = [
        '比较task和playbook, playbook包含task里的title, description, tags, original_content字段',
        '比较task和playbook, title和description字段应准确地总结task里的步骤',
        '比较task和playbook, original_content的Procedure章节应该准确地描述task里的步骤且无重复',
    ]

    final_score = await SummaryRoleScoreRule(summary_role.workspace, result, expectations).execute_rules_and_score('all')
    return final_score
