import pytest
from heracles.agent_roles.cmd_k_role import CmdKRole
from llm_tests.basic.score_rule.cmd_k_role_score_rule import CmdKRoleScoreRule

@pytest.mark.asyncio
async def test_cmd_k_role(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='CmdKRole:Next.js RAGBot Starter/modify .env.example file',
        description='修改 Next.js 项目 RAGBot Starter 中的 .env.example 文件',
        project_state_id='ragbot-starter-main',
        expectations=[
            "结果只有 1 行内容",
            "该行有注释"
        ]
    )

    cmd_k_role, context = await create_role(test_basic_data, CmdKRole)

    async def trigger_callback(message):
        print(message)

    response = await cmd_k_role.run(".env.example", 1, 1, "增加必要的注释", trigger_callback)
    final_score = await CmdKRoleScoreRule(cmd_k_role.workspace, response, test_basic_data.expectations).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_cmd_k_role_add_comment(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='CmdKRole:Next.js RAGBot Starter/modify .env.example file',
        description='修改 Next.js 项目 RAGBot Starter 中的 .env.example 文件',
        project_state_id='ragbot-starter-main',
        expectations=[
            "结果只有 1 行内容",
            "该行的空格缩进是 4"
        ]
    )

    cmd_k_role, context = await create_role(test_basic_data, CmdKRole)

    async def trigger_callback(message):
        print(message)

    response = await cmd_k_role.run("app/page.tsx", 20, 20, "只修改behavior参数, 更换一种滚动方式", trigger_callback)
    final_score = await CmdKRoleScoreRule(cmd_k_role.workspace, response, test_basic_data.expectations).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score
