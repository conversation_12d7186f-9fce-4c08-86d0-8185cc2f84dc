import pytest

from heracles.agent_roles.spec_role import Spec<PERSON><PERSON>
from llm_tests.basic.score_rule.spec_role_score_rule import SpecRoleScoreRule


@pytest.mark.asyncio
async def test_spec_role_simple(create_role, create_test_data):

    test_data = create_test_data(
        title='SpecRole: fadeout-game',
        description='HTML环境,剪头石头布小游戏',
        project_state_id="fadeOut-game-main",
        goal='在胜利（玩家赢）时，添加声音效果',
        goal_detail='玩家赢时，播放 assset 目录下提供的 music.mp3 音乐',
        expectations=[
            "Modified 'index.html' to include an audio element for the victory sound.",
            "Updated 'script.js' to play the victory sound when the player wins."
        ],
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)

    final_score = await SpecRoleScoreRule(spec_role.workspace, spec, test_data.expectations).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

# @pytest.mark.unstable(role='spec_role', reason='failure')
# @pytest.mark.asyncio
# async def test_spec_role_simple_initialize_environment(create_role, create_test_data):
#     test_data = create_test_data(
#         title="SpecRole: chat-websocket-gin/初始化环境",
#         description="初始化 Go 项目 chat-websocket-gin 环境",
#         codezone_id='690631585007382528',
#         codezone_git_url='https://github.com/tinkerbaj/chat-websocket-gin.git',
#         codezone_description='Go-gin环境,chat-websocket-gin',
#         expect=""" "Fetch all necessary Go packages using `go mod tidy`.",
# "Modify the configuration file `.1024` to define the run command." """,
#         goal='Initialize the Development Environment',
#         goal_detail='',
#     )

#     spec_role, context = await create_role(test_data, SpecRole)
#     spec = await spec_role.run(test_data.goal, test_data.goal_detail)

#     final_score = await SpecRoleScoreRule(spec_role.workspace, spec=spec).execute_rules_and_score("all")
#     context.score_model = final_score
#     return final_score

# @pytest.mark.asyncio
# async def test_spec_role_hard_initialize_environment(create_role, create_test_data):
#     test_data = create_test_data(
#         title="SpecRole: Nextra: Docs Starter Kit/初始化环境",
#         description="为 Next.js 项目 Nextra 初始化开发环境",
#         codezone_id='685891705400180736',
#         codezone_git_url='https://github.com/li382112772/nextra-docs-template',
#         codezone_description='Next.js 环境, Nextra/Docs Starter Kit',
#         expect=""" "Use pnpm as the package manager to install the necessary dependencies.",
#     "Modify the .1024 configuration file to define the run command, host, and port. """,
#         goal='Initialize the Development Environment',
#         goal_detail=""" 1. Use the correct package manager to install the necessary dependencies.
#         2. Modify the .1024 configuration file to define run command. """,
#     )

#     spec_role, context = await create_role(test_data, SpecRole)
#     spec = await spec_role.run(test_data.goal, test_data.goal_detail)

#     final_score = await SpecRoleScoreRule(spec_role.workspace, spec=spec).execute_rules_and_score("all")
#     context.score_model = final_score
#     return final_score

@pytest.mark.asyncio
async def test_spec_role_respect_user_language(create_role, create_test_data):
    test_data = create_test_data(
        title="SpecRole: fadeout-game 返回正确用户语言",
        description="HTML环境,剪头石头布小游戏",
        project_state_id="fadeOut-game-main",
        goal="在胜利（玩家赢）时，添加声音效果",
        goal_detail="",
        expectations=[
            "内容不同字段正确使用了合适的语言规范",
            "其中 suggested_name, goal, goal_detail, current_list, proposed_list 使用了中文语言，而不是英文",
            "其中 suggested_branch 使用了英文语言，而不是中文"
        ],
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)

    final_score = await SpecRoleScoreRule(spec_role.workspace, spec, test_data.expectations).execute_rules_and_score("all") # noqa
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_spec_role_respect_user_language2(create_role, create_test_data):
    test_data = create_test_data(
        title="SpecRole: fadeout-game 返回正确用户语言2",
        description="HTML环境,剪头石头布小游戏",
        project_state_id="fadeOut-game-main",
        goal="Add historical gestures and results for the player and computer.",
        goal_detail="Create a table to display the historical gestures and results on the page.",
        expectations=[
            "内容不同字段正确使用了合适的语言规范",
            "其中 suggested_name, goal, goal_detail, current_list, proposed_list 使用了英文语言，而不是中文",
            "其中 suggested_branch 使用了英文语言，而不是中文"
        ],
    )

    spec_role, context = await create_role(test_data, SpecRole)
    spec = await spec_role.run(test_data.goal, test_data.goal_detail)

    final_score = await SpecRoleScoreRule(spec_role.workspace, spec, test_data.expectations).execute_rules_and_score("all") # noqa
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_util_role_refine_brief_requirement(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='SpecRole: 简短需求润色测试',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            '包含项目目标',
            '包含技术规格',
            '包含核心功能',
            '直接给出了润色后的需求而不是询问用户更多信息',
            '没有多余的类似"让我想想","这样够了吗"的解释',
            '技术细节中没有给出多种方案供用户选择',
        ],
    )
    util_role, context = await create_role(test_basic_data, SpecRole)

    res = await util_role.refine_prompt('帮我做一个todolist', 'new_project')
    final_score = await SpecRoleScoreRule(util_role.workspace, res, test_basic_data.expectations).execute_rules_and_score() # noqa
    context.score_model = final_score
    return final_score


@pytest.mark.asyncio
async def test_util_role_refine_detailed_requirement(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='SpecRole: 详细需求润色测试',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            '包含项目目标',
            '包含技术规格',
            '包含核心功能',
            '直接给出了润色后的需求而不是询问用户更多信息',
            '没有多余的类似"让我想想","这样够了吗"的解释',
            '技术细节中没有给出多种方案供用户选择',
        ],
    )
    util_role, context = await create_role(test_basic_data, SpecRole)

    res = await util_role.refine_prompt(
        '帮我做一个基于fastapi的全栈todolist，需要支持用户登录，任务分类，任务优先级，截止日期等功能', 'new_project'
    )
    final_score = await SpecRoleScoreRule(util_role.workspace, res, test_basic_data.expectations).execute_rules_and_score() # noqa
    context.score_model = final_score
    return final_score


@pytest.mark.asyncio
async def test_util_role_refine_with_image(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='SpecRole: 带图片需求润色测试',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            '包含项目目标',
            '包含技术规格',
            '包含核心功能',
            '直接给出了润色后的需求而不是询问用户更多信息',
            '没有多余的类似"让我想想","这样够了吗"的解释',
            '技术细节中没有给出多种方案供用户选择',
        ],
    )
    util_role, context = await create_role(test_basic_data, SpecRole)

    res = await util_role.refine_prompt(
        '帮我做一个类似这样的todolist@@user_context[image_url://https://cdn.zh.okaapps.com/resource/blog/todo/ms_todo.png]@@',
        'new_project',
    )
    final_score = await SpecRoleScoreRule(util_role.workspace, res, test_basic_data.expectations).execute_rules_and_score() # noqa
    context.score_model = final_score
    return final_score


@pytest.mark.asyncio
async def test_util_role_refine_with_webpage(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='SpecRole: 带网页参考需求润色测试',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            '包含项目目标',
            '包含技术规格',
            '包含核心功能',
            '直接给出了润色后的需求而不是询问用户更多信息',
            '没有多余的类似"让我想想","这样够了吗"的解释',
            '技术细节中没有给出多种方案供用户选择',
        ],
    )
    util_role, context = await create_role(test_basic_data, SpecRole)

    res = await util_role.refine_prompt(
        '帮我做一个类似的网页番茄钟应用@@user_context[webpage://https://www.tomatolist.com/timer.html]@@', 'new_project'
    )
    final_score = await SpecRoleScoreRule(util_role.workspace, res, test_basic_data.expectations).execute_rules_and_score() # noqa
    context.score_model = final_score
    return final_score
