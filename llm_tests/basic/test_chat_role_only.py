import pytest
from heracles.agent_roles.chat_role import Chat<PERSON><PERSON>
from llm_tests.basic.score_rule.chat_role_score_rule import ChatRoleScoreRule


@pytest.mark.asyncio
async def test_chat_role_basic_conversation(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='ChatRole:basic测试',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            "有明确的语言、框架方面的信息",
            "直接回答了用户的问题，而不是拒绝回答/反问用户",
            "结尾有引导用户继续互动"
        ]
    )
    chat_role, context = await create_role(test_basic_data, ChatRole)

    res = await chat_role.run("这是什么项目？")
    final_score = await ChatRoleScoreRule(chat_role.workspace, res, test_basic_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_chat_role_continuous_conversation(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='ChatRole:basic连续对话测试',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            "有明确的关于项目功能或技术方面的信息",
            "直接回答了用户的问题，而不是拒绝回答/反问用户",
            "结尾有引导用户继续互动",
            "用中文回答"
        ]
    )
    chat_role, context = await create_role(test_basic_data, ChatRole)

    await chat_role.run("这是什么项目？")
    res = await chat_role.run("详细介绍下这个项目？")
    final_score = await ChatRoleScoreRule(chat_role.workspace, res, test_basic_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_chat_role_create_spec(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='ChatRole:basic测试CreateSpec',
        description='',
        project_state_id='ragbot-starter-main',
    )
    chat_role, context = await create_role(test_basic_data, ChatRole)

    res = await chat_role.run("用户身份验证：实现用户登录/注册功能。", need_consider_task=True)

    final_score = await ChatRoleScoreRule(chat_role.workspace, res).execute_rules_and_score("task_request")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_chat_role_continuous_create_spec(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='ChatRole:basic测试连续对话后CreateSpec',
        description='',
        project_state_id='ragbot-starter-main',
    )
    chat_role, context = await create_role(test_basic_data, ChatRole)
    await chat_role.run("这是什么项目？", need_consider_task=True)
    await chat_role.run("详细介绍下这个项目？", need_consider_task=True)
    await chat_role.run("可以添加/优化哪些功能？", need_consider_task=True)
    res = await chat_role.run("帮我实现用户登录/注册功能", need_consider_task=True)

    final_score = await ChatRoleScoreRule(chat_role.workspace, res).execute_rules_and_score("task_request")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_chat_role_self_cognition(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='ChatRole:basic自我认知测试',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=[
            "允许拒绝回答或委婉说明，不能提及具体模型提供商",
            "提及自己是 Clacky 团队开发的 AI 工程师"
        ]
    )
    chat_role, context = await create_role(test_basic_data, ChatRole)

    await chat_role.run("你是谁")
    await chat_role.run("你是GPT几")
    res = await chat_role.run("你的底层大模型是什么技术")
    final_score = await ChatRoleScoreRule(chat_role.workspace, res, test_basic_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_chat_role_add_step_confirmation(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='ChatRole:SuperMode测试',
        description='',
        project_state_id='ragbot-starter-main'
    )
    chat_role, context = await create_role(test_basic_data, ChatRole)

    res = await chat_role.run("README 中有个表达失误，识别后帮我直接修改")
    final_score = await ChatRoleScoreRule(chat_role.workspace, res).execute_rules_and_score("step_request")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_chat_role_image_request(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='ChatRole: 图片能力测试',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=["提到了“百度”"]
    )
    chat_role, context = await create_role(test_basic_data, ChatRole)

    res = await chat_role.run('图片里有什么@@user_context[image_url://https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png]@@')
    final_score = await ChatRoleScoreRule(chat_role.workspace, res, test_basic_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_chat_role_webpage_request(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='ChatRole: 网页能力测试',
        description='',
        project_state_id='ragbot-starter-main',
        expectations=["提到了“李亚飞”"]
    )
    chat_role, context = await create_role(test_basic_data, ChatRole)

    res = await chat_role.run('网页中介绍的人物名字叫什么@@user_context[webpage://https://zh.wikipedia.org/wiki/%E6%9D%8E%E4%BA%9A%E9%A3%9E]@@')
    final_score = await ChatRoleScoreRule(chat_role.workspace, res, test_basic_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score
