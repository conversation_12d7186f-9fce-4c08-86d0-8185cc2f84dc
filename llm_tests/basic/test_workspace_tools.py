import pytest

from heracles.core.exceptions import IDEServerFileNotFoundError
from llm_tests.utils.schema import APIAssert, FileContentAssert

async def prepare_workspace(create_workspace, create_test_basic_data):
    test_data = create_test_basic_data(
        title='CodeRole:Next.js RAGBot Starter',
        project_state_id='ragbot-starter-main'
    )
    codezone = test_data.dataset.get_codezone_id_by_state_id(test_data.project_state_id)
    return await create_workspace(codezone.id)

@pytest.mark.asyncio
async def test_workspace_tools_read_file(create_workspace, create_test_basic_data):
    workspace = await prepare_workspace(create_workspace, create_test_basic_data)
    file_result = await workspace.tools.read_file('.env.example', should_read_entire_file=True)
    assert "OPENAI_API_KEY=REPLACE_ME" in file_result, "file_result 不包含正确内容"

    with pytest.raises(IDEServerFileNotFoundError, match="non_existent_file.txt"):
        file_result = await workspace.tools.read_file('non_existent_file.txt', should_read_entire_file=True)

@pytest.mark.asyncio
async def test_workspace_tools_snapshot_file(create_workspace, create_test_basic_data):
    workspace = await prepare_workspace(create_workspace, create_test_basic_data)
    # snapshot 文件存在的情况
    file_result = await workspace.tools.read_file('.env.example', should_read_entire_file=True)
    snapshot_uuid = await workspace.tools.snapshot_file('.env.example', file_result)
    assert snapshot_uuid is not None, "snapshot_uuid 为 None"

    snapshots = await workspace.tools.query_snapshot_file('.env.example')
    assert len(snapshots) == 1

    snapshot_content = await workspace.tools.query_snapshot_file_by_uuid('.env.example', snapshot_uuid)
    assert snapshot_content is not None
    assert snapshot_content == file_result

    # snapshot 文件不存在的情况
    snapshot_uuid = await workspace.tools.snapshot_file('non_existent_file.txt', '')
    assert snapshot_uuid is not None, "snapshot_uuid 为 None"

    snapshot_content = await workspace.tools.query_snapshot_file_by_uuid('non_existent_file.txt', snapshot_uuid)
    assert snapshot_content is not None
    assert snapshot_content == ''

@pytest.mark.asyncio
async def test_workspace_assert_response(create_workspace, create_test_basic_data):
    workspace = await prepare_workspace(create_workspace, create_test_basic_data)
    assert_response = APIAssert(
        request_method='GET',
        request_path='/',
        response_status=200,
        assert_type='contains',
        response_body='百度'
    )
    await assert_response.assert_response(workspace, 'https://www.baidu.com')

@pytest.mark.asyncio
async def test_workspace_assert_file_content(create_workspace, create_test_basic_data):
    workspace = await prepare_workspace(create_workspace, create_test_basic_data)
    assert_file_content = FileContentAssert(
        file_path='.env.example',
        content='OPENAI_API_KEY=REPLACE_ME',
        assert_type='contains'
    )
    await assert_file_content.assert_file(workspace)
