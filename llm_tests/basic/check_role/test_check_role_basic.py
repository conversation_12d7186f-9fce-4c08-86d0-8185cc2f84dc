import pytest
import re
from heracles.agent_roles.check_role import <PERSON><PERSON><PERSON>
from llm_tests.basic.score_rule.check_role_score_rule import CheckRoleScoreRule

@pytest.mark.asyncio
async def test_empty_run_command(create_role, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            "检测到空的.1024文件的run_command",
        ],
    )

    check_role, context = await create_role(test_data, CheckRole)

    content = await context.workspace.tools.read_file_content('.1024')
    modified_content = re.sub(r'run_command:.*?(?=\n|$)', "", content)
    await context.workspace.tools.write_file('.1024', modified_content)

    report = await check_role.check_errors()
    final_score = await CheckRoleScoreRule(check_role.workspace, report, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_wrong_run_command(create_role, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            "检测到.1024文件的run_command不是正常的npm dev, yarn dev, pnpm dev等命令",
        ],
    )

    check_role, context = await create_role(test_data, CheckRole)

    content = await context.workspace.tools.read_file_content('.1024')
    modified_content = re.sub(r'run_command:.*?(?=\n|$)', "run_command: './yyyy'", content)
    await context.workspace.tools.write_file('.1024', modified_content)

    report = await check_role.check_errors()
    final_score = await CheckRoleScoreRule(check_role.workspace, report, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_missing_import(create_role, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            "检查到缺少import: ./app/page.tsx",
        ],
    )
    check_role, context = await create_role(test_data, CheckRole)

    content = await context.workspace.tools.read_file_content('./app/page.tsx')
    modified_content = re.sub(r'import.*?(?=\n|$)', "", content)
    await context.workspace.tools.write_file('./app/page.tsx', modified_content)

    report = await check_role.check_errors()
    final_score = await CheckRoleScoreRule(check_role.workspace, report, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score


@pytest.mark.asyncio
async def test_lint_javascript(create_role, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            "检查到语法错误",
        ],
    )
    check_role, context = await create_role(test_data, CheckRole)

    await context.workspace.tools.read_file_content('./app/page.tsx')
    content = await context.workspace.tools.read_file_content('./app/page.tsx')
    modified_content = content.replace('import Link from "next/link";', "// import Link from \"next/link\";")
    await context.workspace.tools.write_file('./app/page.tsx', modified_content)

    report = await check_role.check_errors()
    final_score = await CheckRoleScoreRule(check_role.workspace, report, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score


@pytest.mark.asyncio
async def test_lint_python(create_role, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            "检查到语法错误",
        ],
    )
    check_role, context = await create_role(test_data, CheckRole)

    replaced_file_content = """from fastapi import FastAPI
from fastapii.responses import JSONResponse

### Create FastAPI instance with custom docs and openapi url
app = FastAPI(docs_ url="/api/py/docs", openapi_url="/api/py/openapi.json")

@app.get("/api/py/helloFastApi")
def hello_fast_api():
    return {message": "Hello from FastAPI"}

@app.get("/api/py/hello2")
def hello2():
return {message": "Hello from FastAPI"}

@app.get("/api/py/hello3")
def hello3():
    """
    await context.workspace.tools.write_file('./api/index.py', replaced_file_content)


    report = await check_role.check_errors()
    final_score = await CheckRoleScoreRule(check_role.workspace, report, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score
