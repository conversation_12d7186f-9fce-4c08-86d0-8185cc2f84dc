import pytest
from typing import Type, <PERSON><PERSON>

from heracles.agent_roles.role_base import Role<PERSON><PERSON>
from heracles.core.schema.test import Context
from llm_tests.utils import create_trace_score, update_trace_tag
from llm_tests.utils.schema import TestDataBase, ScoreModel
from heracles.agent_roles.code_role import CodeRole

@pytest.fixture
def create_role(create_workspace, mocker):
    context = Context()

    async def _create_role(
        test_basic_data: Type[TestDataBase],
        role_cls: Type[RoleBase]
    ) -> Tuple[RoleBase, Context]:
        codezone_id = test_basic_data.codezone_id
        if role_cls == CodeRole:
            mocker.patch(
                'heracles.agent_controller.agent_role_controller.AgentRoleController.run_build_workspace_with_background',
                return_value=False
            )
        workspace = await create_workspace(codezone_id)
        context.workspace = workspace
        context.title = test_basic_data.title

        role = role_cls(workspace)
        # 在这里将 context 返回回去, 可以用 context.score_model = xx_model 来设定全局的分数
        return role, context

    try:
        yield _create_role
    finally:
        update_trace_tag(context.workspace, 'basic-test', context.title)

        if score_model := context.score_model:
            if not isinstance(score_model, ScoreModel):
                raise ValueError("score_model must be an instance of ScoreModel")
            create_trace_score(context.workspace, score_model.value, score_model.comment)
        else:
            create_trace_score(context.workspace, 0.01, 'basic test no comment here')

def pytest_addoption(parser):
    parser.addoption('--repeat', action='store', help='Number of times to repeat each test')

def pytest_generate_tests(metafunc):
    repeat_option = getattr(metafunc.config.option, 'repeat', None)
    if repeat_option is not None:
        count = int(repeat_option)

        # We're going to duplicate these tests by parametrizing them,
        # which requires that each test has a fixture to accept the parameter.
        # We can add a new fixture like so:
        metafunc.fixturenames.append('tmp_ct')

        # Now we parametrize. This is what happens when we do e.g.,
        # @pytest.mark.parametrize('tmp_ct', range(count))
        # def test_foo(): pass
        metafunc.parametrize('tmp_ct', range(count))
