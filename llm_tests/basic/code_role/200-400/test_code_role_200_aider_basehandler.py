import pytest


from heracles.agent_roles.code_role import Code<PERSON><PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 200_aider_basehandler',
        description='Refactor BaseHandler.adapt_method_mode',
        project_state_id="refactor-benchmark-base_BaseHandler_adapt_method_mode"
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Refactor BaseHandler.adapt_method_mode",
        description="Refactor the `adapt_method_mode` method in the `BaseHandler` class into a stand-alone, top-level function and update all calls to this method accordingly.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Refactor and update references",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Refactor adapt_method_mode and update method calls',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='base.py',
            detailed_requirement="Refactor the `adapt_method_mode` method from the `BaseHandler` class into a stand-alone function named `adapt_method_mode`. Update all existing `self.adapt_method_mode` references to use the new function.",  # noqa: E501
            references=["Ensure the new function is named `adapt_method_mode`, and all references to `self.adapt_method_mode` are updated accordingly."]  # noqa: E501
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
