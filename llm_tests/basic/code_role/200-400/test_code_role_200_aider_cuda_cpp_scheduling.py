import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 200_aider_cuda_cpp_scheduling',
        description='Refactor CUDACPPScheduling._can_fuse_epilogue_impl',
        project_state_id="refactor-benchmark-cuda_cpp_scheduling_CUDACPPScheduling__can_fuse_epilogue_impl"
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Refactor CUDACPPScheduling._can_fuse_epilogue_impl",
        description="Refactor the `_can_fuse_epilogue_impl` method in the `CUDACPPScheduling` class into a stand-alone, top-level function and update all calls to this method accordingly.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Refactor and update references",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Refactor _can_fuse_epilogue_impl and update method calls',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='cuda_cpp_scheduling.py',
            detailed_requirement="Refactor the `_can_fuse_epilogue_impl` method from the `CUDACPPScheduling` class into a stand-alone function named `_can_fuse_epilogue_impl`. Update all existing `self._can_fuse_epilogue_impl` references to work with the new function.",  # noqa: E501
            references=["Ensure the new function is named `_can_fuse_epilogue_impl` and all references to `self._can_fuse_epilogue_impl` are updated accordingly."]  # noqa: E501
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
