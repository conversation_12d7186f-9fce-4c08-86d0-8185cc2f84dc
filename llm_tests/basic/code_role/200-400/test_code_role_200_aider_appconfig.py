import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 200_aider_appconfig.py',
        description='Refactor AppConfig._path_from_module',
        project_state_id="refactor-benchmark-config_AppConfig__path_from_module"
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Refactor AppConfig._path_from_module",
        description="Refactor the `_path_from_module` method in the `AppConfig` class into a stand-alone, top-level function and update all calls to this method accordingly.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Refactor and update references",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Refactor _path_from_module and update method calls',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='config.py',
            detailed_requirement="Refactor the `_path_from_module` method from the `AppConfig` class into a stand-alone function named `_path_from_module`. Update all existing `self._path_from_module` references to work with the new function.",  # noqa: E501
            references=["Ensure the new function is named `_path_from_module` and all references are updated accordingly."]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
async def test_code_run_add_file(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Generate documentation for `config.py` file',
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path='README-config.md',
            detailed_requirement="Generate documentation for the `config.py` file.",  # noqa: E501
            references=["file://config.py"]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
