import pytest


from heracles.agent_roles.code_role import Code<PERSON><PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 100_music_player',
        description='实现“暂停当前曲目”和“上一首”功能',
        project_state_id="simple_music_player-main"
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Implement Pause and Previous Track Functionality",
        description="Implement the pause and previous track functionality for the music player application",  # noqa: E501
    )
    task_step = TaskStep(
        title="Implement pauseTrack and prevTrack Function",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Implement pauseTrack and prevTrack function',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='main.js',
            detailed_requirement="In main.js, implement the pauseTrack function to pause the current track, ensuring it includes the logic to halt audio playback. Also, implement the prevTrack function to navigate to the previous track. This function should check if the current track is the first; if so, it should switch to the last track. Otherwise, it should move to the previous track, load it, and start playback.",  # noqa: E501
            references=[]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
