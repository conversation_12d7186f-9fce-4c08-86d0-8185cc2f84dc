import pytest


from heracles.agent_roles.code_role import Code<PERSON><PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 100_fadeout-game',
        description='在胜利（玩家赢）时，添加声音效果',
        project_state_id="fadeOut-game-main",
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Add Sound Effect on Player Win",
        description="Implement functionality to play sound effect when the player wins the game.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Add Function to Play Music, Integrate Audio Playback in Player Win Condition",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify_gitignore(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Add a .gitignore file with the specified configurations to ignore unnecessary files.',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='.gitignore',
            detailed_requirement="Add configurations to ignore Clacky Env Files, System, Dependencies, Logs, Environment, and JavaScript specific files.",  # noqa: E501
            references=["playbook://Handling .gitignore File"]  # noqa: E501
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Modify script.js to Add playVictorySound Function and to Call playVictorySound on Player Win',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='script.js',
            detailed_requirement="Add a function named playVictorySound that creates an Audio object for the file located at './assets/music.mp3' and play the audio; Call playVictorySound function inside the player win condition block where the winner textContent is set to '玩家赢'.",  # noqa: E501
            references=["const playVictorySound = () => {\n  const audio = new Audio('./assets/music.mp3');\n  audio.play();\n};\nif (playerChoice === '石头') {\n  if (computerChoice === '剪刀') {\n    winner.textContent = '玩家赢';\n    playVictorySound();\n    pScore++;\n    updateScore();\n    return;\n  }"]  # noqa: E501
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
