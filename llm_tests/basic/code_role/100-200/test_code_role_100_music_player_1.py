import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 100_music_player_1',
        description='HTML环境,音乐播放器,需求：格式化时间显示',
        project_state_id="simple_music_player-main"
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Format Time Display in seekUpdate Function",
        description="Format the time in the seekUpdate function to 'mm:ss' format",  # noqa: E501
    )
    task_step = TaskStep(
        title="Modify seekUpdate Function",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Format time to mm:ss in seekUpdate function',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='main.js',
            detailed_requirement="Format the current time and total duration to 'mm:ss' format in the seekUpdate function. Ensure both current time and total duration are always displayed as two digits by prefixing single-digit numbers with a zero.",  # noqa: E501
            references=[]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
