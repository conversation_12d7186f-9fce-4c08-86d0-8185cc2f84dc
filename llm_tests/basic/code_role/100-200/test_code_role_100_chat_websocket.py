import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 100_chat_websocket',
        description='在 Go 项目 chat-websocket-gin 中添加用户身份验证功能',
        project_state_id="chat-websocket-gin-main",
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Add User Authentication to WebSocket Connections",
        description="Implement user authentication functionality during WebSocket connections to ensure only authorized users can join chat rooms.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Modify `chat/chat.go` to handle WebSocket authentication",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Add authentication logic to WebSocket connection',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='chat/chat.go',
            detailed_requirement="Add authentication logic to check user credentials in the WebSocket connection handler.",  # noqa: E501
            references=[]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
