import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 100_express_blog_2',
        description='在 Express 项目 express_blog 中实现创建新博文的前端功能',
        project_state_id="express-blog-main",
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="实现创建新博文的前端功能",
        description="在现有的前端代码中添加一个表单, 用于输入新博文的标题、作者和内容, 并将这些数据通过API发送到后端",  # noqa: E501
    )
    task_step = TaskStep(
        title="添加表单和处理提交逻辑",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='添加用于输入新博文的表单',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='public/index.html',
            detailed_requirement="在适当的位置添加一个HTML表单, 包括输入新博文的标题、作者和内容的字段",  # noqa: E501
            references=[]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
async def test_code_copy_readme(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Create README.zh-CN.md with README.md content',
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path='README.zh-CN.md',
            detailed_requirement="Create a new file named README.zh-CN.md and copy the content from README.md into it.",  # noqa: E501
            references=["file://README.md", "Ensure the entire content from README.md is copied accurately to the new file."]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
