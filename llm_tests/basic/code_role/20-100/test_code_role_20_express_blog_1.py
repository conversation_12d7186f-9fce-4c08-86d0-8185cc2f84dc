import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 20_express_blog_1',
        description='在 Express 项目 express_blog 中实现完整的用户登录功能',
        project_state_id="express-blog-main",
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Implement Complete User Login Functionality",
        description="Add functionality for user login, including backend APIs for email and password verification, frontend integration for user login, and a User model in the database with email and password fields.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Add Backend API for User Login",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Modify db.js to Add User Model',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='db.js',
            detailed_requirement="""1. Import Sequelize and DataTypes.
            2. Define a User model with email and password fields.""",  # noqa: E501
            references=["""
            const { Sequelize, DataTypes } = require('sequelize');
            const sequelize = new Sequelize('sqlite::memory:');
            const User = sequelize.define('User', { email: DataTypes.STRING, password: DataTypes.STRING });
            module.exports = { sequelize, Blog, User };
            """]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
