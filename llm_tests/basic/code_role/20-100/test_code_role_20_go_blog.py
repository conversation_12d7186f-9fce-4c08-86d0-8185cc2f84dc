import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 20_go_blog',
        description='在 Go 项目 go-blog 中实现图片上传功能',
        project_state_id='go-blog-main',
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Implement Image Upload Functionality",
        description="Implement the ability to upload images during article creation and store the images on the server.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Update the HTML Form",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Modify the new article form',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='templates/new_article.html',
            detailed_requirement="""Update the form in templates/new_article.html to include enctype="multipart/form-data" and add a file input field for image upload.""",  # noqa: E501
            references=["<form method=\"POST\" action=\"/articles/new\" enctype=\"multipart/form-data\">"]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
