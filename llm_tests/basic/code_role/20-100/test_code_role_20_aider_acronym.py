import pytest

from heracles.agent_roles.code_role import Code<PERSON><PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 20_aider_acronym',
        description='Convert Phrase to Acronym',
        project_state_id="exercism-python-main-acronym"
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Convert Phrase to Acronym",
        description="Implement a program that converts a phrase into its acronym by taking the first letter of each word and removing all non-hyphen punctuation. Hyphens act as word separators.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Implement and test acronym conversion",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Implement acronym conversion and handle punctuation',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='acronym.py',
            detailed_requirement="Write a function that converts a phrase to its acronym. The function should take the first letter of each word (words are separated by spaces or hyphens) and return the acronym in uppercase. All other punctuation should be removed. For example, 'Portable Network Graphics' should return 'PNG', and 'Thank George It's Friday!' should return 'TGIF'.",  # noqa: E501
            references=["Test with example phrases like 'As Soon As Possible' -> 'ASAP', 'Liquid-crystal display' -> 'LCD', and 'Thank George It's Friday!' -> 'TGIF'."]  # noqa: E501
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
