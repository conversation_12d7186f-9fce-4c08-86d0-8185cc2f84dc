import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 20_go_todo_app',
        description='在 Go 项目 gin-gorm-todo-app 中使用环境变量配置数据库',
        project_state_id="gin-gorm-todo-app-main",
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Use environment variables for database connection configuration",
        description="Modify the project to use environment variables for configuring the database connection",  # noqa: E501
    )
    task_step = TaskStep(
        title="Modify Config/Database.go",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Update database configuration to use environment variables',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='Config/Database.go',
            detailed_requirement="Use os.Getenv to retrieve the following environment variables: DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, and DB_PORT. Update the database connection setup to use these variables.",  # noqa: E501
            references=["""
            # DB Setup instructions in README.MD\n##相关文件内容展示\n<FILE_PATH>Config/Database.go</FILE_PATH>
            """]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
