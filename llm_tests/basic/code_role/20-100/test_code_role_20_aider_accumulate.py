import pytest

from heracles.agent_roles.code_role import Code<PERSON><PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 20_aider_accumulate',
        description='Implement Accumulate Operation',
        project_state_id="exercism-python-main-accumulate"
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Implement Accumulate Operation",
        description="Implement the `accumulate` operation, which takes a collection and applies an operation to each element, returning a new collection with the results, without using collect/map/fmap functionalities.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Implement and test accumulate operation",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Implement accumulate and apply operation to collection',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='accumulate.py',
            detailed_requirement="Implement the `accumulate` operation, which processes each element of a given collection using a provided operation. The operation should apply to each element individually and return a new collection. The example provided involves squaring numbers in a list: [1, 2, 3, 4, 5] -> [1, 4, 9, 16, 25]. Ensure not to use any standard library methods like collect/map/fmap.",  # noqa: E501
            references=["Test with a collection of numbers and an operation, e.g., squaring each number."]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
