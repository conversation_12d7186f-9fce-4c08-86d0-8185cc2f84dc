import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject, CommandActionObject, CommandLifetimeType

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole:Next.js RAGBot Starter/modify .env.example file',
        description='修改 Next.js 项目 RAGBot Starter 中的 .env.example 文件',
        project_state_id='ragbot-starter-main'
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Change `ASTRA_DB_REGION` to `ASTRA_DB_API_ENDPOINT` in the `.env.example` file",
        description="A task to rename the environment variable `ASTRA_DB_REGION` to `ASTRA_DB_API_ENDPOINT` in the `.env.example` file",
    )
    task_step = TaskStep(
        title="Modify the .env.example file to rename `ASTRA_DB_REGION` to `ASTRA_DB_API_ENDPOINT`"
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score


@pytest.mark.asyncio
async def test_code_run_add(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Add a new file and the content is "test"',
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path='test.txt',
            detailed_requirement='the content is "test"'
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='remove .vercel-link class',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='app/globals.css',
            detailed_requirement='remove .vercel-link class'
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
async def test_code_run_command(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Copy example file to create environment variable configuration file',
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command='cp .env.example .env',
            lifetime=CommandLifetimeType.SHORT,
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
async def test_code_run_command_failed_regenerate(create_role, create_test_basic_data):
    """当 command 执行失败时，会尝试重新生成 command 并重试"""

    task_action = TaskAction(
        title='Copy example file to create environment variable configuration file',
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command='yarn install',
            lifetime=CommandLifetimeType.SHORT,
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
@pytest.mark.parametrize(
    'model_name',
    [
        'litellm_proxy/gemini-2.5-pro-preview',
        'litellm_proxy/gemini-2.5-flash-preview',
        'litellm_proxy/claude-3.7-sonnet-think',
    ],
)
@pytest.mark.parametrize('env_names', [['LLM_REASONING_MODEL_OPTIONAL', 'LLM_FAST_MODEL_OPTIONAL']])
async def test_code_image(create_role, create_test_basic_data, monkeypatch, env_names, model_name):
    for env_name in env_names:
        monkeypatch.setenv(env_name, model_name)
    task_action = TaskAction(
        title='create a svg file similar to user provided image, save it into app/new.svg',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='app/new.svg',
            detailed_requirement='create a svg file similar to user provided image, save it into app/new.svg',
            references=[
                'image_url://https://clacky.ai/_astro/avatar_clackyai_team.Dlm9_uny.svg',
            ],
        ),
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
async def test_code_no_image_real_case(create_role, create_test_basic_data, monkeypatch):
    monkeypatch.setenv('LLM_REASONING_MODEL_OPTIONAL', 'litellm_proxy/gemini-2.5-pro-preview')
    monkeypatch.setenv('LLM_FAST_MODEL_OPTIONAL', 'litellm_proxy/gemini-2.5-flash-preview')

    task_action = TaskAction(
        title='remove .vercel-link class',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='app/globals.css',
            detailed_requirement='remove .vercel-link class',
            references=[],
        ),
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
async def test_code_image_real_case(create_role, create_test_basic_data, monkeypatch):
    monkeypatch.setenv('LLM_REASONING_MODEL_OPTIONAL', 'litellm_proxy/gemini-2.5-pro-preview')
    monkeypatch.setenv('LLM_FAST_MODEL_OPTIONAL', 'litellm_proxy/gemini-2.5-flash-preview')

    task_action = TaskAction(
        title='create a svg file similar to user provided image, save it into app/new.svg',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='app/new.svg',
            detailed_requirement='create a svg file similar to user provided image, save it into app/new.svg',
            references=[
                'image_url://https://clacky.ai/_astro/avatar_clackyai_team.Dlm9_uny.svg',
            ],
        ),
    )
    await run_test(create_role, create_test_basic_data, task_action)

@pytest.mark.asyncio
async def test_code_image_local(create_role, create_test_basic_data, monkeypatch):
    task_action = TaskAction(
        title='remove .vercel-link class',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='app/globals.css',
            detailed_requirement='remove .vercel-link class',
            references=[
                'image_url://test.jpg',
                'image_url://test.png',
                'image_url://test.webp',
                'image_url://test.gif',
            ],
        ),
    )
    await run_test(create_role, create_test_basic_data, task_action)
