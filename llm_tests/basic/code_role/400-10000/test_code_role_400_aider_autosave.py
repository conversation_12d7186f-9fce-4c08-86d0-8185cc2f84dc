import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 400_aider_autosave',
        description='Refactor AutosaveForPlugin.get_files_to_recover',
        project_state_id='refactor-benchmark-autosave_AutosaveForPlugin_get_files_to_recover'
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Refactor AutosaveForPlugin.get_files_to_recover",
        description="Refactor the `get_files_to_recover` method in the `AutosaveForPlugin` class into a stand-alone, top-level function and update any calls to this method accordingly.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Refactor and update references",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Refactor get_files_to_recover and update method calls',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='autosave.py',
            detailed_requirement="Refactor the `get_files_to_recover` method in the `AutosaveForPlugin` class into a stand-alone function named `get_files_to_recover`. Update all existing `self.get_files_to_recover` references to use the new function.",  # noqa: E501
            references=["Ensure the new function is named `get_files_to_recover` and all references are updated."]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
