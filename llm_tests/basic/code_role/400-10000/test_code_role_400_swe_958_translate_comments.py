import pytest


from heracles.agent_roles.code_role import Code<PERSON><PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 400_swe_958',
        description='Translate all the code comments to Chinese',
        project_state_id='pydicom__pydicom-958'
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Translate all the code comments to Chinese",
        description="Translate all the code comments for functions/classes to Chinese.",
    )
    task_step = TaskStep(
        title="Translate all code comments in `pydicom/charset.py` to Chinese"
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_add_comments(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Translate all the code comments to Chinese',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='pydicom/charset.py',
            detailed_requirement="Translate all the code comments for functions/classes to Chinese.",
            references=[]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
