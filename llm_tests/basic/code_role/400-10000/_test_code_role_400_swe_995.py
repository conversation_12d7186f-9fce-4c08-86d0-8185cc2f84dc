import pytest


from heracles.agent_roles.code_role import Code<PERSON><PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule

async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: test_code_rule_400_swe_995',
        description='LUT Descriptor values do not follow standard',
        project_state_id='pydicom__pydicom-995'
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Update Dataset.pixel_array behavior",
        description="Ensure that Dataset.pixel_array returns a consistent numpy array unless the PixelData, Rows, Columns, or Samples Per Pixel values change",  # noqa: E501
    )
    task_step = TaskStep(
        title="Update the pixel array calculation"
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='using a dict of id values of pixel related elements rather than just (Float/Double Float) Pixel Data',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='pydicom/dataset.py',
            target='',
            detailed_requirement="""
Currently ds.pixel_array produces a numpy array that depends on element values for Rows, Columns, Samples Per Pixel, etc, however the code for ds.pixel_array only changes the returned array if the value for ds.PixelData changes. This may lead to confusion/undesirable behaviour if the values for related elements are changed after ds.pixel_array is called but not the underlying pixel data.

I can't think of any real use cases except maybe in an interactive session when debugging a non-conformant dataset, but I suggest we change the way Dataset._pixel_id is calculated so that it takes into account changes in related elements as well.
""",  # noqa: E501
            references=[]
            )
    )
    await run_test(create_role, create_test_basic_data, task_action)
