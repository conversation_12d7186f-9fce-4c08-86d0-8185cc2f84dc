import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 400_aider_check_autocomplete',
        description='Refactor BaseModelAdminChecks._check_autocomplete_fields_item',
        project_state_id='refactor-benchmark-checks_BaseModelAdminChecks__check_autocomplete_fields_item'
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Refactor BaseModelAdminChecks._check_autocomplete_fields_item",
        description="Refactor the `_check_autocomplete_fields_item` method in the `BaseModelAdminChecks` class into a stand-alone, top-level function and update all calls to this method accordingly.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Refactor and update references",
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Refactor _check_autocomplete_fields_item and update method calls',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='checks.py',
            detailed_requirement="Refactor the `_check_autocomplete_fields_item` method from the `BaseModelAdminChecks` class into a stand-alone function named `_check_autocomplete_fields_item`. Update all existing `self._check_autocomplete_fields_item` references to use the new function.",  # noqa: E501
            references=["Ensure the new function is named `_check_autocomplete_fields_item` and all references are updated accordingly."]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
