import pytest


from heracles.agent_roles.code_role import Code<PERSON><PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


async def run_test(create_role, create_test_basic_data, task_action):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 400_swe_958',
        description='Encoding to ISO 2022 IR 159 does not work',
        project_state_id='pydicom__pydicom-958'
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Fix encoding issue for ISO 2022 IR 159",
        description="Ensure that encoding to ISO 2022 IR 159 works correctly when 'ISO 2022 IR 159' is passed to `pydicom.charset.convert_encodings`.",  # noqa: E501
    )
    task_step = TaskStep(
        title="Update charset conversion functions"
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score

@pytest.mark.asyncio
@pytest.mark.unstable(reason='容易产生无关修改')
async def test_code_run_modify(create_role, create_test_basic_data):
    task_action = TaskAction(
        title='Fix charset conversion',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='pydicom/charset.py',
            detailed_requirement="Ensure `convert_encodings` function can handle 'ISO 2022 IR 159' properly for encoding. \n`ISO 2022 IR 159` map item in `python_encoding` is not a valid encoding. need to be fixed.",  # noqa: E501
            references=[]
        )
    )
    await run_test(create_role, create_test_basic_data, task_action)
