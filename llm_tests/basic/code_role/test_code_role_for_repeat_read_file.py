import pytest
import json
from typing import cast

from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject, ActionStatus


@pytest.mark.asyncio
async def test_code_run_modify_repeat_read_file(create_role, create_test_basic_data, mocker):
    basic_data = {
        'title': 'CodeRole: fix repeat file - run_modify',
        'description': 'Add batch add/delete operations and data export capabilities to the user module.',
        'project_state_id': 'java-master-main',
    }

    task = Task(
        title='Implement Batch Operations and Data Export for User Module',
        description=(
            'Enhance the user module by adding features for administrators or users '
            'to perform batch addition and deletion of users, as well as export user data '
            'in a common format like CSV.'
        ),
    )

    # Step 1: Implement Batch Add/Delete Functionality
    task_step1 = TaskStep(
        title='Implement Backend Logic for Batch User Operations',
        description='Develop the backend API endpoints and service logic required to handle batch creation and deletion of users.',
    )

    s1_action1 = TaskAction(
        title='Modify UserController for Batch Operations',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='src/main/java/com/company/project/web/UserController.java',
            detailed_requirement=(
                '1. Add new endpoints (e.g., POST /users/batch and DELETE /users/batch) to handle batch requests.'
                '2. Define request body structures for batch add (list of user objects) and batch delete (list of user IDs).'
                '3. Implement request validation for the batch operations.'
                '4. Delegate the core logic to the UserService.'
                '5. Handle potential errors and return appropriate HTTP responses (e.g., 200 OK, 207 Multi-Status, 400 Bad Request).'
                '6. Use the `batchAddUsers(List<User> users)` and `batchDeleteUsers(List<Long> userIds)` methods of the `UserServiceImpl` class to implement the batch operations.'  # noqa: E501
                '7. Assume that the UserService and UserServiceImpl classes and methods you need to modify or create already exist.'
            ),
            references=[
                'Add POST /users/batch endpoint',
                'Add DELETE /users/batch endpoint',
                'Call UserService methods for batch processing',
                'file://src/main/java/com/company/project/service/UserService.java',
                'file://src/main/java/com/company/project/service/impl/UserServiceImpl.java',
            ],
        ),
    )

    s1_action2 = TaskAction(
        title='Modify UserService and UserServiceImpl for Batch Logic',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='src/main/java/com/company/project/service/UserService.java',
            detailed_requirement=(
                '1. Add interface methods `batchAddUsers(List<User> users)` and `batchDeleteUsers(List<Long> userIds)`.'
                '2. The interface definition should be appropriate for the usage in the `UserController` class.'
            ),
            references=[
                'Add interface methods `batchAddUsers(List<User> users)` to the `UserService` interface.',
                'Add interface methods `batchDeleteUsers(List<Long> userIds)` to the `UserService` interface.',
                'file://src/main/java/com/company/project/web/UserController.java',
            ],
        ),
    )

    s1_action3 = TaskAction(
        title='Modify UserServiceImpl for Batch Logic',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='src/main/java/com/company/project/service/impl/UserServiceImpl.java',
            detailed_requirement=(
                '1. Add the implementation of the methods in the `UserServiceImpl` class that are defined in the `UserService` interface.'
                '2. The implementation should be appropriate for the definition in the `UserService` interface.'
            ),
            references=[
                'Implement the `batchAddUsers(List<User> users)` method defined in the `UserService` interface.',
                'Implement the `batchDeleteUsers(List<Long> userIds)` method defined in the `UserService` interface.',
                'file://src/main/java/com/company/project/service/UserService.java',
                'file://src/main/java/com/company/project/web/UserController.java',
            ],
        ),
    )

    task.add_task_step(task_step1)
    task_step1.add_task_action(s1_action1)
    task_step1.add_task_action(s1_action2)
    task_step1.add_task_action(s1_action3)

    test_basic_data = create_test_basic_data(**basic_data)

    code_role, context = await create_role(test_basic_data, CodeRole)

    context.workspace.set_task(task)

    all_actions = [a for s in task.task_steps for a in s.task_actions]

    original_function_call = code_role._function_call
    repeat_read_files = []

    for action in all_actions[:]:
        mock_function_call = mocker.patch.object(code_role, '_function_call', wraps=original_function_call)
        result = await code_role.run(action)
        action.result = result.result
        action.status = ActionStatus.COMPLETED_STATUS
        action_object = cast(FileActionObject, action.action_object)
        action_references = [r.replace('file://', '') for r in action_object.references] + [action_object.path]
        for call in mock_function_call.call_args_list:
            fn = call.args[0].function.name
            if fn == 'read_file':
                arg0 = json.loads(call.args[0].function.arguments).get('path')
                if arg0 in action_references:
                    repeat_read_files.append(arg0)

        # assert len(repeat_readed_files) <= len(all_actions) / 2 + 1, f'Repeat read file {len(repeat_readed_files)}: {repeat_readed_files}'
    # 重复读取文件次数 3 2 1 1 2 2 3 . . . ->  1 1 2 2 1 . . . . .
    assert len(repeat_read_files) <= 2, f'Repeat read file {len(repeat_read_files)}: {repeat_read_files}'
