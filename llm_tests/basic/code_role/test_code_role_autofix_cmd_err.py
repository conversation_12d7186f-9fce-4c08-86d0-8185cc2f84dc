import pytest


from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, CommandActionObject, CommandLifetimeType

from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule

@pytest.mark.asyncio
async def test_code_run_command_failed_regenerate(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 自动修复命令与AI交互terminal',
        description='修改 Next.js 项目 RAGBot Starter 中的 .env.example 文件',
        project_state_id='blank-project-main'
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Change `ASTRA_DB_REGION` to `ASTRA_DB_API_ENDPOINT` in the `.env.example` file",
        description="A task to rename the environment variable `ASTRA_DB_REGION` to `ASTRA_DB_API_ENDPOINT` in the `.env.example` file",
    )
    task_step = TaskStep(
        title="Modify the .env.example file to rename `ASTRA_DB_REGION` to `ASTRA_DB_API_ENDPOINT`"
    )
    task_action = TaskAction(
        title='Copy example file to create environment variable configuration file',
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command='npx shadcn-ui@latest add card button',
            lifetime=CommandLifetimeType.SHORT,
        )
    )
    task.add_task_step(task_step)
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score


@pytest.mark.asyncio
async def test_code_run_command_check(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 判断命令是否执行成功1',
        description='修改 Next.js 项目 RAGBot Starter 中的 .env.example 文件',
        project_state_id='blank-project-main'
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    cmd = "npx shadcn@latest add card button"
    cmd_output = """
Need to install the following packages:
  shadcn@2.5.0
Ok to proceed? (y) y(⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠋ idealTree:d66c5096c7023bfb: sill(⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠋ idealTree:d66c5096c7023bfb: sill(⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠋ idealTree:d66c5096c7023bfb: sill(⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠸ idealTree:d66c5096c7023bfb: sill(⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠙ idealTree:d66c5096c7023bfb: sill(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠴ idealTree:d66c5096c7023bfb: timi(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠴ idealTree:d66c5096c7023bfb: timi(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠏ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠏ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠏ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠴ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠏ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠧ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠦ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠦ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠦ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠦ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠦ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠇ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠋ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠏ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠏ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠸ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠹ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠧ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠹ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠇ idealTree:prompts: sill fetch ma(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠹ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠸ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠇ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠙ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠹ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠋ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠼ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠼ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠼ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠦ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠋ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠸ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠙ idealTree:npm-run-path: sill pla(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠦ idealTree:npm-run-path: sill pla(######⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠼ idealTree:wrap-ansi: sill fetch(#######⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠸ idealTree:prompts: timing idealT(#######⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠼ idealTree:prompts: timing idealT(#######⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠇ idealTree:prompts: timing idealT(#######⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠴ idealTree:prompts: timing idealT(#######⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠸ idealTree:prompts: timing idealT(#######⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠼ idealTree:prompts: timing idealT(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠧ idealTree:wrap-ansi: timing ideanpm WARN EBADENGINE Unsupported engine {
npm WARN EBADENGINE   package: 'mute-stream@2.0.0',
npm WARN EBADENGINE   required: { node: '^18.17.0 || >=20.5.0' },
npm WARN EBADENGINE   current: { node: 'v20.1.0', npm: '9.6.4' }
npm WARN EBADENGINE }
(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠙ idealTree: timing idealTree Comp(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠙ idealTree: timing idealTree Comp(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠼ reify:shadcn: sill audit bulk re(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠼ reify:shadcn: sill audit bulk re(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠹ reify:shadcn: sill audit bulk re(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠧ reify:shadcn: sill audit bulk re(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠹ reify:shadcn: sill audit bulk re(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠹ reify:shadcn: sill audit bulk re(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠹ reify:shadcn: sill audit bulk re(#########⠂⠂⠂⠂⠂⠂⠂⠂⠂) ⠙ reify:querystringify: http fetch(##########⠂⠂⠂⠂⠂⠂⠂⠂) ⠹ reify:get-own-enumerable-keys: h(##########⠂⠂⠂⠂⠂⠂⠂⠂) ⠙ reify:lines-and-columns: http fe(###########⠂⠂⠂⠂⠂⠂⠂) ⠹ reify:mute-stream: http fetch GE(###########⠂⠂⠂⠂⠂⠂⠂) ⠹ reify:is-node-process: timing re(############⠂⠂⠂⠂⠂⠂) ⠦ reify:@bundled-es-modules/status(#############⠂⠂⠂⠂⠂) ⠇ reify:npm-run-path: http fetch G(#############⠂⠂⠂⠂⠂) ⠼ reify:onetime: http fetch GET 20(##############⠂⠂⠂⠂) ⠧ reify:@babel/helper-string-parse(###############⠂⠂⠂) ⠹ reify:nanoid: timing reifyNode:n(################⠂⠂) ⠇ reify:https-proxy-agent: timing npm WARN deprecated node-domexception@1.0.0: Use your platform's native DOMException instead
(#################⠂) ⠏ reify:node-fetch: http fetch GET(#################⠂) ⠙ reify:cosmiconfig: http fetch GE(#################⠂) ⠙ reify:js-yaml: timing reifyNode:(#################⠂) ⠴ reify:@antfu/ni: http fetch GET(#################⠂) ⠹ reify:tsconfig-paths: timing rei(##################) ⠇ reify:ast-types: http fetch GET(##################) ⠋ reify:type-fest: http fetch GET(##################) ⠹ reify:@babel/core: http fetch GE(##################) ⠼ reify:@babel/helpers: http fetch(##################) ⠇ reify:@babel/types: http fetch G(##################) ⠇ reify:@babel/types: http fetch G(##################) ⠦ reify:zod: http fetch GET 200 ht(##################) ⠦ reify:zod: http fetch GET 200 ht(##################) ⠇ reify:msw: http fetch GET 200 ht(##################) ⠸ reify:caniuse-lite: http fetch G(##################) ⠏ reify:caniuse-lite: http fetch G(##################) ⠦ reify:caniuse-lite: http fetch G? The path /home/<USER>/app does not contain a package.json file.
  Would you like to start a new project? › - Use arrow-keys. Return to submit.
❯   Next.js
    Next.js (Monorepo)
✔ The path /home/<USER>/app does not contain a package.json file.
  Would you like to start a new project? › Next.js
? What is your project named? › my-app78✔ What is your project named? … my-app78
⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.⠹ Creating a new Next.js project. This may take a few minutes.⠸ Creating a new Next.js project. This may take a few minutes.⠼ Creating a new Next.js project. This may take a few minutes.⠴ Creating a new Next.js project. This may take a few minutes.⠦ Creating a new Next.js project. This may take a few minutes.⠧ Creating a new Next.js project. This may take a few minutes.⠇ Creating a new Next.js project. This may take a few minutes.⠏ Creating a new Next.js project. This may take a few minutes.⠋ Creating a new Next.js project. This may take a few minutes.⠙ Creating a new Next.js project. This may take a few minutes.✔ Creating a new Next.js project.
? Which color would you like to use as the base color? › - Use arrow-keys. Return to submit.
❯   Neutral
    Gray
    Zinc
    Stone
    Slate
✔ Which color would you like to use as the base color? › Neutral
⠋ Writing components.json.✔ Writing components.json.
⠋ Checking registry.✔ Checking registry.
⠋ Installing dependencies.  Installing dependencies.
It looks like you are using React 19.
Some packages may fail to install due to peer dependency issues in npm (see https://ui.shadcn.com/react-19).
? How would you like to proceed? › - Use arrow-keys. Return to submit.
❯   Use --force
    Use --legacy-peer-deps
✔ How would you like to proceed? › Use --force
⠋ Installing dependencies.⠙ Installing dependencies.⠹ Installing dependencies.⠸ Installing dependencies.⠼ Installing dependencies.⠴ Installing dependencies.⠦ Installing dependencies.⠧ Installing dependencies.⠇ Installing dependencies.⠏ Installing dependencies.⠋ Installing dependencies.⠙ Installing dependencies.⠹ Installing dependencies.⠸ Installing dependencies.⠼ Installing dependencies.⠴ Installing dependencies.✔ Installing dependencies.
⠋ Updating files.✔ Created 2 files:
  - components/ui/card.tsx
  - components/ui/button.tsx
""" # noqa

    success, reason = await code_role.analyze_cmd_result(cmd, cmd_output)
    assert success is True
