import pytest

from heracles.agent_roles.code_role import CodeR<PERSON>
from heracles.core.schema.task import ActionType, Task, TaskStep, TaskAction, FileActionObject
from llm_tests.basic.score_rule.code_role_score_rule import CodeRoleScoreRule


@pytest.mark.asyncio
async def test_code_run_modify(create_role, create_test_basic_data):
    test_basic_data = create_test_basic_data(
        title='CodeRole: 10000_swe_22711',
        description='Fix RangeSlider initialization value error',
        project_state_id='matplotlib__matplotlib-22711'
    )

    code_role, context = await create_role(test_basic_data, CodeRole)

    task = Task(
        title="Fix RangeSlider initialization value error",
        description="""Fix an IndexError in the RangeSlider widget when setting initial values. The error occurs due to attempting to access index 4 of poly.xy array which only has size 4. This needs to be fixed by properly handling the polygon vertices in the set_val method.

### Pre Analysis Report
Based on the analysis of the code and requirements, here are the key points:

1. Location of issue:
- Main file: `[lib/matplotlib/widgets.py](lib/matplotlib/widgets.py)`
- Issue occurs in RangeSlider.set_val() method where polygon vertices are incorrectly updated

2. Issue details:
- IndexError happens when accessing 5th vertex in poly.xy array which only has 4 vertices
- Current implementation incorrectly tries to set a redundant fifth vertex
- The polygon should be closed with just 4 vertices - no need for explicit 5th vertex

3. Files requiring changes:
- lib/matplotlib/widgets.py - Need to modify RangeSlider.set_val() method
- lib/matplotlib/tests/test_widgets.py - Need to add regression test

4. Testing requirements:
- Need to add test case that verifies proper initialization with valinit
- Test should check both horizontal and vertical orientations
- Focus on validating polygon vertex updates

5. Test strategy:
- Use existing test infrastructure from test_widgets.py
- Add test case specifically for RangeSlider initialization scenario
- Test should ensure no IndexError occurs
- Verify proper polygon vertex coordinates after initialization

6. Implementation guidelines:
- Remove redundant fifth vertex assignment
- Keep existing four vertices for proper polygon closure
- Update documentation to clarify polygon vertex structure
- Handle both horizontal and vertical orientations consistently

7. Project constraints:
- Must maintain backward compatibility with existing RangeSlider usage
- Follow project's testing conventions
- Maintain core slider widget functionality while fixing vertex issue

8. Quality checks:
- Run existing test suite to ensure no regressions
- Add new test case specifically for initialization
- Follow project code style and documentation standards
""",  # noqa: E501
    )
    task_step = TaskStep(
        title="Fix RangeSlider.set_val() method to properly handle polygon vertices"
    )
    task.add_task_step(task_step)
    task_action = TaskAction(
        title='Fix RangeSlider initialization value error',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='lib/matplotlib/widgets.py',
            detailed_requirement="""- Find the RangeSlider.set_val() method in the file
- Remove the redundant fifth vertex assignment in the method
- Ensure the polygon is properly closed with just four vertices
- Update comments to clarify the polygon vertex structure
- Make sure the fix handles both horizontal and vertical orientations correctly""",  # noqa: E501
            references=[]
        )
    )
    task_step.add_task_action(task_action)

    context.workspace.set_task(task)
    result = await code_role.run(task_action)
    task_action.result = result.result

    final_score = await CodeRoleScoreRule(code_role.workspace, task_action).execute_rules_and_score("all")
    context.score_model = final_score
    return final_score
