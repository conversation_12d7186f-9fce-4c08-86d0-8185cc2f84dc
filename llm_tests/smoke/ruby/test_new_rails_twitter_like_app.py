# -*- coding: UTF-8 -*-
import pytest


@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_rails_twitter_like_app(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: Twitter-like APP",
        description="",
        project_state_id="blank-project-ruby-main",
        goal="Develop a twitter-like system",
        goal_detail="""- based on latest rails 7 + bootstrap + SQLite
- no need for authentication, just for demo
        """,
        middlewares=['postgres 15.0'],
        expectations=[
        ],
    )

    await run_whole_process(test_data)
