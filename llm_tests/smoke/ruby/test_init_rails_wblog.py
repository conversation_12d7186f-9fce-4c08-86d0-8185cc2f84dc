import pytest

from llm_tests.utils.schema import FileContentAssert


@pytest.mark.asyncio
async def test_init_rails_wblog(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="Smoke: Ruby/rails_wblog/init",
        description="",
        project_state_id="wblog-main",
        goal="Initialize the Development Environment",
        expectations=[
            FileContentAssert(file_path='.1024', content='rails s'),
            FileContentAssert(file_path='.gitignore', content='config/database.yml'),
            FileContentAssert(file_path='.gitignore', content='config/application.yml'),
            FileContentAssert(file_path='.gitignore', content='.1024*'),
            FileContentAssert(file_path='.gitignore', content='!.1024'),
            "规划了 Action 运行 bundle install",
            "规划了 Action 对 `config/application.yml.example` 和 `config/database.yml.example` 文件进行复制",
            "规划了 Action 对 `config/database.yml` 文件进行了必要的修改",
        ],
    )

    await run_whole_process(test_data)
