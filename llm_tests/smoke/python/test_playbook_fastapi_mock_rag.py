import pytest
from heracles.core.logger import heracles_logger as logger
from heracles.core.schema import FileSnippet
from heracles.agent_workspace.tools import Tools

playbook_content = r"""
Playbook: Add a read api to FastAPI app

#### Overview
Add a read api to FastAPI app

#### Procedure
1. Modify entry file e.g. main.py or app.py, and add the api route using fastapi decorator like @app.{method}("{route}").
2. Modify api file e.g. api.py or router.py, and implement the function the api should call, define params types in the api folder. Follow original files styles and indent.
3. Add or Update pydantic Model of related data, if there is some file like xxx.json containing data you need, transform the json into pydantic model. DO NOT add fields don't existed, if not sure add Optional typing.
4. Check imports, syntax, indent and any possible error which will cause linter and execution failed, fix them.
5. Read test files and consider if there is any code that will fail in test, fix them.

#### Example
```
diff --git a/app/api/api.py b/app/api/api.py
index b0d7c36..af12301 100644
--- a/app/api/api.py
+++ b/app/api/api.py
@@ -7,6 +7,11 @@ def read_user():

     return users

+def read_car():
+    with open('data/cars.json') as stream:
+        cars = json.load(stream)
+
+    return cars

 def read_questions(position: int):
     with open('data/questions.json') as stream:
diff --git a/app/main.py b/app/main.py
index 09ca0ff..2c9dff5 100644
--- a/app/main.py
+++ b/app/main.py
@@ -16,6 +16,9 @@ def root():
 def read_user():
     return api.read_user()

+@app.get("/car")
+def read_car():
+    return api.read_car()

 @app.get("/question/{position}", status_code=200)
 def read_questions(position: int, response: Response):
```
Above is extracted from git diff command of an example of adding an api to fastapi project, learn and analyze the example and apply useful parts on your task.
"""  # noqa: E501


@pytest.mark.asyncio
async def test_playbook_fastapi_mock_rag(run_whole_process, create_test_data, mocker):
    async def mock_search_related_code(self, question, need_expand: bool = True):
        return [
            FileSnippet(
                path='app/main.py',
                content='from fastapi import FastAPI, HTTPException\nfrom starlette.responses import Response\n\nfrom app.db.models import UserAnswer\nfrom app.api import api\n\napp = FastAPI()\n\n\<EMAIL>("/")\ndef root():\n    return {"message": "Fast API in Python"}\n\n\<EMAIL>("/user")\ndef read_user():\n    return api.read_user()',  # noqa: E501
                row_start=1,
                row_end=17,
            ),
            FileSnippet(
                path='app/api/api.py',
                content="def read_user():\n    with open('data/users.json') as stream:\n        users = json.load(stream)\n\n    return users",  # noqa: E501
                row_start=4,
                row_end=8,
            ),
            FileSnippet(
                path='app/db/models.py',
                content='from pydantic import BaseModel\nfrom typing import List\n\n\nclass Answer(BaseModel):\n    question_id: int\n    alternative_id: int',  # noqa: E501
                row_start=1,
                row_end=7,
            ),
            FileSnippet(
                path='app/data/cars.json',
                content='  {\n    "id": 1,\n    "name": "Volkswagen ID.3",\n    "fuel": "electric",\n    "price": "low",\n    "category": "compact",\n    "link": ""\n  },',  # noqa: E501
                row_start=2,
                row_end=9,
            ),
        ]

    mocker.patch.object(Tools, 'search_related_code', mock_search_related_code)

    test_data = create_test_data(
        title='Playbook fastapi test',
        description='Playbook fastapi test',
        codezone_id='741120140736954368',
        codezone_description='Python fastapi 项目',
        codezone_git_url='https://github.com/marciovrl/fastapi.git',
        goal='Add an api to fastapi app',
        goal_detail='Add read_cars api which return all cars data, route should be "/car", method should be GET, and update cars data model',  # noqa: E501
        expect='',
    )

    context = await run_whole_process(test_data)
    is_test_success = True
    try:
        cmd = 'pytest test/__custom_test.py'
        res = await context.workspace.playground.func_call('agent_terminal_with_result', cmd)
        logger.info(res)
    except Exception as e:
        is_test_success = False
        logger.error(e)
    finally:
        assert is_test_success
