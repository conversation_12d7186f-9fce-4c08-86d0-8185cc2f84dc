import pytest
import asyncio
from heracles.core.logger import heracles_logger as logger


@pytest.mark.asyncio
async def test_playbook_programming_problem_2(run_whole_process, create_test_data, mocker):
    test_data = create_test_data(
        title='Playbook programming problem 2',
        description='Playbook programming problem 2',
        codezone_id='741120140736954368',
        codezone_description='Python fastapi 项目',
        codezone_git_url='https://github.com/marciovrl/fastapi.git',
        goal='You are a clever programer, read below problem and Modify app/programming_problem_2.py to solve it',
        goal_detail="""
Find Minimum Diameter After Merging Two Trees

There exist two undirected trees with n and m nodes, numbered from 0 to n - 1 and from 0 to m - 1, respectively. You are given two 2D integer arrays edges1 and edges2 of lengths n - 1 and m - 1, respectively, where edges1[i] = [ai, bi] indicates that there is an edge between nodes ai and bi in the first tree and edges2[i] = [ui, vi] indicates that there is an edge between nodes ui and vi in the second tree.

You must connect one node from the first tree with another node from the second tree with an edge.

Return the minimum possible diameter of the resulting tree.

The diameter of a tree is the length of the longest path between any two nodes in the tree.


Example 1:

Input: edges1 = [[0,1],[0,2],[0,3]], edges2 = [[0,1]]

Output: 3

Explanation:

We can obtain a tree of diameter 3 by connecting node 0 from the first tree with any node from the second tree.

Example 2:


Input: edges1 = [[0,1],[0,2],[0,3],[2,4],[2,5],[3,6],[2,7]], edges2 = [[0,1],[0,2],[0,3],[2,4],[2,5],[3,6],[2,7]]

Output: 5

Explanation:

We can obtain a tree of diameter 5 by connecting node 0 from the first tree with node 0 from the second tree.


Constraints:

1 <= n, m <= 105
edges1.length == n - 1
edges2.length == m - 1
edges1[i].length == edges2[i].length == 2
edges1[i] = [ai, bi]
0 <= ai, bi < n
edges2[i] = [ui, vi]
0 <= ui, vi < m
The input is generated such that edges1 and edges2 represent valid trees.

You should implement as efficient as possible.
Your solution should fill below template codes where the return value is your output.
```
class Solution:
    def minimumDiameterAfterMerge(self, edges1: List[List[int]], edges2: List[List[int]]) -> int:
```
""",  # noqa: E501
        expect='',
        proposed_list=['DO NOT read file in test/ folder or start with test_'],
    )

    context = await run_whole_process(test_data)
    is_test_success = True
    try:
        cmd = 'pytest test/__test_programming_problem_2.py'
        async with asyncio.timeout(120):
            res = await context.workspace.playground.func_call('agent_terminal_with_result', cmd, soft_timeout=120)
            logger.info(res)
    except Exception as e:
        is_test_success = False
        logger.error(e)
    finally:
        assert is_test_success
