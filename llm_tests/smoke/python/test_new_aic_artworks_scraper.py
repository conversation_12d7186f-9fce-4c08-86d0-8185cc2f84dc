import pytest


@pytest.mark.asyncio
@pytest.mark.build_from_scratch
@pytest.mark.unstable(reason="tailwindcss@4")
async def test_new_aic_artworks_scraper(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: artworks scraper for AIC",
        description="",
        project_state_id="blank-project-python-main",
        goal="Build a artworks scraper for AIC with web interface using FastAPI",
        goal_detail="""- Scrape the Art Institute of Chicago website, with API(https://api.artic.edu/docs/#introduction)
- Save the scraped data to MongoDB
- Create a simple web interface at root path `/` to start scraping, monitor progress, and display results
- Use Tailwind CSS for styling with CDN""",
        middlewares=['mongo 5.0'],
    )

    await run_whole_process(test_data)
