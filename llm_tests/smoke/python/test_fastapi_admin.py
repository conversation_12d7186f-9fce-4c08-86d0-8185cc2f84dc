# -*- coding: UTF-8 -*-
import pytest
from llm_tests.utils.schema import FileContentAssert

@pytest.mark.init
@pytest.mark.asyncio
@pytest.mark.unstable(role="paas", reason="当前 paas 并不支持在 docker 中运行 docker")
@pytest.mark.skip(reason="当前 paas 并不支持在 dokcer 中运行 docker")
async def test_fastapi_admin_init(run_whole_process, create_test_data):
    """完整项目: fastapi_admin 初始化环境 (python)"""

    test_data = create_test_data(
        title="完整项目：fastapi_admin 初始化环境",
        description="为默认使用 docker 的 python 项目 fastapi-admin 初始化开发环境",
        project_state_id="fastapi-admin-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='docker-compose up -d --build', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)
