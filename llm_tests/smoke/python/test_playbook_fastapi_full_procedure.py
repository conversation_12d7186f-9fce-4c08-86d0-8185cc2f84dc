import pytest
import asyncio
from heracles.core.logger import heracles_logger as logger

@pytest.mark.asyncio
async def test_playbook_fastapi(run_whole_process, create_test_data, mocker):
    test_data = create_test_data(
        title='Playbook fastapi test full procedure',
        description='Playbook fastapi test full procedure',
        codezone_id='741120140736954368',
        codezone_description='Python fastapi 项目',
        codezone_git_url='https://github.com/marciovrl/fastapi.git',
        goal='Add an api to fastapi app',
        goal_detail='Add read_cars api which return all cars data, route should be "/car", method should be GET, and update cars data model',  # noqa: E501
        expect='',
    )

    context = await run_whole_process(test_data)
    is_test_success = True
    try:
        cmd = 'pytest test/__custom_test.py'
        async with asyncio.timeout(120):
            res = await context.workspace.playground.func_call('agent_terminal_with_result', cmd, soft_timeout=120)
            logger.info(res)
    except Exception as e:
        is_test_success = False
        logger.error(e)
    finally:
        assert is_test_success
