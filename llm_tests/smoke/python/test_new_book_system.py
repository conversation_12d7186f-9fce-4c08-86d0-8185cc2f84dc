import pytest


@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_book_system(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: 书籍管理系统",
        description="",
        project_state_id="blank-project-python-main",
        goal="Develop a book management system using Django and SQLite",
        goal_detail="""- Book list and detail page showing the book information
- Support for categories, tags, author profiles, publication dates, and featured images
- User can mark the book as read, unread, and add to wishlist. no need to login
- Full CRUD operations: create, edit, delete, search and filter books""",
        middlewares=['postgres 15.0'],
    )

    await run_whole_process(test_data)
