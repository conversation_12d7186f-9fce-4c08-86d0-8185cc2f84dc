# -*- coding: UTF-8 -*-
import pytest


@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_hotel_order_system(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: 酒店预订系统",
        description="",
        project_state_id="blank-project-main",
        goal="开发一个包含前台预订界面和后台管理系统的酒店预订系统",
        goal_detail="""- 包含前台预订界面和后台管理系统：前台支持房型展示、在线预订、可以通过预定号查询到订单；
- 后台系统包含房型管理，预订管理，可以对预定进行状态确认""",
        expectations=[
        ],
    )

    await run_whole_process(test_data)
