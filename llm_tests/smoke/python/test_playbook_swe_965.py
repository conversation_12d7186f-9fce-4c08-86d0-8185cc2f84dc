# == SWE data pydicom__pydicom_965 problem statement:
# == Git info: pydicom/pydicom, ee775c8a137cd8e0b69b46dc24c23648c31fe34c

import pytest

problem_statement = """
Empty data elements with value representation SQ are set to None
**Describe the bug**
In the current `master`, empty data elements are not read correctly from files. The attribute value is set to `None` instead of `[]`.

**Expected behavior**
Create empty list `[]` for empty sequence, i.e., a sequence with zero items.

**Steps To Reproduce**
```python
import pydicom
ds = pydicom.Dataset()
ds.AcquisitionContextSequence = []
print(ds)
ds.is_little_endian = True
ds.is_implicit_VR = True
ds.save_as('/tmp/test.dcm')

reloaded_ds = pydicom.dcmread('/tmp/test.dcm', force=True)
print(reloaded_ds)
```
This prints:
```
(0040, 0555)  Acquisition Context Sequence   0 item(s) ----
...
TypeError: With tag (0040, 0555) got exception: object of type 'NoneType' has no len()
Traceback (most recent call last):
  File "/private/tmp/pydicom/pydicom/tag.py", line 30, in tag_in_exception
    yield
  File "/private/tmp/pydicom/pydicom/dataset.py", line 1599, in _pretty_str
    len(data_element.value)))
TypeError: object of type 'NoneType' has no len()
```

**Your environment**
```
Darwin-18.6.0-x86_64-i386-64bit
Python  3.7.3 (default, Mar 27 2019, 09:23:15)
[Clang 10.0.1 (clang-1001.0.46.3)]
pydicom  1.4.0.dev0
```
"""  # noqa: E501
test_patch = 'diff --git a/pydicom/tests/test_dataelem.py b/pydicom/tests/test_dataelem.py\n--- a/pydicom/tests/test_dataelem.py\n+++ b/pydicom/tests/test_dataelem.py\n@@ -503,6 +503,23 @@ def check_empty_binary_element(value):\n             check_empty_binary_element(MultiValue(int, []))\n             check_empty_binary_element(None)\n \n+    def test_empty_sequence_is_handled_as_array(self):\n+        ds = Dataset()\n+        ds.AcquisitionContextSequence = []\n+        elem = ds[\'AcquisitionContextSequence\']\n+        assert bool(elem.value) is False\n+        assert 0 == elem.VM\n+        assert elem.value == []\n+\n+        fp = DicomBytesIO()\n+        fp.is_little_endian = True\n+        fp.is_implicit_VR = True\n+        filewriter.write_dataset(fp, ds)\n+        ds_read = dcmread(fp, force=True)\n+        elem = ds_read[\'AcquisitionContextSequence\']\n+        assert 0 == elem.VM\n+        assert elem.value == []\n+\n \n class TestRawDataElement(object):\n     """Tests for dataelem.RawDataElement."""\n'  # noqa: E501


@pytest.mark.asyncio
async def test_playbook_swe_965(create_swe_test_data, run_swe_whole_process, mocker):

    swe_data = {
        'instance_id': 'pydicom__pydicom-965',
        'repo': 'pydicom/pydicom',
        'base_commit': 'ee775c8a137cd8e0b69b46dc24c23648c31fe34c',
        'problem_statement': problem_statement,
        'test_patch': test_patch,
        'FAIL_TO_PASS': '["pydicom/tests/test_dataelem.py::TestDataElement::test_empty_sequence_is_handled_as_array"]',  # noqa: E501
    }

    test_data = create_swe_test_data(
        title=f"swe test: {swe_data['instance_id']}",
        swe_base_commit=swe_data['base_commit'],
        description=swe_data['problem_statement'],
        codezone_id='695395448911204352',
        codezone_description=swe_data['problem_statement'],
        codezone_git_url=f"https://github.com/{swe_data['repo']}",
        goal=swe_data['problem_statement'],
        goal_detail=swe_data['problem_statement'],
        expect=swe_data['FAIL_TO_PASS'],
        swe_test_patch=swe_data['test_patch'],
    )

    context = await run_swe_whole_process(test_data)
    assert context.score_model.value == 1
