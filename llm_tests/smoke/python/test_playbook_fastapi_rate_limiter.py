import pytest
import asyncio
from heracles.core.logger import heracles_logger as logger


@pytest.mark.asyncio
async def test_playbook_fastapi_rate_limiter(run_whole_process, create_test_data, mocker):
    test_data = create_test_data(
        title='implement leaky bucket rate limiter',
        description='implement leaky bucket rate limiter',
        codezone_id='741120140736954368',
        codezone_description='Python fastapi 项目',
        codezone_git_url='https://github.com/marciovrl/fastapi.git',
        goal='implment fastapi rate limiter, DO NOT use external library, only use built-in python library and libraries in requiments.txt',
        goal_detail="""
Implement a leaky bucket rate limiter in app/rate_limiter.py file, limit the api calling rate
Replace class name YourLimiter if needed.

1 You should implement some class like below, the method can_pass should return True if calls under rate, return False if calls exceed rate.
```
class YourLimiter:
    def __init__(self) -> None:
        pass

    def can_pass(self):
        pass
```

2 modify app/main.py as below to use your rate limiter, if exceed rate just return a json with 'code' field has value 429
```
from .rate_limiter import YourLimiter

limiter = YourLimiter()

@app.get("/")
def root():
    if not limiter.can_pass():
        return { "code": 429, "message": "request too fast" }
    return { "code": 200, "message": "Fast API in Python" }
```
You should limit the rate 1 call per second using LeakyBucket or TokenBucke algorithm.
    """,  # noqa: E501
        expect='',
        proposed_list=['DO NOT read file in test/ folder or file start with test_'],
    )

    context = await run_whole_process(test_data)
    is_test_success = True
    try:
        cmd = 'pytest test/__test_rate_limiter.py'
        async with asyncio.timeout(120):
            res = await context.workspace.playground.func_call('agent_terminal_with_result', cmd, soft_timeout=120)
            logger.info(res)
    except Exception as e:
        is_test_success = False
        logger.error(e)
    finally:
        assert is_test_success
