import pytest
import asyncio
from heracles.core.logger import heracles_logger as logger


@pytest.mark.asyncio
async def test_playbook_programming_problem_1(run_whole_process, create_test_data, mocker):
    test_data = create_test_data(
        title='Playbook programming problem 1',
        description='Playbook programming problem 1',
        codezone_id='741120140736954368',
        codezone_description='Python fastapi 项目',
        codezone_git_url='https://github.com/marciovrl/fastapi.git',
        goal='You are a clever programer, read below problem and Modify app/programming_problem_1.py to solve it',
        goal_detail="""
Find Building Where <PERSON> and <PERSON> Can Meet

You are given a 0-indexed array heights of positive integers, where heights[i] represents the height of the ith building.

If a person is in building i, they can move to any other building j if and only if i < j and heights[i] < heights[j].

You are also given another array queries where queries[i] = [ai, bi]. On the ith query, <PERSON> is in building ai while <PERSON> is in building bi.

Return an array ans where ans[i] is the index of the leftmost building where <PERSON> and <PERSON> can meet on the ith query. If <PERSON> and <PERSON> cannot move to a common building on query i, set ans[i] to -1.

Example 1:

Input: heights = [6,4,8,5,2,7], queries = [[0,1],[0,3],[2,4],[3,4],[2,2]]
Output: [2,5,-1,5,2]
Explanation: In the first query, <PERSON> and <PERSON> can move to building 2 since heights[0] < heights[2] and heights[1] < heights[2].
In the second query, <PERSON> and <PERSON> can move to building 5 since heights[0] < heights[5] and heights[3] < heights[5].
In the third query, Alice cannot meet Bob since Alice cannot move to any other building.
In the fourth query, Alice and Bob can move to building 5 since heights[3] < heights[5] and heights[4] < heights[5].
In the fifth query, Alice and Bob are already in the same building.
For ans[i] != -1, It can be shown that ans[i] is the leftmost building where Alice and Bob can meet.
For ans[i] == -1, It can be shown that there is no building where Alice and Bob can meet.
Example 2:

Input: heights = [5,3,8,2,6,1,4,6], queries = [[0,7],[3,5],[5,2],[3,0],[1,6]]
Output: [7,6,-1,4,6]
Explanation: In the first query, Alice can directly move to Bob's building since heights[0] < heights[7].
In the second query, Alice and Bob can move to building 6 since heights[3] < heights[6] and heights[5] < heights[6].
In the third query, Alice cannot meet Bob since Bob cannot move to any other building.
In the fourth query, Alice and Bob can move to building 4 since heights[3] < heights[4] and heights[0] < heights[4].
In the fifth query, Alice can directly move to Bob's building since heights[1] < heights[6].
For ans[i] != -1, It can be shown that ans[i] is the leftmost building where Alice and Bob can meet.
For ans[i] == -1, It can be shown that there is no building where Alice and Bob can meet.


Constraints:

1 <= heights.length <= 5 * 104
1 <= heights[i] <= 109
1 <= queries.length <= 5 * 104
queries[i] = [ai, bi]
0 <= ai, bi <= heights.length - 1

You should implement as efficient as possible.
Your solution should fill below template codes where the return value is your output.
```
class Solution:
    def leftmostBuildingQueries(self, heights: List[int], queries: List[List[int]]) -> List[int]:

```
""",  # noqa: E501
        expect='',
        proposed_list=['DO NOT read file in test/ folder or start with test_'],
    )

    context = await run_whole_process(test_data)
    is_test_success = True
    try:
        cmd = 'pytest test/__test_programming_problem_1.py'
        async with asyncio.timeout(120):
            res = await context.workspace.playground.func_call('agent_terminal_with_result', cmd, soft_timeout=120)
            logger.info(res)
    except Exception as e:
        is_test_success = False
        logger.error(e)
    finally:
        assert is_test_success
