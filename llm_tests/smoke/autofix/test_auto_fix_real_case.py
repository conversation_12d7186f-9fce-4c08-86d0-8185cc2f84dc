import pytest
import asyncio
from heracles.core.schema.task import Task
from llm_tests.basic.score_rule.smoke_test_score_rule import SmokeTestScoreRule
from heracles.core.schema.test import Context
from heracles.core.logger import heracles_logger as logger
from llm_tests.utils.schema import ScoreModel
from heracles.core.exceptions import AgentRunException
import time


@pytest.fixture
def run_and_score(create_workspace):
    async def _run_and_score(test_data):
        context = Context()
        workspace = await create_workspace(test_data.codezone_id)
        context.playground_id = workspace.playground.playground_id
        record = {}
        record['old_codezone_id'] = test_data.codezone_id
        record['title'] = test_data.title
        record['codezone_id'] = workspace.new_codezone_id
        record['playground_id'] = context.playground_id

        task = Task(title=test_data.title, description='')
        workspace.set_task(task)
        controller = workspace.playground.agent_controller
        controller.task_state.set_state("working")

        start = time.time()
        await controller.check_and_fix_errors()
        end = time.time()
        record['time'] = end - start
        # with open('pytest_file', 'a+') as f:
        #     f.write(str(record)+"\n")

        logger.warning(f'-> step: run run_project: {test_data.title}')
        check_role = controller.check_role
        errors = workspace.smart_detect.errors

        smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)
        await workspace.smart_detect.set_status('monitoring_errors')
        try:
            await check_role.run_and_check_project()
            # 收集并分析错误信息
            await asyncio.sleep(10)
            if len(errors) > 0:
                for error in errors:
                    logger.error(f'-> step: RUN failed with error found: \nref_id: {error.ref_id}\n\n{error.content}')
                error_titles = ', '.join([error.title for error in errors])
                smoke_test_score_rule.errors.append(f"errors detected: {error_titles}")
                smoke_test_score_rule.scores.append(ScoreModel(value=0.0, comment='smoke 用例无法通过, 原因: run 日志中包含错误信息'))
            else:
                smoke_test_score_rule.scores.append(ScoreModel(value=1.0, comment='规则: 运行项目有日志输出，且没有错误信息'))
        except AgentRunException as e:
            logger.error(f'-> wait for clacky terminal log error: {e}')
            smoke_test_score_rule.errors.append(f'运行项目 10 秒超时仍没有日志输出: {e}')
            smoke_test_score_rule.scores.append(ScoreModel(value=0.0, comment='规则: 运行项目有日志输出，且没有错误信息'))
        logger.warning(f'-> step: run smoke_test_score_rule: {test_data.title}')
        context.score_model = await smoke_test_score_rule.execute_rules_and_score('all')
        logger.warning(f'-> step: end run: {test_data.title}')
        return context
    return _run_and_score

@pytest.mark.asyncio
async def test_autofix_no_error(run_and_score, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        is_web_service=True
    )

    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_hackernews_error_case_1(run_and_score, create_test_data):
    """测试hackernews-error-case-1的自动修复"""
    test_data = create_test_data(
        title="hackernews前端项目1",
        description="",
        project_state_id="hackernews-error-case-1",
        goal="Fix errors in hackernews project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_hackernews_error_case_2(run_and_score, create_test_data):
    """测试hackernews-error-case-2的自动修复"""
    test_data = create_test_data(
        title="hackernews前端项目2",
        description="",
        project_state_id="hackernews-error-case-2",
        goal="Fix errors in hackernews project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_blog_error_case_1(run_and_score, create_test_data):
    """测试blog-error-case-1的自动修复"""
    test_data = create_test_data(
        title="blog项目1",
        description="",
        project_state_id="blog-error-case-1",
        goal="Fix errors in blog project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_blog_error_case_2(run_and_score, create_test_data):
    """测试blog-error-case-2的自动修复"""
    test_data = create_test_data(
        title="blog项目2",
        description="",
        project_state_id="blog-error-case-2",
        goal="Fix errors in blog project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_blog_error_case_3(run_and_score, create_test_data):
    """测试blog-error-case-3的自动修复"""
    test_data = create_test_data(
        title="blog项目3",
        description="",
        project_state_id="blog-error-case-3",
        goal="Fix errors in blog project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_pomodoro_error_case_1(run_and_score, create_test_data):
    """测试pomodoro-error-case-1的自动修复"""
    test_data = create_test_data(
        title="pomodoro项目1",
        description="",
        project_state_id="pomodoro-error-case-1",
        goal="Fix errors in pomodoro project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_fastapi_todolist_error_case_1(run_and_score, create_test_data):
    """测试fastapi-todolist-error-case-1的自动修复"""
    test_data = create_test_data(
        title="fastapi-todolist项目1",
        description="",
        project_state_id="fastapi-todolist-error-case-1",
        goal="Fix errors in fastapi-todolist project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_fastapi_todolist_error_case_2(run_and_score, create_test_data):
    """测试fastapi-todolist-error-case-2的自动修复"""
    test_data = create_test_data(
        title="fastapi-todolist项目2",
        description="",
        project_state_id="fastapi-todolist-error-case-2",
        goal="Fix errors in fastapi-todolist project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_fastapi_todolist_error_case_3(run_and_score, create_test_data):
    """测试fastapi-todolist-error-case-3的自动修复"""
    test_data = create_test_data(
        title="fastapi-todolist项目3",
        description="",
        project_state_id="fastapi-todolist-error-case-3",
        goal="Fix errors in fastapi-todolist project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_rails_twitter_like_error_case_1(run_and_score, create_test_data):
    """测试rails-twitter-like-error-case-1的自动修复"""
    test_data = create_test_data(
        title="rails-twitter-like项目1",
        description="",
        project_state_id="rails-twitter-like-error-case-1",
        goal="Fix errors in rails-twitter-like project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_rails_twitter_like_error_case_2(run_and_score, create_test_data):
    """测试rails-twitter-like-error-case-2的自动修复"""
    test_data = create_test_data(
        title="rails-twitter-like项目2",
        description="",
        project_state_id="rails-twitter-like-error-case-2",
        goal="Fix errors in rails-twitter-like project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_simple_todolist_error_case_1(run_and_score, create_test_data):
    """测试simple-todolist-error-case-1的自动修复"""
    test_data = create_test_data(
        title="simple-todolist项目1",
        description="",
        project_state_id="simple-todolist-error-case-1",
        goal="Fix errors in simple-todolist project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_simple_todolist_error_case_2(run_and_score, create_test_data):
    """测试simple-todolist-error-case-2的自动修复"""
    test_data = create_test_data(
        title="simple-todolist项目2",
        description="",
        project_state_id="simple-todolist-error-case-2",
        goal="Fix errors in simple-todolist project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_simple_todolist_nextjs(run_and_score, create_test_data):
    """测试simple-todolist-nextjs的自动修复"""
    test_data = create_test_data(
        title="simple-todolist-nextjs项目",
        description="",
        project_state_id="simple-todolist-nextjs",
        goal="Fix errors in simple-todolist-nextjs project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_twitter_like_rails_tailwind_postgresql(run_and_score, create_test_data):
    """测试twitter-like-rails-tailwind-postgresql的自动修复"""
    test_data = create_test_data(
        title="twitter-like-rails项目",
        description="",
        project_state_id="twitter-like-rails-tailwind-postgresql",
        goal="Fix errors in twitter-like-rails-tailwind-postgresql project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_product_hunt_scrapper(run_and_score, create_test_data):
    """测试product-hunt-scrapper的自动修复"""
    test_data = create_test_data(
        title="product-hunt-scrapper项目",
        description="",
        project_state_id="product-hunt-scrapper",
        goal="Fix errors in product-hunt-scrapper project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_product_hunt_scrapper_2(run_and_score, create_test_data):
    """测试product-hunt-scrapper-2的自动修复"""
    test_data = create_test_data(
        title="product-hunt-scrapper项目2",
        description="",
        project_state_id="product-hunt-scrapper-2",
        goal="Fix errors in product-hunt-scrapper-2 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_product_hunt_scrapper_3(run_and_score, create_test_data):
    """测试product-hunt-scrapper-3的自动修复"""
    test_data = create_test_data(
        title="product-hunt-scrapper项目3",
        description="",
        project_state_id="product-hunt-scrapper-3",
        goal="Fix errors in product-hunt-scrapper-3 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_product_hunt_scrapper_4(run_and_score, create_test_data):
    """测试product-hunt-scrapper-4的自动修复"""
    test_data = create_test_data(
        title="product-hunt-scrapper项目4",
        description="",
        project_state_id="product-hunt-scrapper-4",
        goal="Fix errors in product-hunt-scrapper-4 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_product_hunt_scrapper_5(run_and_score, create_test_data):
    """测试product-hunt-scrapper-5的自动修复"""
    test_data = create_test_data(
        title="product-hunt-scrapper项目5",
        description="",
        project_state_id="product-hunt-scrapper-5",
        goal="Fix errors in product-hunt-scrapper-5 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_product_hunt_scrapper_6(run_and_score, create_test_data):
    """测试product-hunt-scrapper-6的自动修复"""
    test_data = create_test_data(
        title="product-hunt-scrapper项目6",
        description="",
        project_state_id="product-hunt-scrapper-6",
        goal="Fix errors in product-hunt-scrapper-6 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_book_django_postgresql(run_and_score, create_test_data):
    """测试book-django-postgresql的自动修复"""
    test_data = create_test_data(
        title="book-django-postgresql项目",
        description="",
        project_state_id="book-django-postgresql",
        goal="Fix errors in book-django-postgresql project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_book_django_postgresql_2(run_and_score, create_test_data):
    """测试book-django-postgresql-2的自动修复"""
    test_data = create_test_data(
        title="book-django-postgresql项目2",
        description="",
        project_state_id="book-django-postgresql-2",
        goal="Fix errors in book-django-postgresql-2 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_hn_client_react(run_and_score, create_test_data):
    """测试hn-client-react的自动修复"""
    test_data = create_test_data(
        title="hn-client-react项目",
        description="",
        project_state_id="hn-client-react",
        goal="Fix errors in hn-client-react project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_hn_client_react_2(run_and_score, create_test_data):
    """测试hn-client-react-2的自动修复"""
    test_data = create_test_data(
        title="hn-client-react项目2",
        description="",
        project_state_id="hn-client-react-2",
        goal="Fix errors in hn-client-react-2 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_hn_client_react_3(run_and_score, create_test_data):
    """测试hn-client-react-3的自动修复"""
    test_data = create_test_data(
        title="hn-client-react项目3",
        description="",
        project_state_id="hn-client-react-3",
        goal="Fix errors in hn-client-react-3 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_hn_client_react_4(run_and_score, create_test_data):
    """测试hn-client-react-4的自动修复"""
    test_data = create_test_data(
        title="hn-client-react项目4",
        description="",
        project_state_id="hn-client-react-4",
        goal="Fix errors in hn-client-react-4 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_todolist_nextjs(run_and_score, create_test_data):
    """测试todolist-nextjs的自动修复"""
    test_data = create_test_data(
        title="todolist-nextjs项目",
        description="",
        project_state_id="todolist-nextjs",
        goal="Fix errors in todolist-nextjs project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_todolist_nextjs_2(run_and_score, create_test_data):
    """测试todolist-nextjs-2的自动修复"""
    test_data = create_test_data(
        title="todolist-nextjs项目2",
        description="",
        project_state_id="todolist-nextjs-2",
        goal="Fix errors in todolist-nextjs-2 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_todolist_nextjs_3(run_and_score, create_test_data):
    """测试todolist-nextjs-3的自动修复"""
    test_data = create_test_data(
        title="todolist-nextjs项目3",
        description="",
        project_state_id="todolist-nextjs-3",
        goal="Fix errors in todolist-nextjs-3 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)


@pytest.mark.asyncio
async def test_new_autofix_todolist_nextjs_4(run_and_score, create_test_data):
    """测试todolist-nextjs-4的自动修复"""
    test_data = create_test_data(
        title="todolist-nextjs项目4",
        description="",
        project_state_id="todolist-nextjs-4",
        goal="Fix errors in todolist-nextjs-4 project",
        goal_detail="",
        is_web_service=True
    )
    await run_and_score(test_data)
