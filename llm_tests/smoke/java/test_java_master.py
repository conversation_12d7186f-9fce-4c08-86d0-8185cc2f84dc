# -*- coding: UTF-8 -*-
import pytest
from llm_tests.utils.schema import FileContentAssert, APIAssert


@pytest.mark.init
@pytest.mark.asyncio
async def test_java_master_init(run_whole_process, create_test_data):
    """完整项目: java-master初始化环境 (java)"""

    test_data = create_test_data(
        title="完整项目：java-master 初始化环境",
        description="Spring Boot & MyBatis的项目",
        project_state_id="java-master-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='run_command: mvn spring-boot:run', assert_type='equals'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)

@pytest.mark.API
@pytest.mark.asyncio
async def test_java_master_modify_response_data(run_whole_process, create_test_data):
    """完整项目: java-master 修改响应接口数据"""

    test_data = create_test_data(
        title="完整项目：java-master 修改响应接口数据",
        description="Spring Boot & MyBatis的项目",
        project_state_id="java-master-main",
        goal="Modify return content of the Home Page",
        run_timeout=30,
        goal_detail='I need to change the/interface after the project is running, and change the return '
                    'content from "Welcome to the Home Page!" to "Welcome to Clacky AI"',
        expectations=[
            APIAssert(
                request_method="GET", request_path="/",
                response_status=200,
                assert_type='contains',
                response_body='Clacky'
            ),
            APIAssert(
                request_method="POST", request_path="/user/login",
                request_headers={"content-type": "application/json; charset=utf-8"},
                request_body={"username": "1qaz", "password": "2wsx"},
                response_status=200,
                assert_type='contains',
                response_body={'message': 'SUCCESS'}
            ),
            APIAssert(
                request_method="POST", request_path="/user/add",
                request_headers={"content-type": "application/json; charset=utf-8"},
                request_body={"username": "test888", "password": "0123456", "nickName": "test003", "sex": 1},
                response_status=200,
                assert_type='contains',
                response_body={'message': 'SUCCESS'}
            ),
            APIAssert(
                request_method="GET", request_path="/user/list",
                response_status=200,
                assert_type='contains',
                response_body={'message': 'SUCCESS'}
            ),
        ],
    )

    await run_whole_process(test_data)
