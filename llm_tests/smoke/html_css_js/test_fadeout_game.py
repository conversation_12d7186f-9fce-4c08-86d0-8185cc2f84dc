import pytest

from llm_tests.utils.schema import FileContentAssert

@pytest.mark.init
@pytest.mark.asyncio
async def test_fadeOut_game_init(run_whole_process, create_test_data):
    """完整项目: fadeOut-game 初始化环境 (html/css/js)"""

    test_data = create_test_data(
        title="完整项目：fadeOut-game 初始化环境",
        description="HTML环境,剪头石头布小游戏",
        project_state_id="fadeOut-game-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='browser-sync start --server --no-notify --no-open --files', assert_type='contains'),  # noqa: E501
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_fadeout_game_add_sound_effect(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="完整项目: fadeout-game",
        description="HTML环境,剪头石头布小游戏",
        project_state_id="fadeOut-game-main",
        goal="在胜利（玩家赢）时，添加声音效果",
        goal_detail="玩家赢时，播放 asset 目录下提供的 music.mp3 音乐",
        expect="""1. Modified 'index.html' to include an audio element for the victory sound.
2. Updated 'script.js' to play the victory sound when the player wins.""",
    )

    await run_whole_process(test_data)
