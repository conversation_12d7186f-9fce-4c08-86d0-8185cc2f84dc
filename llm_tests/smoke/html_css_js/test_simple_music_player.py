import pytest
from llm_tests.utils.schema import FileContentAssert

@pytest.mark.init
@pytest.mark.asyncio
async def test_simple_music_player_init(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="HTML/CSS/JS MUSICPLAYER 项目初始化",
        description="HTML环境,音乐播放器",
        project_state_id="simple_music_player-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(
                file_path='.1024',
                content='browser-sync start --server --no-notify --no-open --files',
                assert_type='contains'
            ),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_simple_music_player_pause_prev_feature(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: simple_music_player/实现功能",
        description="HTML环境,音乐播放器，实现“暂停当前曲目”和“上一首”功能",
        codezone_id="691059535397662720",
        codezone_description="简单的JS音乐播放器",
        codezone_git_url="https://github.com/li382112772/simple_music_player.git",
        goal="实现“暂停当前曲目”和“上一首”功能",
        goal_detail="""1.实现 pauseTrack 函数
```
// TODO: 暂停当前曲目
function pauseTrack() {
  // not implemented
}
```
2. 实现 prevTrack 函数：
```
// TODO: 上一首
function prevTrack() {
  // 如果当前曲目不是第一首，则把当前曲目改为上一首
  // 如果当前曲目是第一首，则把当前曲目改为最后一首
  // 加载当前曲目
  // 播放当前曲目
}
```
3. 修改 README.md 文档，去掉对这两个功能未完成的说明""",
        expect="""1. Implemented pauseTrack function to pause the currently playing track.
2. Implemented prevTrack function to switch to the previous track.
3. Updated README.md to reflect the completion of pauseTrack and prevTrack functionalities.""",
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_simple_music_player_format_time_feature(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: simple_music_player/格式化时间显示",
        description="HTML环境,音乐播放器,需求：格式化时间显示",
        codezone_id="691059535397662720",
        codezone_description="简单的JS音乐播放器",
        codezone_git_url="https://github.com/li382112772/simple_music_player.git",
        goal="格式化时间显示",
        goal_detail="""1. 在 seekUpdate 函数中格式化时间为 "mm:ss" 的形式
```
// 更新音乐播放进度条和时间显示
function seekUpdate() {
  let seekPosition = 0;

  if (!isNaN(curr_track.duration)) {
    // 计算并设置进度条位置
    seekPosition = curr_track.currentTime * (100 / curr_track.duration);
    seek_slider.value = seekPosition;

    // 格式化当前时间和总时长
    let currentMinutes = Math.floor(curr_track.currentTime / 60);
    let currentSeconds = Math.floor(curr_track.currentTime - currentMinutes * 60);
    let durationMinutes = Math.floor(curr_track.duration / 60);
    let durationSeconds = Math.floor(curr_track.duration - durationMinutes * 60);

    // TODO: 格式化时间为 "mm:ss" 的形式

    // 显示当前时间和总时长
    curr_time.textContent = currentMinutes + ":" + currentSeconds;
    total_duration.textContent = durationMinutes + ":" + durationSeconds;
  }
```
2. 修改 README.md 文档，去掉对这个功能未完成的说明""",
        expect="""1. Updated the `seekUpdate` function to display playtime and duration in the format `mm:ss`.
2. Ensured two digits for both minutes and seconds in the displayed time.
3. Updated `README.md` to remove notes about the incomplete time formatting feature.""",
    )

    await run_whole_process(test_data)
