import pytest

@pytest.mark.asyncio
@pytest.mark.build_from_scratch
@pytest.mark.unstable
async def test_new_hackernews_homepage(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: HackerNews 介绍首页",
        description="开发一个介绍 HackerNews 的网站首页",
        project_state_id="blank-project-main",
        goal="Develop a beautiful homepage for HackerNews using Tailwind CSS",
        goal_detail="""- Use Tailwind CSS for styling with CDN
- Top navigation bar: including website logo, navigation menu
- Homepage banner: display the introduction and main features of HackerNews
- Latest News: display the latest 10 news items, click any of the news item would jump to HackerNews""",
    )

    await run_whole_process(test_data)
