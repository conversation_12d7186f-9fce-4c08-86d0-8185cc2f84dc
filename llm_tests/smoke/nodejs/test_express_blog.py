import pytest
from llm_tests.utils.schema import FileContentAssert

@pytest.mark.init
@pytest.mark.asyncio
async def test_express_blog_init(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="完整项目: express_blog/初始化环境",
        description="为 Express 项目 express_blog 初始化开发环境",
        project_state_id="express-blog-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='node index.js', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains'),
            "规划了 Action 通过 npm 先安装 yarn",
            "规划了 Action 运行 yarn install",
        ],
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_express_blog_refactor(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: express_blog/优化代码结构",
        description="为 Express 项目 express_blog 优化代码结构",
        project_state_id="express-blog-main",
        goal="优化代码结构",
        goal_detail="""将路由、控制器、模型等分离到不同的文件中, 提升代码的可维护性。
        例如, 将路由定义放在routes文件夹中, 将控制器逻辑放在controllers文件夹中。""",
        expect=""" "Separated routes into a 'routes' folder",
"Moved controller logic into a 'controllers' folder",
"Organized model definitions into a 'models' folder"
 """,
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_express_blog_create_blogs_feature(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: express_blog/实现创建新博文的前端功能",
        description="在 Express 项目 express_blog 中实现创建新博文的前端功能",
        project_state_id="express-blog-main",
        goal="实现创建新博文的前端功能",
        goal_detail="""在现有的前端代码中添加一个表单, 用于输入新博文的标题、作者和内容, 并将这些数据通过API发送到后端。""",
        expect=""" "Added a form in `public/index.html` containing input fields for title, author, and content.",
"Modified `public/style.css` to include styles for the new form.",
"Added a submit button to the form to send the entered data to the backend via API."
 """,
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_express_blog_user_login_feature(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: express_blog/实现完整的用户登录功能",
        description="在 Express 项目 express_blog 中实现完整的用户登录功能",
        project_state_id="express-blog-main",
        goal="实现完整的用户登录功能",
        goal_detail="""1. 后端实现用户登录的API验证用户的邮箱和密码并返回相应的响应。
2. 前端补充用户登录的功能，确保用户可以通过前端界面登录。
3. 确保在数据库中有用户模型，并且包含邮箱和密码字段。""",
        expect="""1. Added a user model to the database with email and password fields.
2. Created backend API endpoints for user login, including validation of email and password.
3. Modified the frontend to add a login form for users.
4. Ensured the login form is styled appropriately using CSS.
5. Updated the frontend logic to handle login form submission and display appropriate messages based on API response.
 """,
    )

    await run_whole_process(test_data)
