import pytest

@pytest.mark.asyncio
@pytest.mark.build_from_scratch
@pytest.mark.unstable(reason="tailwindcss@4")
async def test_new_hackernews_app(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: HackerNews API 客户端",
        description="基于 HackerNews API 开发一个漂亮的客户端",
        project_state_id="blank-project-main",
        goal="Develop a HackerNews client app in React and TailwindCSS",
        goal_detail="""1. Tech Stack: React(with <PERSON><PERSON> Hooks), TailwindCSS
2. Core Features: Display lists of newest stories, Pagination support, Story search functionality
3. User Experience: Dark/Light theme toggle, Loading states, Smooth page transitions

API Reference: `https://github.com/HackerNews/API`
""",  # noqa: E501
    )

    await run_whole_process(test_data)
