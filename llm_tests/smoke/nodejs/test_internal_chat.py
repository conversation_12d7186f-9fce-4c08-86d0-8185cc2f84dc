# -*- coding: UTF-8 -*-
import pytest
from llm_tests.utils.schema import FileContentAssert

@pytest.mark.init
@pytest.mark.asyncio
async def test_internal_chat_init(run_whole_process, create_test_data):
    """node：局域网文字/文件 p2p 传输环境初始化"""

    test_data = create_test_data(
        title="项目：局域网文字/文件 p2p 传输工具的环境初始化",
        description="",
        project_state_id="internal-chat-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024',
                              content='cd server && npm run start && browser-sync',
                              assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)


@pytest.mark.asyncio
async def test_internal_chat_upload_file_select_user(run_whole_process, create_test_data):
    """需求: 实现文件发送的用户选择功能 """

    test_data = create_test_data(
        title="需求: 实现文件发送的用户选择功能",
        description="",
        project_state_id="internal-chat-main",
        goal="Implement the user selection function of sending files",
        goal_detail="Allows the user to manually select the recipient to send the file, "
                    "ensuring that only online users can be selected."
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_internal_chat_upload_folder(run_whole_process, create_test_data):
    """需求: 实现文件夹的整体传输功能 """

    test_data = create_test_data(
        title="需求: 实现文件夹的整体传输功能",
        description="",
        project_state_id="internal-chat-main",
        goal="To achieve the overall folder transfer function",
        goal_detail="rsync is used to achieve the overall transfer of the folder and all its subfolders and files, "
                    "and to ensure the integrity and security of the data during the transfer process."
    )

    await run_whole_process(test_data)


@pytest.mark.asyncio
async def test_real_time_display_transfer_progress(run_whole_process, create_test_data):
    """需求: 实现实时显示传输进度功能 """

    test_data = create_test_data(
        title="需求: 实现实时显示传输进度功能",
        description="",
        project_state_id="internal-chat-main",
        goal="Add a real-time progress bar for file transfers",
        goal_detail="Implement a progress bar in the userSelectModal that shows the progress of the "
                    "current file transfer in percentage, alongside the current transfer "
                    "rate (e.g., MB/s) and a remaining time estimation based on transmission speed."
                    " Adjust the JavaScript logic in index.js to update these values dynamically "
                    "during the file transfer process."
    )

    await run_whole_process(test_data)
