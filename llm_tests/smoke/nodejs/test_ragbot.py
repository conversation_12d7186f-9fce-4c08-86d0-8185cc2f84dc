# -*- coding: UTF-8 -*-
import pytest
from llm_tests.utils.schema import FileContentAssert

@pytest.mark.init
@pytest.mark.asyncio
async def test_ragbot_init(run_whole_process, create_test_data):
    """完整项目:  RAGBot Starter 初始化环境 (next.js)"""

    test_data = create_test_data(
        title="Next.js RAGBot Starter/初始化环境",
        description="为 Next.js 项目 RAGBot Starter 初始化开发环境",
        codezone_id="743585420734005248",
        codezone_description="Next.js 环境, RAGBot Starter — An Astra DB and OpenAI chatbot",
        codezone_git_url="https://github.com/datastax/ragbot-starter",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='npm run dev', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.pnpm-store', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_ragbot_modify_env_example_file(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="Next.js RAGBot Starter/modify .env.example file",
        description="修改 Next.js 项目 RAGBot Starter 中的 .env.example 文件",
        project_state_id="ragbot-starter-main",
        goal="change `ASTRA_DB_REGION` to `ASTRA_DB_API_ENDPOINT`",
        goal_detail=""" change `ASTRA_DB_REGION` to `ASTRA_DB_API_ENDPOINT` in the `.env.example` file. """,
        expect=""" Changed ASTRA_DB_REGION to ASTRA_DB_API_ENDPOINT in the .env.example file. """,
    )

    await run_whole_process(test_data)
