import pytest
from llm_tests.utils.schema import FileContentAssert

@pytest.mark.init
@pytest.mark.asyncio
async def test_ai_chatbot_init(run_whole_process, create_test_data):
    """完整项目: Chatbot/初始化环境 (next.js)"""

    test_data = create_test_data(
        title="完整项目: Next.js AI Chatbot/初始化环境",
        description="为 Next.js 项目 AI Chatbot 初始化开发环境",
        project_state_id="ai-chatbot-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='pnpm run dev', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.pnpm-store', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)
