# -*- coding: UTF-8 -*-
import pytest
from llm_tests.utils.schema import FileContentAssert


@pytest.mark.init
@pytest.mark.asyncio
async def test_nextjs_dashboard_init(run_whole_process, create_test_data):
    """完整项目: dashboard初始化环境 (next.js)"""

    test_data = create_test_data(
        title="完整项目： Next.js dashboard 初始化环境",
        description="为 Next.js dashboard 项目初始化开发环境",
        project_state_id="nextjs-dashboard-main",
        run_timeout=30,
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='pnpm run dev', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.pnpm-store', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)
