# -*- coding: UTF-8 -*-
import pytest


@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_vitepress_website(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: VitePress 产品文档网站",
        description="",
        project_state_id="blank-project-main",
        goal="Build a doc website with vitepress",
        goal_detail="""Use VitePress to build a technical documentation website:
- Use pnpm to manage dependencies, and Typescript
- Use the logo of HackerNews(https://news.ycombinator.com/y18.svg)

Reference: https://vitepress.dev/guide/getting-started
""",
    )

    await run_whole_process(test_data)
