# -*- coding: UTF-8 -*-
import pytest
from llm_tests.utils.schema import FileContentAssert

@pytest.mark.init
@pytest.mark.asyncio
async def test_nextra_docs_template_init(run_whole_process, create_test_data):
    """完整项目: Docs Starter Kit 初始化环境 (next.js)"""

    test_data = create_test_data(
        title="完整项目: Nextra: Docs Starter Kit/初始化环境",
        description="为 Next.js 项目 Nextra 初始化开发环境",
        project_state_id="nextra-docs-template-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='pnpm run dev', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.pnpm-store', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_nextra_sidebar_cache_issues(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: Nextra: Docs Starter Kit/Sidebar - Cache issues",
        description="在 Next.js 项目 Nextra 解决 sidebar 缓存问题",
        project_state_id="nextra-docs-template-main",
        goal="Sidebar - Cache issues",
        goal_detail=""" Sidebar navigation has cache issues. After adding a new page, the page does not appear in the sidebar.
        For example, by updating the index page, the new page appears, but if you visit another page again,
        the new page is missing in the sidebar, as long you don't update all pages in the navigation. """,
        expect=""" "Retrieved code from 'components/counters.tsx' to analyze sidebar implementation.",
"Checked 'next.config.js' for any caching mechanism configurations that might affect the sidebar.",
"Investigated any related code influencing the sidebar caching issue." """,
    )

    await run_whole_process(test_data)
