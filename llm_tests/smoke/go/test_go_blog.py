import pytest


@pytest.mark.asyncio
async def test_go_blog_upload_image_feature(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: go-blog/实现图片上传功能",
        description="在 Go 项目 go-blog 中实现图片上传功能",
        project_state_id="go-blog-main",
        goal="实现图片上传功能",
        goal_detail="""目前的图片上传功能没有实现，应该在处理文章创建时处理图片上传，并将图片保存到服务器""",
        expect=""" "Added file upload support in the article creation form in `templates/new_article.html`.",
"Updated the article handler in `handlers.article.go` to handle file uploads.",
"Saved uploaded files to the `public/uploads` directory.",
"Updated routing in `routes.go` to include new file upload handling." """,
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_go_blog_refactor(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: go-blog/优化代码结构",
        description="在 Go 项目 go-blog 中优化代码结构",
        project_state_id="go-blog-main",
        goal="优化代码结构",
        goal_detail="""将代码分成更小的模块和包，以提高可维护性。例如，可以将用户和文章的处理逻辑分别放在不同的包中""",
        expectations=[
            "Created a new directory structure to segregate different modules.",
            "Moved handlers.article.go to handlers/article_handlers/article.go.",
            "Moved handlers.user.go to handlers/user_handlers/user.go.",
            "Moved models.article.go to models/article/article.go.",
            "Moved models.user.go to models/user/user.go.",
            "Updated import paths in main.go, routes.go, and other affected files."
        ]
    )

    await run_whole_process(test_data)
