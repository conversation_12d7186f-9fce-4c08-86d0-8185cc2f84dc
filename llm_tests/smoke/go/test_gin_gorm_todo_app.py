import pytest
from llm_tests.utils.schema import FileContentAssert


# FIXME: 该文件下 codezone_id 均不存在，需重新 fork
@pytest.mark.init
@pytest.mark.asyncio
@pytest.mark.unstable(role="code_role", reason="项目启动需要预先配置好数据库环境,且创建好库表")
@pytest.mark.skip(reason="项目启动需要预先配置好数据库环境,且创建好库表")
async def test_gin_gorm_todo_app_init(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="初始化开发环境 gin-gorm-todo-app",
        description="Go 项目 gin-gorm-todo-app",
        project_state_id="gin-gorm-todo-app-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='run_command: go run main.go', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains'),
            FileContentAssert(file_path='.env', content='DB_HOST=', assert_type='contains'),
        ],
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_gin_gorm_todo_app_refactor(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: gin-gorm-todo-app/优化代码结构",
        description="在 Go 项目 gin-gorm-todo-app 中将控制器、模型和路由分离到不同的包中，以提高代码的可维护性和可读性",
        codezone_id="694662748390440960",
        codezone_description="Go-gin环境,gin-gorm-todo-app",
        codezone_git_url="https://github.com/li382112772/gin-gorm-todo-app.git",
        goal="优化代码结构",
        goal_detail="""将控制器、模型和路由分离到不同的包中，以提高代码的可维护性和可读性。""",
        expect=""" 1. Moved existing files using the new package structure to organize the code according to business logic.
        Updated import paths to reflect the new file structure.
2. Renamed and moved related files into the new directories.
Ensured file names and their contents reflected the new directory structure. """,
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_gin_gorm_todo_app_add_environment_variable(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="完整项目: gin-gorm-todo-app/环境变量配置数据库",
        description="在 Go 项目 gin-gorm-todo-app 中使用环境变量配置数据库",
        codezone_id="694662748390440960",
        codezone_description="Go-gin环境,gin-gorm-todo-app",
        codezone_git_url="https://github.com/li382112772/gin-gorm-todo-app.git",
        goal="使用环境变量来配置数据库连接信息，以提高安全性和灵活性",
        goal_detail="""1. 在 Config/Database.go 中，使用环境变量来配置数据库连接信息，以提高安全性和灵活性。
2. 更新 README.md 文件，包含环境变量配置的说明。""",
        expect="""1. Added user authentication via session-based mechanism.
2. Modified WebSocket connection handler to check user authentication.
3. Updated main.go to initialize session management.""",
    )

    await run_whole_process(test_data)
