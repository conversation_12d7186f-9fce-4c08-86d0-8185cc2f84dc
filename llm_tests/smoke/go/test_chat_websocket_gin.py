# -*- coding: UTF-8 -*-
import pytest
from llm_tests.utils.schema import FileContentAssert

@pytest.mark.init
@pytest.mark.asyncio
async def test_chat_websocket_gin_init(run_whole_process, create_test_data):
    """完整项目: chat-websocket-gin 初始化环境 (golang)"""

    test_data = create_test_data(
        title="完整项目：chat-websocket-gin 初始化环境",
        description="为 chat-websocket-gin 项目初始化开发环境",
        project_state_id="chat-websocket-gin-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='run_command: go run main.go', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains')
        ],
    )

    await run_whole_process(test_data)

@pytest.mark.asyncio
async def test_chat_websocket_gin_add_user_authentication(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="完整项目: chat-websocket-gin/添加用户身份验证功能",
        description="在 Go 项目 chat-websocket-gin 中添加用户身份验证功能",
        project_state_id="chat-websocket-gin-main",
        goal="添加用户身份验证功能",
        goal_detail="""在 WebSocket 连接时进行用户身份验证，确保只有授权用户才能加入房间。""",
        expectations=[
            "Added user authentication via session-based mechanism.",
            "Modified WebSocket connection handler to check user authentication.",
            "Updated main.go to initialize session management."
        ]
    )

    await run_whole_process(test_data)
