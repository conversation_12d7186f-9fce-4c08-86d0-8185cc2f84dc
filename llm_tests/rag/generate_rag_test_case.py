"""
Description:
    This script is to generate a test case of the RAG search, using the code-search-net data set.
    It is mainly used to test the recall ability, that is, the number of relevant results retrieved / the total number of all relevant results.

Usage:
    - python llm_tests/rag/generate_rag_test_case.py
    - After generating the file, you need
        - create a new playground
        - clone the code through git clone
        - get the codezone_id, and fill in the test case.
""" # noqa: E501

import os
import re
import asyncio
from datasets import load_dataset

from heracles.agent_controller.llm import LLM

GENERATE_NUMBER = 30

# 依赖
DEPENDENCIES_STR = """\"""
使用数据集 code-search-net/code_search_net 批量生成的测试用例
repository_name: {repository_name}
\"""

import pytest
from heracles.core.schema import FileSnippet

"""

# 测试用例
UNITTEST_STR = """@pytest.mark.asyncio
async def {func_name}(run_whole_rag_process, create_test_rag_data):
    repository_name = "{repository_name}"
    path = "{path}"
    question = "{question}"  # noqa: E501
    content = "{content}"  # noqa: E501
    row_start = {row_start}
    row_end = {row_end}

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {{path}}",
        codezone_id="inited_codezone_id", # TODO: init codezone_id by git clone
        codezone_description="",
        codezone_git_url=f"**************:{{repository_name}}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

"""

ADD_NOISE_TO_QUESTION_PROMPT="""
Do not answer any questions or provide any confirmation.
You are tasked with transforming text inputs by introducing noise or paraphrasing. Your goal is to slightly modify the input text while maintaining its original meaning. You should use the following techniques to achieve this:

1. **Synonym Replacement**: Replace some key words with their synonyms or near-synonyms.
2. **Word Order Alteration**: Change the order of words in a sentence without altering the meaning.
3. **Query Expansion or Reduction**: Add or remove words or phrases to expand or shorten the input text.
4. **Introduce Spelling Errors**: Intentionally introduce minor spelling mistakes in non-critical parts of the text.
5. **Substitute Expressions**: Replace phrases with equivalent expressions or technical jargon.
6. **Rephrase Sentences**: Paraphrase the sentence while preserving its original intent.
7. **Grammar Variation**: Modify the grammatical structure, such as changing from active to passive voice or from a statement to a question.
8. **Add Noise Words**: Insert non-essential words or phrases to make the text more conversational.

For each transformation, ensure the modified text remains grammatically correct (unless a spelling error is introduced) and that the overall meaning is preserved. Your transformations should be subtle, not drastically changing the text, but rather making it slightly different from the original.
"""  # noqa: E501

DIR_PATH = "llm_tests/rag/test_with_datasets/"

def init_file(file_path, content):
    """
    初始化文件，写入依赖
    """
    if os.path.exists(file_path):
        # 如果文件已经存在，则认为已经初始化过了
        return

    os.makedirs(DIR_PATH, exist_ok=True)
    with open(file_path, 'w') as file:
        file.write(content)

def get_row_number(func_code_url):
    """
    获取代码行数
    """
    # 使用正则表达式提取行号范围
    match = re.search(r'#L(\d+)-L(\d+)', func_code_url)

    if match:
        return int(match.group(1)), int(match.group(2))
    else:
        raise ValueError(f"Can't extract row number from {func_code_url}")

def escape_to_write(str):
    return str.replace('\n', '\\n').replace('"', '\\"')

def add_noise(question):
    llm = LLM()
    return asyncio.run(llm.async_send(user_message=question, system_message=ADD_NOISE_TO_QUESTION_PROMPT))

def generate():
    # 加载 CodeSearchNet 数据集
    dataset = load_dataset('code-search-net/code_search_net')

    # 获取训练集
    train_dataset = dataset['train']

    for index in range(GENERATE_NUMBER):
        try:
            data = train_dataset[130 + index]
            func_name = f"test_rag_search_datasets_{index}"
            path = data['func_path_in_repository']
            content = data['whole_func_string']
            repository_name = data['repository_name']
            question = data['func_documentation_string']
            func_code_url = data['func_code_url']

            row_start, row_end = get_row_number(func_code_url)
            content = escape_to_write(content)
            question = add_noise(question)
            question = escape_to_write(question)
            test_file_content = UNITTEST_STR.format(
                func_name=func_name,
                path=path,
                content=content,
                repository_name=repository_name,
                question=question,
                row_start=row_start,
                row_end=row_end
            )

            repository_name_path = repository_name.replace('/', '_').replace('-', '_')
            file_path = os.path.join(DIR_PATH, f"test_rag_{repository_name_path}.py")
            init_file_content = DEPENDENCIES_STR.format(repository_name=repository_name)

            init_file(file_path, init_file_content)

            with open(file_path, 'a') as file:
                file.write(test_file_content)
        except Exception as e:
            print(f"[index_{index}] Error: {e}")
            continue

if __name__ == '__main__':
    generate()
