import pytest
from heracles.core.schema import FileSnippet

@pytest.mark.asyncio
async def test_rag_search_basic(run_whole_rag_process, create_test_rag_data):
    except_list = [
        FileSnippet(
            path="components/chat-panel.tsx",
            content='\n<div className="mb-4 grid grid-cols-2 gap-2 px-4 sm:px-0">\n  {messages.length === 0 &&\n    exampleMessages.map((example, index) => (\n      <div\n        key={example.heading}\n        className={`cursor-pointer rounded-lg border bg-white p-4 hover:bg-zinc-50 dark:bg-zinc-950 dark:hover:bg-zinc-900 ${\n          index > 1 && \'hidden md:block\'\n        }`}\n        onClick={async () => {\n          setMessages(currentMessages => [\n            ...currentMessages,\n            {\n              id: nanoid(),\n              display: <UserMessage>{example.message}</UserMessage>\n            }\n          ])\n          const responseMessage = await submitUserMessage(\n            example.message\n          )\n          setMessages(currentMessages => [\n            ...currentMessages,\n            responseMessage\n          ])\n        }}\n      >\n        <div className="text-sm font-semibold">{example.heading}</div>\n        <div className="text-sm text-zinc-600">\n          {example.subheading}\n        </div>\n      </div>\n    ))}\n</div>\n',  # noqa: E501
            row_start=68,
            row_end=101
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: from two columns to one column",
        description="",
        codezone_id="693594297324408832",
        codezone_description="默认的 Next.js 项目",
        codezone_git_url="**************:vercel/ai-chatbot.git",
        question="Change the `exampleMessages` view from two columns to one column",
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)
