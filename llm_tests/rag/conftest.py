import asyncio
import pytest

from llm_tests.utils.schema import ScoreModel, TestRagData
from llm_tests.utils import update_trace_tag, create_trace_score
from heracles.core.config import get_env_var

async def score_rag_result_with_llm(workspace, expect, result):
    """
    通过 LLM 打分
    """
    llm = workspace.rag_searcher.llm
    prompt = """
    You are a senior R&D engineer, and your task is to evaluate whether a relevant document meets expectations.

    <EXPECT>
    {expect}
    </EXPECT>

    <RELATED_FILES>
    {result}
    </RELATED_FILES>
    """

    joined_relevant_snippets = workspace.rag_searcher.convert_snippets_to_prompt(result)
    expect_str = workspace.rag_searcher.convert_snippets_to_prompt(expect)

    score_model = await llm.async_send(
        user_message=prompt.format(expect=expect_str, result=joined_relevant_snippets),
        response_model=ScoreModel,
    )
    return score_model

def similarity(res, expect):
    """
    计算查询和期望的相似度
    """
    # 计算重叠区间
    overlap_start = max(res.row_start, expect.row_start)
    overlap_end = min(res.row_end, expect.row_end)

    # 没有重叠，返回 0 相似度
    if overlap_start >= overlap_end:
        return 0.0

    # 计算重叠长度
    overlap_length = overlap_end - overlap_start

    # 计算相似度
    similarity_score = overlap_length / (expect.row_end - expect.row_start)
    return similarity_score

async def score_rag_result_with_recall(expect, result):
    """
    计算 recall ability, 原理是 result 中的 snippet.path 是否在 expect 中
    """
    comment = "召回率 = 所有相关结果的相似总数 / 检索到的相关结果数量"
    relevant_score = 0
    for expect_snippet in expect:
        for snippet in result:
            if snippet.path != expect_snippet.path:
                continue
            similarity_score = similarity(snippet, expect_snippet)
            relevant_score += similarity_score
            print('relevant_score', relevant_score, similarity_score)
    score = round(relevant_score / len(expect), 2)

    print(f"召回率: {score}")
    return ScoreModel(value=score, comment=comment)

@pytest.fixture
def create_test_rag_data():
    def _create_test_rag_data(**kwargs):
        # RAG 使用 rag_expect，不使用 expect
        kwargs['expect'] = ''
        return TestRagData(**kwargs)
    return _create_test_rag_data


def playground_is_inactive(workspace):
    return workspace.playground.ide_server_client.status != 'ACTIVE'

@pytest.fixture
def run_whole_rag_process(create_workspace):
    """跑完整的RAG流程并打分
    """
    async def _run_whole_rag_process(test_rag_data: TestRagData):
        codezone_id = test_rag_data.codezone_id
        workspace = await create_workspace(codezone_id)

        rag_score_mode = get_env_var('RAG_SCORE_MODE', 'recall')
        question_expand = get_env_var('QUESTION_EXPAND', 'y')
        score_model = ScoreModel(value=0, comment='draft')

        try:
            # 0. 确保 playground status == ACTIVE
            if playground_is_inactive(workspace):
                await asyncio.sleep(5)  # playground 正在自动激活中，等待 5 秒
                if playground_is_inactive(workspace):
                    raise Exception("playground status is not ACTIVE")

            # 1. RAG 检索
            relevant_snippets = await workspace.rag_searcher.search(test_rag_data.question, question_expand == 'y')
            print("RAG 检索结果", [[s.path, s.row_start, s.row_end] for s in relevant_snippets])

            # 2. 结果打分
            if rag_score_mode == 'recall':
                score_model = await score_rag_result_with_recall(test_rag_data.rag_expect, relevant_snippets)
            else:
                score_model = await score_rag_result_with_llm(workspace, test_rag_data.expect, relevant_snippets)
        finally:
            update_trace_tag(workspace, 'rag-test', test_rag_data.title)
            score_name = 'rag-recall' if rag_score_mode == 'recall' else ''
            create_trace_score(workspace, score_model.value, score_model.comment, name=score_name)

    return _run_whole_rag_process
