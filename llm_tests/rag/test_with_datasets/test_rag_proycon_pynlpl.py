"""
使用数据集 code-search-net/code_search_net 批量生成的测试用例
repository_name: proycon/pynlpl
"""

import pytest
from heracles.core.schema import FileSnippet

@pytest.mark.asyncio
async def test_rag_search_datasets_0(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/search.py"
    question = "Returns the optimal result, and if several have an identical score, it will yield the first match found."  # noqa: E501
    content = "def searchbest(self):\n        \"\"\"Returns the single best result (if multiple have the same score, the first match is returned)\"\"\"\n        finalsolution = None\n        bestscore = None\n        for solution in self:\n            if bestscore == None:\n                bestscore = solution.score()\n                finalsolution = solution\n            elif self.minimize:\n                score = solution.score()\n                if score < bestscore:\n                    bestscore = score\n                    finalsolution = solution\n            elif not self.minimize:\n                score = solution.score()\n                if score > bestscore:\n                    bestscore = score\n                    finalsolution = solution                \n        return finalsolution"  # noqa: E501
    row_start = 243
    row_end = 261

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_1(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/search.py"
    question = "Return the top n best outcomes (or fewer if there are not enough available)."  # noqa: E501
    content = "def searchtop(self,n=10):\n        \"\"\"Return the top n best resulta (or possibly less if not enough is found)\"\"\"            \n        solutions = PriorityQueue([], lambda x: x.score, self.minimize, length=n, blockworse=False, blockequal=False,duplicates=False)\n        for solution in self:\n            solutions.append(solution)\n        return solutions"  # noqa: E501
    row_start = 263
    row_end = 268

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_2(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/search.py"
    question = "Retrieve the final n results, although it could be fewer if not all are located. Keep in mind that the most recent results might not always correspond to the most relevant ones! This varies based on the search type."  # noqa: E501
    content = "def searchlast(self,n=10):\n        \"\"\"Return the last n results (or possibly less if not found). Note that the last results are not necessarily the best ones! Depending on the search type.\"\"\"            \n        solutions = deque([], n)\n        for solution in self:\n            solutions.append(solution)\n        return solutions"  # noqa: E501
    row_start = 270
    row_end = 275

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_3(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/clients/cornetto.py"
    question = "Generates a catalog of synset identifiers using a lemma"  # noqa: E501
    content = "def get_syn_ids_by_lemma(self, lemma):\n        \"\"\"Returns a list of synset IDs based on a lemma\"\"\"\n        if not isinstance(lemma,unicode):\n            lemma = unicode(lemma,'utf-8')\n\n\n        http, resp, content = self.connect()\n\n        params   = \"\"\n        fragment = \"\"\n\n        path = \"cdb_syn\"\n        if self.debug:\n            printf( \"cornettodb/views/query_remote_syn_lemma: db_opt: %s\" % path )\n\n        query_opt = \"dict_search\"\n        if self.debug:\n            printf( \"cornettodb/views/query_remote_syn_lemma: query_opt: %s\" % query_opt )\n    \n        qdict = {}\n        qdict[ \"action\" ] = \"queryList\"\n        qdict[ \"word\" ]   = lemma.encode('utf-8')\n\n\n        query = urllib.urlencode( qdict )\n\n        db_url_tuple = ( self.scheme, self.host + ':' + str(self.port), path, params, query, fragment )\n        db_url = urlparse.urlunparse( db_url_tuple )\n        if self.debug:\n            printf( \"db_url: %s\" % db_url )\n\n        resp, content = http.request( db_url, \"GET\" )\n        if self.debug:\n            printf( \"resp:\n%s\" % resp )\n            printf( \"content:\n%s\" % content )\n        #    printf( \"content is of type: %s\" % type( content ) )\n\n        dict_list = []\n        dict_list = eval( content )        # string to list\n\n        synsets = []\n        items = len( dict_list )\n        if self.debug:\n            printf( \"items: %d\" % items )\n\n        # syn dict: like lu dict, but without pos: part-of-speech\n        for dict in dict_list:\n            if self.debug:\n                printf( dict )\n\n            seq_nr = dict[ \"seq_nr\" ]   # sense number\n            value  = dict[ \"value\" ]    # lexical unit identifier\n            form   = dict[ \"form\" ]     # lemma\n            label  = dict[ \"label\" ]    # label to be shown\n\n            if self.debug:\n                printf( \"seq_nr: %s\" % seq_nr )\n                printf( \"value:  %s\" % value )\n                printf( \"form:   %s\" % form )\n                printf( \"label:  %s\" % label )\n\n            if value != \"\":\n                synsets.append( value )\n\n        return synsets"  # noqa: E501
    row_start = 96
    row_end = 160

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_4(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/clients/cornetto.py"
    question = "invoke cdb_syn using the synset identifier -> retrieves the synset XML;"  # noqa: E501
    content = "def get_synset_xml(self,syn_id):\n        \"\"\"\n        call cdb_syn with synset identifier -> returns the synset xml;\n        \"\"\"\n\n        http, resp, content = self.connect()\n\n        params   = \"\"\n        fragment = \"\"\n\n        path = \"cdb_syn\"\n        if self.debug:\n            printf( \"cornettodb/views/query_remote_syn_id: db_opt: %s\" % path )\n\n        # output_opt: plain, html, xml\n        # 'xml' is actually xhtml (with markup), but it is not valid xml!\n        # 'plain' is actually valid xml (without markup)\n        output_opt = \"plain\"\n        if self.debug:\n            printf( \"cornettodb/views/query_remote_syn_id: output_opt: %s\" % output_opt )\n\n        action = \"runQuery\"\n        if self.debug:\n            printf( \"cornettodb/views/query_remote_syn_id: action: %s\" % action )\n            printf( \"cornettodb/views/query_remote_syn_id: query: %s\" % syn_id )\n\n        qdict = {}\n        qdict[ \"action\" ]  = action\n        qdict[ \"query\" ]   = syn_id\n        qdict[ \"outtype\" ] = output_opt\n\n        query = urllib.urlencode( qdict )\n\n        db_url_tuple = ( self.scheme, self.host + ':' + str(self.port), path, params, query, fragment )\n        db_url = urlparse.urlunparse( db_url_tuple )\n        if self.debug:\n            printf( \"db_url: %s\" % db_url )\n\n        resp, content = http.request( db_url, \"GET\" )\n        if self.debug:\n            printf( \"resp:\n%s\" % resp )\n        #    printf( \"content:\n%s\" % content )\n        #    printf( \"content is of type: %s\" % type( content ) )        #<type 'str'>\n\n        xml_data = eval( content )\n        return etree.fromstring( xml_data )"  # noqa: E501
    row_start = 227
    row_end = 272

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_5(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/clients/cornetto.py"
    question = "Provides a list of tuples consisting of (word, lu_id) based on a synset ID."  # noqa: E501
    content = "def get_lus_from_synset(self, syn_id):\n        \"\"\"Returns a list of (word, lu_id) tuples given a synset ID\"\"\"\n\n        root = self.get_synset_xml(syn_id)\n        elem_synonyms = root.find( \".//synonyms\" )\n\n\n        lus = []\n        for elem_synonym in elem_synonyms:\n            synonym_str = elem_synonym.get( \"c_lu_id-previewtext\" )        # get \"c_lu_id-previewtext\" attribute\n            # synonym_str ends with \":<num>\"\n            synonym = synonym_str.split( ':' )[ 0 ].strip()\n            lus.append( (synonym, elem_synonym.get( \"c_lu_id\") ) )\n        return lus"  # noqa: E501
    row_start = 275
    row_end = 288

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_6(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/clients/cornetto.py"
    question = "It returns a tuple (lu_id, synonyms=[(word, lu_id)] ) given a synset ID along with a lemma."  # noqa: E501
    content = "def get_lu_from_synset(self, syn_id, lemma = None):\n        \"\"\"Returns (lu_id, synonyms=[(word, lu_id)] ) tuple given a synset ID and a lemma\"\"\"\n        if not lemma:\n            return self.get_lus_from_synset(syn_id) #alias\n        if not isinstance(lemma,unicode):\n            lemma = unicode(lemma,'utf-8')\n\n        root = self.get_synset_xml(syn_id)\n        elem_synonyms = root.find( \".//synonyms\" )\n\n        lu_id = None\n        synonyms = []\n        for elem_synonym in elem_synonyms:\n            synonym_str = elem_synonym.get( \"c_lu_id-previewtext\" )        # get \"c_lu_id-previewtext\" attribute\n            # synonym_str ends with \":<num>\"\n            synonym = synonym_str.split( ':' )[ 0 ].strip()\n\n            if synonym != lemma:\n                synonyms.append( (synonym, elem_synonym.get(\"c_lu_id\")) )\n                if self.debug:\n                    printf( \"synonym add: %s\" % synonym )\n            else:\n                lu_id = elem_synonym.get( \"c_lu_id\" )        # get \"c_lu_id\" attribute\n                if self.debug:\n                    printf( \"lu_id: %s\" % lu_id )\n                    printf( \"synonym skip lemma: %s\" % synonym )\n        return lu_id, synonyms"  # noqa: E501
    row_start = 291
    row_end = 317

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_7(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/formats/dutchsemcor.py"
    question = "Produces a list of every anticipated sense"  # noqa: E501
    content = "def senses(self, bestonly=False):\n        \"\"\"Returns a list of all predicted senses\"\"\"\n        l = []\n        for word_id, senses,distance in self:\n            for sense, confidence in senses:\n                if not sense in l: l.append(sense)\n                if bestonly:\n                    break\n        return l"  # noqa: E501
    row_start = 139
    row_end = 147

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_8(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/clients/frogclient.py"
    question = "Takes in input_data as either a str or unicode object, sends this to the server with careful regard for the encodings, and retrieves the Frog results as a sequence of tuples: (word,pos,lemma,morphology). Each of these tuples will be a proper unicode object since return_unicode is now always set to True; the parameter remains solely for backward compatibility."  # noqa: E501
    content = "def process(self,input_data, source_encoding=\"utf-8\", return_unicode = True, oldfrog=False):\n        \"\"\"Receives input_data in the form of a str or unicode object, passes this to the server, with proper consideration for the encodings, and returns the Frog output as a list of tuples: (word,pos,lemma,morphology), each of these is a proper unicode object unless return_unicode is set to False, in which case raw strings will be returned. Return_unicode is no longer optional, it is fixed to True, parameter is still there only for backwards-compatibility.\"\"\"\n        if isinstance(input_data, list) or isinstance(input_data, tuple):\n            input_data = \" \".join(input_data)\n\n\n\n        input_data = u(input_data, source_encoding) #decode (or preferably do this in an earlier stage)\n        input_data = input_data.strip(' \t\n')\n\n        s = input_data.encode(self.server_encoding) +b'\r\n'\n        if not oldfrog: s += b'EOT\r\n'\n        self.socket.sendall(s) #send to socket in desired encoding\n        output = []\n\n        done = False\n        while not done:\n            data = b\"\"\n            while not data.endswith(b'\n'):\n                moredata = self.socket.recv(self.BUFSIZE)\n                if not moredata: break\n                data += moredata\n\n\n            data = u(data,self.server_encoding)\n\n\n            for line in data.strip(' \t\r\n').split('\n'):\n                if line == \"READY\":\n                    done = True\n                    break\n                elif line:\n                    line = line.split('\t') #split on tab\n                    if len(line) > 4 and line[0].isdigit(): #first column is token number\n                        if line[0] == '1' and output:\n                            if self.returnall:\n                                output.append( (None,None,None,None, None,None,None, None) )\n                            else:\n                                output.append( (None,None,None,None) )\n                        fields = line[1:]\n                        parse1=parse2=ner=chunk=\"\"\n                        word,lemma,morph,pos = fields[0:4]\n                        if len(fields) > 5:\n                            ner = fields[5]\n                        if len(fields) > 6:\n                            chunk = fields[6]\n                        if len(fields) >= 8:\n                            parse1 = fields[7]\n                            parse2 = fields[8]\n\n                        if len(fields) < 5:\n                            raise Exception(\"Can't process response line from Frog: \", repr(line), \" got unexpected number of fields \", str(len(fields) + 1))\n\n                        if self.returnall:\n                            output.append( (word,lemma,morph,pos,ner,chunk,parse1,parse2) )\n                        else:\n                            output.append( (word,lemma,morph,pos) )\n\n        return output"  # noqa: E501
    row_start = 40
    row_end = 98

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_9(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/clients/frogclient.py"
    question = "For each input word, supply the index of the corresponding output word."  # noqa: E501
    content = "def align(self,inputwords, outputwords):\n        \"\"\"For each inputword, provides the index of the outputword\"\"\"\n        alignment = []\n        cursor = 0\n        for inputword in inputwords:\n            if len(outputwords) > cursor and outputwords[cursor] == inputword:\n                alignment.append(cursor)\n                cursor += 1\n            elif len(outputwords) > cursor+1 and outputwords[cursor+1] == inputword:\n                alignment.append(cursor+1)\n                cursor += 2\n            else:\n                alignment.append(None)\n                cursor += 1\n        return alignment"  # noqa: E501
    row_start = 115
    row_end = 129

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_10(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/textprocessors.py"
    question = "Compute the commonalities between two sequences. Returns a list of (overlap, placement) pairs since there could be several common segments. The first element is the overlapping segment, while the second is -1 for overlap on the left, 0 if one segment is a subset of the other, 1 for overlap on the right, and 2 for an exact match."  # noqa: E501
    content = "def calculate_overlap(haystack, needle, allowpartial=True):\n    \"\"\"Calculate the overlap between two sequences. Yields (overlap, placement) tuples (multiple because there may be multiple overlaps!). The former is the part of the sequence that overlaps, and the latter is -1 if the overlap is on the left side, 0 if it is a subset, 1 if it overlaps on the right side, 2 if its an identical match\"\"\"\n    needle = tuple(needle)\n    haystack = tuple(haystack)\n    solutions = []\n\n    #equality check\n    if needle == haystack:\n        return [(needle, 2)]\n\n    if allowpartial:\n        minl =1\n    else:\n        minl = len(needle)\n\n    for l in range(minl,min(len(needle), len(haystack))+1):\n        #print \"LEFT-DEBUG\", l,\":\", needle[-l:], \" vs \", haystack[:l]\n        #print \"RIGHT-DEBUG\", l,\":\", needle[:l], \" vs \", haystack[-l:]\n        #Search for overlap left (including partial overlap!)\n        if needle[-l:] == haystack[:l]:\n            #print \"LEFT MATCH\"\n            solutions.append( (needle[-l:], -1) )\n        #Search for overlap right (including partial overlap!)\n        if needle[:l] == haystack[-l:]:\n            #print \"RIGHT MATCH\"\n            solutions.append( (needle[:l], 1) )\n\n    if len(needle) <= len(haystack):\n        options = list(iter(Windower(haystack,len(needle),beginmarker=None,endmarker=None)))\n        for option in options[1:-1]:\n            if option == needle:\n                #print \"SUBSET MATCH\"\n                solutions.append( (needle, 0) )\n\n    return solutions"  # noqa: E501
    row_start = 228
    row_end = 262

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_11(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/textprocessors.py"
    question = "Tokenizes a string and produces a list of tokens.\n\n    :input text: The text to be tokenized\n    :kind text: string\n    :params regexps: Regular expressions utilized as tokenizer rules during tokenization (default=_pynlpl.textprocessors.TOKENIZERRULES_)\n    :style regexps:  Tuple/list of regular expressions applied in tokenization\n    :output: Produces a list of tokens\n\n    Illustrations:\n\n    >>> for token in tokenize(\"This is a test.\"):\n    ...    print(token)\n    This\n    is\n    a\n    test\n    ."  # noqa: E501
    content = "def tokenize(text, regexps=TOKENIZERRULES):\n    \"\"\"Tokenizes a string and returns a list of tokens\n\n    :param text: The text to tokenise\n    :type text: string\n    :param regexps: Regular expressions to use as tokeniser rules in tokenisation (default=_pynlpl.textprocessors.TOKENIZERRULES_)\n    :type regexps:  Tuple/list of regular expressions to use in tokenisation\n    :rtype: Returns a list of tokens\n\n    Examples:\n\n    >>> for token in tokenize(\"This is a test.\"):\n    ...    print(token)\n    This\n    is\n    a\n    test\n    .\n\n\n    \"\"\"\n\n    for i,regexp in list(enumerate(regexps)):\n        if isstring(regexp):\n            regexps[i] = re.compile(regexp)\n\n    tokens = []\n    begin = 0\n    for i, c in enumerate(text):\n        if begin > i:\n            continue\n        elif i == begin:\n            m = False\n            for regexp in regexps:\n                m = regexp.findall(text[i:i+300])\n                if m:\n                    tokens.append(m[0])\n                    begin = i + len(m[0])\n                    break\n            if m: continue\n\n        if c in string.punctuation or c in WHITESPACE:\n            prev = text[i-1] if i > 0 else \"\"\n            next = text[i+1] if i < len(text)-1 else \"\"\n\n            if (c == '.' or c == ',') and prev.isdigit() and next.isdigit():\n                #punctuation in between numbers, keep as one token\n                pass\n            elif (c == \"'\" or c == \"`\") and prev.isalpha() and next.isalpha():\n                #quote in between chars, keep...\n                pass\n            elif c not in WHITESPACE and next == c: #group clusters of identical punctuation together\n                continue\n            elif c == '\r' and prev == '\n':\n                #ignore\n                begin = i+1\n                continue\n            else:\n                token = text[begin:i]\n                if token: tokens.append(token)\n\n                if c not in WHITESPACE:\n                    tokens.append(c) #anything but spaces and newlines (i.e. punctuation) counts as a token too\n                begin = i + 1 #set the begin cursor\n\n    if begin <= len(text) - 1:\n        token = text[begin:]\n        tokens.append(token)\n\n    return tokens"  # noqa: E501
    row_start = 317
    row_end = 386

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_12(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/textprocessors.py"
    question = "Take a sequence of words and divide it into individual sentences; each sentence should then be returned as a list containing the words (or tokens) from that sentence. The final output should be a list where each element is a list of the words forming a distinct sentence."  # noqa: E501
    content = "def split_sentences(tokens):\n    \"\"\"Split sentences (based on tokenised data), returns sentences as a list of lists of tokens, each sentence is a list of tokens\"\"\"\n    begin = 0\n    for i, token in enumerate(tokens):\n        if is_end_of_sentence(tokens, i):\n            yield tokens[begin:i+1]\n            begin = i+1\n    if begin <= len(tokens)-1:\n        yield tokens[begin:]"  # noqa: E501
    row_start = 403
    row_end = 411

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_13(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/textprocessors.py"
    question = "Please convert characters with diacritical marks to a flat ASCII version."  # noqa: E501
    content = "def strip_accents(s, encoding= 'utf-8'):\n    \"\"\"Strip characters with diacritics and return a flat ascii representation\"\"\"\n    if sys.version < '3':\n        if isinstance(s,unicode):\n           return unicodedata.normalize('NFKD', s).encode('ASCII', 'ignore')\n        else:\n           return unicodedata.normalize('NFKD', unicode(s,encoding)).encode('ASCII', 'ignore')\n    else:\n        if isinstance(s,bytes): s = str(s,encoding)\n        return str(unicodedata.normalize('NFKD', s).encode('ASCII', 'ignore'),'ascii')"  # noqa: E501
    row_start = 415
    row_end = 424

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_14(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/textprocessors.py"
    question = "Swap tokens in a sequence within a specified maximum distance, exhaustively swapping every token up to that distance. This operation is a part of all possible permutations."  # noqa: E501
    content = "def swap(tokens, maxdist=2):\n    \"\"\"Perform a swap operation on a sequence of tokens, exhaustively swapping all tokens up to the maximum specified distance. This is a subset of all permutations.\"\"\"\n    assert maxdist >= 2\n    tokens = list(tokens)\n    if maxdist > len(tokens):\n        maxdist = len(tokens)\n    l = len(tokens)\n    for i in range(0,l - 1):\n        for permutation in permutations(tokens[i:i+maxdist]):\n            if permutation != tuple(tokens[i:i+maxdist]):\n                newtokens = tokens[:i]\n                newtokens += permutation\n                newtokens += tokens[i+maxdist:]\n                yield newtokens\n        if maxdist == len(tokens):\n            break"  # noqa: E501
    row_start = 426
    row_end = 441

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_15(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/textprocessors.py"
    question = "Identify a key term within a given set of tokens, then extract the surrounding context. The size of the context is defined by the number of words on both sides. In instances where the key term is composed of multiple words, it should be provided as a tuple or a list."  # noqa: E501
    content = "def find_keyword_in_context(tokens, keyword, contextsize=1):\n    \"\"\"Find a keyword in a particular sequence of tokens, and return the local context. Contextsize is the number of words to the left and right. The keyword may have multiple word, in which case it should to passed as a tuple or list\"\"\"\n    if isinstance(keyword,tuple) and isinstance(keyword,list):\n        l = len(keyword)\n    else:\n        keyword = (keyword,)\n        l = 1\n    n = l + contextsize*2\n    focuspos = contextsize + 1\n    for ngram in Windower(tokens,n,None,None):\n        if ngram[focuspos:focuspos+l] == keyword:\n            yield ngram[:focuspos], ngram[focuspos:focuspos+l],ngram[focuspos+l+1:]"  # noqa: E501
    row_start = 444
    row_end = 455

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_16(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "Fetch the subsequent element in line; this will dequeue it."  # noqa: E501
    content = "def pop(self):\n        \"\"\"Retrieve the next element in line, this will remove it from the queue\"\"\"\n        e = self.data[self.start]\n        self.start += 1\n        if self.start > 5 and self.start > len(self.data)//2:\n            self.data = self.data[self.start:]\n            self.start = 0\n        return e"  # noqa: E501
    row_start = 65
    row_end = 72

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_17(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "Inserts an item into the priority queue at the correct position; returns True if successful, False if the item was blocked due to an undesirable score."  # noqa: E501
    content = "def append(self, item):\n        \"\"\"Adds an item to the priority queue (in the right place), returns True if successfull, False if the item was blocked (because of a bad score)\"\"\"\n        f = self.f(item)\n        if callable(f):\n            score = f()\n        else:\n            score = f\n\n        if not self.duplicates:\n            for s, i in self.data:\n                if s == score and item == i:\n                    #item is a duplicate, don't add it\n                    return False\n\n        if self.length and len(self.data) == self.length:\n                #Fixed-length priority queue, abort when queue is full and new item scores worst than worst scoring item.\n                if self.minimize:\n                    worstscore = self.data[-1][0]\n                    if score >= worstscore:\n                        return False\n                else:\n                    worstscore = self.data[0][0]\n                    if score <= worstscore:\n                        return False\n\n        if self.blockworse and self.bestscore != None:\n            if self.minimize:\n                if score > self.bestscore:\n                    return False\n            else:\n                if score < self.bestscore:\n                    return False\n        if self.blockequal and self.bestscore != None:\n            if self.bestscore == score:\n                return False\n        if (self.bestscore == None) or (self.minimize and score < self.bestscore) or (not self.minimize and score > self.bestscore):\n            self.bestscore = score\n        bisect.insort(self.data, (score, item))\n        if self.length:\n            #fixed length queue: queue is now too long, delete worst items\n            while len(self.data) > self.length:\n                if self.minimize:\n                    del self.data[-1]\n                else:\n                    del self.data[0]\n        return True"  # noqa: E501
    row_start = 97
    row_end = 142

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_18(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "Fetch the next item in line; this will dequeue it."  # noqa: E501
    content = "def pop(self):\n        \"\"\"Retrieve the next element in line, this will remove it from the queue\"\"\"\n        if self.minimize:\n            return self.data.pop(0)[1]\n        else:\n            return self.data.pop()[1]"  # noqa: E501
    row_start = 173
    row_end = 178

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_19(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "Retrieve the score for item x (economical lookup). Note that item 0 consistently ranks as the top item."  # noqa: E501
    content = "def score(self, i):\n        \"\"\"Return the score for item x (cheap lookup), Item 0 is always the best item\"\"\"\n        if self.minimize:\n            return self.data[i][0]\n        else:\n            return self.data[(-1 * i) - 1][0]"  # noqa: E501
    row_start = 181
    row_end = 186

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_20(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "retain only the first (and best) n elements"  # noqa: E501
    content = "def prune(self, n):\n        \"\"\"prune all but the first (=best) n items\"\"\"\n        if self.minimize:\n            self.data = self.data[:n]\n        else:\n            self.data = self.data[-1 * n:]"  # noqa: E501
    row_start = 188
    row_end = 193

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_21(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "randomly reduce the list to n elements, ignoring their scores"  # noqa: E501
    content = "def randomprune(self,n):\n        \"\"\"prune down to n items at random, disregarding their score\"\"\"\n        self.data = random.sample(self.data, n)"  # noqa: E501
    row_start = 196
    row_end = 198

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_22(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "Removes all elements below or above a specific score from the queue, based on whether minimize is True or False. Keep in mind: For better efficiency, it is suggested to use blockworse=True / blockequal=True instead! This prevents the inclusion of 'inferior' items."  # noqa: E501
    content = "def prunebyscore(self, score, retainequalscore=False):\n        \"\"\"Deletes all items below/above a certain score from the queue, depending on whether minimize is True or False. Note: It is recommended (more efficient) to use blockworse=True / blockequal=True instead! Preventing the addition of 'worse' items.\"\"\"\n        if retainequalscore:\n            if self.minimize:\n                f = lambda x: x[0] <= score\n            else:\n                f = lambda x: x[0] >= score\n        else:\n            if self.minimize:\n                f = lambda x: x[0] < score\n            else:\n                f = lambda x: x[0] > score\n        self.data = filter(f, self.data)"  # noqa: E501
    row_start = 205
    row_end = 217

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_23(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "Could you please add an element to the Tree?"  # noqa: E501
    content = "def append(self, item):\n        \"\"\"Add an item to the Tree\"\"\"\n        if not isinstance(item, Tree):\n            return ValueError(\"Can only append items of type Tree\")\n        if not self.children: self.children = []\n        item.parent = self\n        self.children.append(item)"  # noqa: E501
    row_start = 261
    row_end = 267

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_24(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "The size refers to the total number of nodes within the trie, including the current node."  # noqa: E501
    content = "def size(self):\n        \"\"\"Size is number of nodes under the trie, including the current node\"\"\"\n        if self.children:\n            return sum( ( c.size() for c in self.children.values() ) ) + 1\n        else:\n            return 1"  # noqa: E501
    row_start = 361
    row_end = 366

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_25(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/datatypes.py"
    question = "Depth-first search navigates through a trie, returning all encountered nodes (by default, only the leaves)."  # noqa: E501
    content = "def walk(self, leavesonly=True, maxdepth=None, _depth = 0):\n        \"\"\"Depth-first search, walking through trie, returning all encounterd nodes (by default only leaves)\"\"\"\n        if self.children:\n            if not maxdepth or (maxdepth and _depth < maxdepth):\n                for key, child in self.children.items():\n                    if child.leaf():\n                        yield child\n                    else:\n                        for results in child.walk(leavesonly, maxdepth, _depth + 1):\n                            yield results"  # noqa: E501
    row_start = 392
    row_end = 401

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_26(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/formats/sonar.py"
    question = "Go through all the sentences (sentence_id, sentence) within the document; note that each sentence consists of a list of 4-tuples (word, id, pos, lemma)."  # noqa: E501
    content = "def sentences(self):\n        \"\"\"Iterate over all sentences (sentence_id, sentence) in the document, sentence is a list of 4-tuples (word,id,pos,lemma)\"\"\"\n        prevp = 0\n        prevs = 0\n        sentence = [];\n        sentence_id = \"\"\n        for word, id, pos, lemma in iter(self):\n            try:\n                doc_id, ptype, p, s, w = re.findall('([\\w\\d-]+)\\.(p|head)\\.(\\d+)\\.s\\.(\\d+)\\.w\\.(\\d+)',id)[0]\n                if ((p != prevp) or (s != prevs)) and sentence:\n                    yield sentence_id, sentence\n                    sentence = []\n                    sentence_id = doc_id + '.' + ptype + '.' + str(p) + '.s.' + str(s)\n                prevp = p\n            except IndexError:\n                doc_id, s, w = re.findall('([\\w\\d-]+)\\.s\\.(\\d+)\\.w\\.(\\d+)',id)[0]\n                if s != prevs and sentence:\n                    yield sentence_id, sentence\n                    sentence = []\n                    sentence_id = doc_id + '.s.' + str(s)\n            sentence.append( (word,id,pos,lemma) )\n            prevs = s\n        if sentence:\n            yield sentence_id, sentence"  # noqa: E501
    row_start = 85
    row_end = 108

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_27(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/formats/sonar.py"
    question = "Extract paragraphs, return a list of plain text (!) paragraphs."  # noqa: E501
    content = "def paragraphs(self, with_id = False):\n        \"\"\"Extracts paragraphs, returns list of plain-text(!) paragraphs\"\"\"\n        prevp = 0\n        partext = []\n        for word, id, pos, lemma in iter(self):\n            doc_id, ptype, p, s, w = re.findall('([\\w\\d-]+)\\.(p|head)\\.(\\d+)\\.s\\.(\\d+)\\.w\\.(\\d+)',id)[0]\n            if prevp != p and partext:\n                    yield ( doc_id + \".\" + ptype + \".\" + prevp , \" \".join(partext) )\n                    partext = []\n            partext.append(word)\n            prevp = p\n        if partext:\n            yield (doc_id + \".\" + ptype + \".\" + prevp, \" \".join(partext) )"  # noqa: E501
    row_start = 110
    row_end = 122

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_28(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/formats/sonar.py"
    question = "verifies if the documnt is legitimate"  # noqa: E501
    content = "def validate(self, formats_dir=\"../formats/\"):\n        \"\"\"checks if the document is valid\"\"\"\n        #TODO: download XSD from web\n        if self.inline:\n            xmlschema = ElementTree.XMLSchema(ElementTree.parse(StringIO(\"\n\".join(open(formats_dir+\"dcoi-dsc.xsd\").readlines()))))\n            xmlschema.assertValid(self.tree)\n            #return xmlschema.validate(self)\n        else:\n            xmlschema = ElementTree.XMLSchema(ElementTree.parse(StringIO(\"\n\".join(open(formats_dir+\"dutchsemcor-standalone.xsd\").readlines()))))\n            xmlschema.assertValid(self.tree)"  # noqa: E501
    row_start = 235
    row_end = 244

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)

@pytest.mark.asyncio
async def test_rag_search_datasets_29(run_whole_rag_process, create_test_rag_data):
    repository_name = "proycon/pynlpl"
    path = "pynlpl/formats/sonar.py"
    question = "Executes an XPath expression utilizing the appropriate namespaces."  # noqa: E501
    content = "def xpath(self, expression):\n        \"\"\"Executes an xpath expression using the correct namespaces\"\"\"\n        global namespaces\n        return self.tree.xpath(expression, namespaces=namespaces)"  # noqa: E501
    row_start = 247
    row_end = 250

    except_list = [
        FileSnippet(
            path=path,
            content=content,
            row_start=row_start,
            row_end=row_end # 虚拟的行数
        ),
    ]

    test_rag_data = create_test_rag_data(
        title="RAG: 数据集测试",
        description="file_path: {path}",
        codezone_id="693595671193849856",
        codezone_description="",
        codezone_git_url=f"**************:{repository_name}.git",
        question=question,
        rag_expect=except_list
    )

    await run_whole_rag_process(test_rag_data)
