# Heracles - AI Coding Agent

The name "<PERSON><PERSON>" originates from <PERSON> in ancient Greek mythology, a hero renowned for his strength and wisdom. Likewise, AI Agent <PERSON><PERSON> aims to embody the "strength" and "wisdom" of modern technology, maximizing the ubiquity and simulation capabilities of computers.

## Project Architecture

### Overview Diagrams

The project consists of two main architectural views:
- Role Architecture: Illustrates the agent role system and interactions (clacky-ai-role-arch.png)
- Runtime Design: Shows the runtime components and data flow (clacky-ai-runtime-design.png)

### Key Components
- Agent Roles: Specialized components for different tasks (analysis, coding, planning etc.)
- Agent Memory: Manages context and state across interactions
- Agent Workspace: Handles file operations and tool integrations
- Agent Controller: Orchestrates role execution and manages lifecycle

## Project Overview

### Key Features
- RAG-based AI assistant with task-oriented design
- Support for multiple LLM providers (OpenAI, Deepseek, Llama)
- Task state management and execution tracking
- Code analysis and generation capabilities
- Extensible task role system

### Project Structure

```
heracles/
├── core/                 # Core utilities and configurations
├── server/               # FastAPI server implementation
├── agent_roles/          # Implementations of various agent roles
├── tests/                # Test cases and fixtures
└── llm_tests/            # LLM-specific test cases
```

## Getting Started

### Prerequisites
- Python 3.11+
- FastAPI
- LLM provider (OpenAI, Deepseek, or Llama)

### Installation

1. Clone the repository
```bash
git clone https://github.com/clacky-ai/clacky-ai-agent.git
cd clacky-ai-agent

# Install dependencies
pip install poetry
make install
```

### Configuration

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Configure the following essential environment variables in `.env`:

#### LLM Configuration
- `LLM_MODEL`: The main LLM model (e.g. "ollama/llama3")
- `LLM_API_KEY`: API key for the LLM provider
- `LLM_BASE_URL`: Base URL for LLM API endpoint

#### Redis Cache Configuration
- `CACHE_REDIS_HOST`: Redis server host (required for caching)
- `CACHE_REDIS_PORT`: Redis server port
- `CACHE_REDIS_DB`: Redis database number
- `CACHE_REDIS_DAY`: Cache expiration in days (default: 1)

#### Vector Database Configuration
- `ADMIN_VECTOR_DB_HOST`: Vector DB server host
- `ADMIN_VECTOR_DB_PORT`: Vector DB server port
- `ADMIN_VECTOR_DB_API_KEY`: API key for vector database

## Dependencies

### Redis
Redis is used as the caching layer for:
- LLM response caching
- Agent state persistence
- Temporary data storage

Make sure Redis is installed and running before starting the server.

### Running the Server

```bash
make dev
```

The server will start on `http://127.0.0.1:3020`. You can access the demo page in `/demo` or API documentation at `/docs`.

## Testing

### Unit Tests
Run unit tests with coverage report:
```bash
poetry run pytest tests/ --cov=heracles --cov-report term-missing
```

### LLM Tests
Run LLM integration tests:
```bash
poetry run pytest llm_tests/basic/ # Basic LLM tests
poetry run pytest llm_tests/smoke/ # Smoke tests
poetry run pytest llm_tests/rag/   # RAG system tests
```

Test coverage goals:
- Aim for 100% code coverage
- Use `# pragma: no cover` for hard-to-test sections (sparingly)

#### Adding New Items to the Test Dataset

The LLM test dataset is stored in `llm_tests/datasets/github_repos/projects.yml`. To add a new test case, you can directly edit this file using the following YAML format:

```yaml
- name: "project-name"                      # Name of the project
  description: "project description"        # Brief description of the project
  github_repo: "repo-owner/repo-name"       # URL of the GitHub repository
  env_code: "environment-code"              # Must be defined in `llm_tests/datasets/env_info.json`
  states:
    - id: "state-id"                # Unique identifier for the state_id
      commit_id: "commit-id"        # Optional: Specific commit hash
      branch_name: "branch-name"    # Optional: Branch name (use either commit_id or branch_name)
      path: "path-to-code"          # Optional: Path to code root (defaults to repository root)
```

After adding the new item, you can update the codezone data by running:
```bash
poetry run python llm_tests/datasets/cli.py update-codezone-data
```

> **Note**: This command will only generate the environment specified in your `.env::PAAS_DOMAIN_URL` configuration.

Once the command completes, the new item will be available in `llm_tests/datasets/github_repos/codezones.json`.

### Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Submit a pull request

## License

This project is under the MIT License. See the [LICENSE](LICENSE) file for details.
