name: Delete Feature Deployment

on:
  workflow_dispatch:

jobs:
  delete-feature:
    name: Delete Feature Resources
    runs-on: ubuntu-latest
    steps:
      - name: Set branch name
        id: branch
        run: |
          safe_branch=$(echo "${GITHUB_INPUT_BRANCH}" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9-]/-/g' | sed 's/^-*//;s/-*$//')
          echo "branch_name=$safe_branch" >> $GITHUB_OUTPUT
        env:
          GITHUB_INPUT_BRANCH: ${{ github.ref_name }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ${{ vars.AWS_REGION }}

      - name: Update kubeconfig
        run: |
            aws eks update-kubeconfig --name ${{ vars.EKS_DEVELOP_NAME }} --region ${{ vars.AWS_REGION }}

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.15.0

      - name: Delete Helm Release
        run: |
          TAG="${{ steps.branch.outputs.branch_name }}"
          helm uninstall clacky-ai-agent-$TAG -n feature
