name: _Run cov Tests

on:
  workflow_call:
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true

env:
  PYTHON_VERSION: "3.11"

jobs:
  cov-test:
    name: cov Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install poetry via pipx
        run: pipx install poetry
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: "poetry"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Install Python dependencies using Poetry
        run: poetry install

      - name: Initialize config
        run: cp .env.example .env

      - name: Run Tests
        run: make test > result.log

      - name: Upload coverage to S3
        if:  github.event_name == 'push'
        run: aws s3 cp result.log s3://llm-smoke-report/coverage/${GITHUB_REF#refs/heads/}.log

      - name: Retrieve previous coverage
        if:  github.event_name == 'pull_request'
        run: |
          if aws s3 cp s3://llm-smoke-report/coverage/${{ github.event.pull_request.base.ref }}.log previous_coverage.log ; then
            previous_coverage=$(grep 'TOTAL' previous_coverage.log | awk '{print $4}')
          else
            previous_coverage="0%"
          fi
          current_coverage=$(grep 'TOTAL' result.log | awk '{print $4}')
          echo "previous_coverage=$previous_coverage" >> $GITHUB_ENV
          echo "current_coverage=$current_coverage" >> $GITHUB_ENV

      - name: Comment on PR
        uses: actions/github-script@v6
        if:  github.event_name == 'pull_request'
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `Previous Coverage is: **${{ env.previous_coverage }}**\nCurrent Coverage is: **${{ env.current_coverage }}**`
            })

      - name: Check coverage decrease
        if: github.event_name == 'pull_request'
        run: |
          previous_value=${previous_coverage/\%/}
          current_value=${current_coverage/\%/}
          if (( $(echo "$current_value < $previous_value" | bc -l) )); then
            echo "::error::Test coverage decreased from $previous_coverage to $current_coverage, please check and increase the coverage!"
            exit 1
          fi
