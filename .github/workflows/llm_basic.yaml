# .github/workflows/manual_trigger.yml
name: llm test basic

on:
  push:
    branches:
      - staging
    paths-ignore:
    - '**/*.md'
    - 'docs/**'
  workflow_dispatch:

env:
  PYTHON_VERSION: "3.11"

jobs:
  basic-test:
    name: basic Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install poetry via pipx
        run: pipx install poetry
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: "poetry"

      - name: Install Python dependencies using Poetry
        run: poetry install

      - name: Initialize config
        run: printf "%s" "${{ secrets.LLM_TEST_ENV }}" > .env

      - name: Clean up
        run: rm -f llm_tests/llm_metrics.db || true

      - name: Run Tests
        run: poetry run pytest -n auto llm_tests/basic

      - name: Webhook supporting
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY_LLM }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"llm test basic [执行成功]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

  notify_failure:
    if: failure()
    runs-on: ubuntu-latest
    needs:
      - basic-test
    steps:
      - name: Send Webhook on Failure
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY_LLM }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"llm test basic [执行失败]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"
