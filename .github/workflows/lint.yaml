name: _Run Lint

on:
  workflow_call:

env:
  PYTHON_VERSION: "3.11"

jobs:
  lint:
    name: Lint python
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
      - name: Install pre-commit
        run: pip install pre-commit==3.7.1
      - name: Run pre-commit hooks
        run: pre-commit run -a --show-diff-on-failure
