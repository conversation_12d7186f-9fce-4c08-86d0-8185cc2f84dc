name: Build and Deploy Feature

on:
  workflow_dispatch:
    inputs:
      myenv:
        description: '自定义环境变量，使用scripts/flatten_env.py 处理'
        required: true

concurrency:
    group: ${{ github.workflow }}-${{ github.ref_name }}-${{ github.event.pull_request.number || github.sha }}
    cancel-in-progress: true

env:
  PYTHON_VERSION: "3.11"

jobs:
  build-docker:
    name: Build Docker Image
    runs-on: ubuntu-latest
    outputs:
      branch_name: ${{ steps.branch.outputs.branch_name }}
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.ref_name }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Set branch name
        id: branch
        run: |
          safe_branch=$(echo "${GITHUB_INPUT_BRANCH}" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9-]/-/g' | sed 's/^-*//;s/-*$//')
          echo "branch_name=$safe_branch" >> $GITHUB_OUTPUT
        env:
          GITHUB_INPUT_BRANCH: ${{ github.ref_name }}
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v3
        with:
          images: ghcr.io/clacky-ai/clacky-ai-agent
          tags: |
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha
          flavor: |
            latest=auto
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v1
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_ACTOR }}
          password: ${{ secrets.GHCR_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v2
        with:
            context: .
            file: ./.deploy/Dockerfile
            push: true
            tags: ${{ steps.meta.outputs.tags }}
            labels: ${{ steps.meta.outputs.labels }}
            cache-from: type=gha
            cache-to: type=gha,mode=max


  deploy-feature:
    needs: build-docker
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ${{ vars.AWS_REGION }}

      - name: Update kubeconfig
        run: |
            aws eks update-kubeconfig --name ${{ vars.EKS_DEVELOP_NAME }} --region ${{ vars.AWS_REGION }}

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.15.0
      - name: Install helm-s3 plugin
        run: helm plugin install https://github.com/hypnoglow/helm-s3.git
      - name: Add Helm repository
        run: helm repo add dao42 s3://dao42-charts
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: Update Helm repositories
        run: helm repo update
      
      - name: Write myenv to values.yaml
        run: |
          echo "env:" > myenv-values.yaml
          echo "${{ github.event.inputs.myenv }}" | tr ';' '\n' | while IFS= read -r line; do
            if [[ -n "$line" && ! "$line" =~ ^# ]]; then
              key=$(echo "$line" | cut -d'=' -f1)
              value=$(echo "$line" | cut -d'=' -f2-)
              echo "  $key: $value" >> myenv-values.yaml
            fi
          done

      - name: Print myenv-values.yaml
        run: cat myenv-values.yaml

      - name: Deploy to EKS
        run: |
          IMG_TAG="sha-$(echo ${{ github.sha }}|head -c 7)"
          TAG="${{ needs.build-docker.outputs.branch_name }}"
          helm upgrade --install clacky-ai-agent-$TAG dao42/clacky-ai-agent-feature  --version=0.1.3 -n feature --set image.tag=$IMG_TAG --set ingress.hostPrefix=$TAG -f myenv-values.yaml

      - name: Webhook supporting
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.FEAT_WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"Clacky-AI-Agent **feature** [部署即将完成]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})\n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }}) \n> HOST: [https://${{ needs.build-docker.outputs.branch_name }}-develop.agent.clackyai.com](https://${{ needs.build-docker.outputs.branch_name }}-develop.agent.clackyai.com)\"
                  }
                }"
