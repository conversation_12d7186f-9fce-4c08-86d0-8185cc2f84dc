name: Build and Deploy Prod

on:
  push:
    branches:
      - main
    paths-ignore:
    - '**/*.md'
    - 'docs/**'
    - 'heracles/server/admin/frontend/**'
    - '.deploy-playbook/**'
  pull_request:
    branches:
      - main
    paths-ignore:
    - '**/*.md'
    - 'docs/**'
    - 'heracles/server/admin/frontend/**'
    - '.deploy-playbook/**'


concurrency:
    group: ${{ github.workflow }}-${{ github.ref_name }}-${{ github.event.pull_request.number || github.sha }}
    cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

env:
  PYTHON_VERSION: "3.11"


jobs:
  lint:
    uses: ./.github/workflows/lint.yaml

  test:
    uses: ./.github/workflows/test.yaml

  cov-test:
    permissions:
      actions: read # Only required for private GitHub Repo
      contents: read
      deployments: write
      pull-requests: write
      issues: write
    uses: ./.github/workflows/cov-test.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}


  build-docker:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [lint,test]
    steps:
      - name: Webhook starting
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.PROD_ACTION_WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"Clacky-AI-Agent Prod [开始构建]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"
      - uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v3
        with:
          images: ghcr.io/clacky-ai/clacky-ai-agent
          tags: |
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha
          flavor: |
            latest=auto
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v1
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_ACTOR }}
          password: ${{ secrets.GHCR_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v2
        with:
            context: .
            file: ./.deploy/Dockerfile
            push: true
            tags: ${{ steps.meta.outputs.tags }}
            labels: ${{ steps.meta.outputs.labels }}
            cache-from: type=gha
            cache-to: type=gha,mode=max

  deploy-prod:
    needs: build-docker
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    # if:  github.event_name == 'push' 
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ${{ vars.AWS_REGION }}

      - name: Update kubeconfig
        run: |
            aws eks update-kubeconfig --name ${{ vars.EKS_PROD_NAME }} --region ${{ vars.AWS_REGION }}

      - name: Set up Helm
        uses: azure/setup-helm@v1
        with:
          version: v3.15.0
      - name: Install helm-s3 plugin
        run: helm plugin install https://github.com/hypnoglow/helm-s3.git
      - name: Add Helm repository
        run: helm repo add dao42 s3://dao42-charts 
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: Update Helm repositories
        run: helm repo update

      - name: Deploy to EKS
        run: |
          TAG="sha-$(echo ${{ github.sha }}|head -c 7)"
          helm upgrade --install clacky-ai-agent-prod dao42/clacky-ai-agent  --version=0.1.2 -n clacky-ai-agent --set image.tag=$TAG -f .deploy/helm/value-prod.yaml
          
      - name: Webhook supporting
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.PROD_ACTION_WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"Clacky-AI-Agent **Prod** [部署即将完成]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"
              

  notify_failure:
    if: failure()  
    runs-on: ubuntu-latest
    needs: 
      - deploy-prod
      - build-docker
    steps:
      - name: Send Webhook on Failure
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.PROD_ACTION_WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"Clacky-AI-Agent Prod [部署失败]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"