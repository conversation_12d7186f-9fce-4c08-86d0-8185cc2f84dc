# .github/workflows/manual_trigger.yml
name: llm test smoke with rag

on:
  workflow_dispatch:

env:
  PYTHON_VERSION: "3.11"

jobs:
  smoke-test:
    name: smoke Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install poetry via pipx
        run: pipx install poetry
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: "poetry"

      - name: Generate timestamp-based folder name
        id: generate_timestamp
        run: echo "folder_name=$(date +'%Y%m%d%H%M%S')" >> $GITHUB_ENV

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Install Python dependencies using Poetry
        run: poetry install

      - name: Initialize config
        run: printf "%s" "${{ secrets.LLM_TEST_ENV }}" > .env

      - name: Run Tests
        run: AUTO_SCORE=1 WITH_ANALYZE=1 WITH_RAG=1 poetry run pytest -s llm_tests/smoke --html=report.html

      - name: Install AWS CLI
        if: always()  # 确保此步骤总是执行
        run: sudo apt-get install -y awscli

      - name: Upload assets to S3
        if: always()  # 确保此步骤总是执行
        run: |
          aws s3 cp assets s3://llm-smoke-report/${{ env.folder_name }}/assets --recursive

      - name: Upload report.html to S3
        if: always()  # 确保此步骤总是执行
        run: |
          aws s3 cp report.html s3://llm-smoke-report/${{ env.folder_name }}/

      - name: Print report.html URL
        if: always()  # 确保此步骤总是执行
        run: |
          REPORT_URL="https://llm-smoke-report.s3.amazonaws.com/${{ env.folder_name }}/report.html"
          echo "Report URL: $REPORT_URL"

      - name: Webhook supporting
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY_LLM }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"llm test smoke with rag [执行成功]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

  notify_failure:
    if: failure()
    runs-on: ubuntu-latest
    needs:
      - smoke-test
    steps:
      - name: Send Webhook on Failure
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY_LLM }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"llm test smoke with rag [执行失败]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"
