name: _Run Unit Tests

on:
  workflow_call:

env:
  PYTHON_VERSION: "3.11"

jobs:
  unit-test:
    name: Unit Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install poetry via pipx
        run: pipx install poetry
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: "poetry"

      - name: Install Python dependencies using Poetry
        run: poetry install

      - name: Initialize config
        run: cp .env.example .env

      - name: Run Tests
        run: poetry run pytest -n 2 tests
