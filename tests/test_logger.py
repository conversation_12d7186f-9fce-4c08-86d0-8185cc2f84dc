import logging
import os
import uuid

from heracles.core.logger import get_logger, get_log_level, setup_logger, InterceptHandler


def test_logger_append_file(monkeypatch):
    os.environ['LOG_FILE_OPTIONAL'] = 'app.log'

    logger = get_logger()
    logger.bind(trace_id=str(uuid.uuid4())).info("start test")

    os.environ.pop('LOG_FILE_OPTIONAL')


def test_logger_output_terminal(monkeypatch):
    if os.getenv("LOG_FILE_OPTIONAL") is not None:
        os.environ.pop('LOG_FILE_OPTIONAL')

    setup_logger(get_logger())

    def func(a, b):
        return a / b

    logger = get_logger()
    try:
        func(1, 0)
    except ZeroDivisionError:
        logger.exception("Math error occurred")


def test_log_level_configuration():
    # Test default log level
    if 'LOG_LEVEL_OPTIONAL' in os.environ:
        del os.environ['LOG_LEVEL_OPTIONAL']
    assert get_log_level() == 10  # DEBUG level

    # Test different log levels
    log_levels = {
        'CRITICAL': 50,
        'ERROR': 40,
        'WARNING': 30,
        'INFO': 20,
        'DEBUG': 10
    }

    for level_name, level_value in log_levels.items():
        os.environ['LOG_LEVEL_OPTIONAL'] = level_name
        assert get_log_level() == level_value


def test_get_log_level():
    os.environ['LOG_LEVEL_OPTIONAL'] = "debbg"
    assert get_log_level() == logging.DEBUG


def test_intercept_handler_value_error():
    # Create a custom log record with an invalid level name
    record = logging.LogRecord(
        name="test",
        level=999,  # Invalid level number
        pathname="test.py",
        lineno=1,
        msg="Test message",
        args=(),
        exc_info=None
    )
    record.levelname = "INVALID_LEVEL"  # Set an invalid level name

    # Create and use the handler
    handler = InterceptHandler()
    handler.emit(record)  # Should not raise any exception

    # The handler should have handled the ValueError gracefully
    # and used the record.levelno as fallback
    assert True  # If we get here, no exception was raised
