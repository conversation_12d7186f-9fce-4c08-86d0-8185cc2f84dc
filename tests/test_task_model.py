import pytest

from heracles.core.schema.models.task_model import TaskModel, TaskStepModel, TaskActionModel
from heracles.core.schema.task import ActionType, CommandLifetimeType
from heracles.core.exceptions import IDEServerFileNotFoundError


def test_task_action_model_invalid_action():
    # action 类型不正确，pydantic 会抛出异常
    with pytest.raises(ValueError, match="Input should be"):
        TaskActionModel(
            action='invalid_action', # type: ignore
            path='test/path',
            target='',
            detailed_requirement='Test requirement',
            references=[]
        )

def test_task_action_model_invalid_multiple_actions():
    # 验证一个文件不能同时被多个 action 修改和删除
    with pytest.raises(ValueError, match="cannot be deleted and modified by multiple actions"):
        TaskStepModel(
            title='Test Step',
            task_actions=[
                TaskActionModel(
                    action=ActionType.ADD_FILE,
                    path='test/path1',
                    detailed_requirement='Test requirement 1',
                    references=[]
                ),
                TaskActionModel(
                    action=ActionType.DELETE_FILE,
                    path='test/path1'
                )
            ]
        )

def test_task_action_model_invalid_detailed_requirement():
    # 验证 action_object.detailed_requirement 不能超过1000字符
    with pytest.raises(ValueError, match="detailed_requirement cannot exceed 5000 characters"):
        TaskActionModel(
            action=ActionType.MODIFY_FILE,
            path="controller.rb",
            target="writeControllerClass",
            detailed_requirement="x" * 5001
        )

    # 验证 action_object.references 每个元素不能超过1000字符
    with pytest.raises(ValueError, match="each reference item cannot exceed 1000 characters"):
        TaskActionModel(
            action=ActionType.MODIFY_FILE,
            path="controller.rb",
            target="writeControllerClass",
            references=["x" * 1001]
        )

def test_task_model_invalid_multiple_actions():
    # 验证一个文件不能同时被多个 action 修改和删除
    with pytest.raises(ValueError, match="cannot be deleted and modified by multiple actions"):
        TaskModel(
            title='Test Task',
            description='Test Description',
            task_steps=[
                TaskStepModel(
                    title='Test Step',
                    task_actions=[
                        TaskActionModel(
                            action=ActionType.ADD_FILE,
                            path='test/path1',
                            target='',
                            detailed_requirement='Test requirement 1',
                            references=[]
                        ),
                    ]
                ),
                TaskStepModel(
                    title='Test Step',
                    task_actions=[
                        TaskActionModel(
                            action=ActionType.DELETE_FILE,
                            path='test/path1',
                            target='',
                            detailed_requirement='Test requirement 2',
                            references=[]
                        )
                    ]
                )
            ]
        )

def test_task_step_model_from_dict():
    task_step_model_dict = {
        "title": "为首页菜谱标题添加闪动广告牌效果",
        "task_actions": [
            {
                "action": "modify_file",
                "path": "path/to/your/file1",
                "detailed_requirement": "在首页的菜谱标题上增加闪动效果，添加包含标题的div，并为其添加类名'sticker'。",
                "references": []
            },
            {
                "action": "modify_file",
                "path": "style.css",
                "detailed_requirement": "在style.css文件中添加CSS动画，定义类名'sticker'的闪动效果的动画样式。",
                "references": []
            },
            {
                "action": "modify_file",
                "path": "path/to/your/file3",
                "detailed_requirement": "添加一个CSS动画，使元素快速透明和可见，以产生闪动效果。",
                "references": []
            }
        ]
    }
    TaskStepModel(**task_step_model_dict) # type: ignore

def test_task_action_model_invalid_action_without_path():
    # action_object.path 不能为空
    with pytest.raises(ValueError, match="path cannot be empty when action is add/modify/delete"):
        TaskActionModel(
            action=ActionType.MODIFY_FILE
        )

    # action_object.path 不能是'/'开头
    with pytest.raises(ValueError, match="path cannot start with '/'"):
        TaskActionModel(
            action=ActionType.ADD_FILE,
            path='/test/path',
            detailed_requirement='Test requirement',
            references=[]
        )

def test_task_action_model_invalid_command():
    # command 不能为空
    with pytest.raises(ValueError, match="command cannot be empty when action is run_command"):
        TaskActionModel(
            action=ActionType.RUN_COMMAND
        )

    with pytest.raises(ValueError, match="command cannot be empty when action is run_command"):
        TaskActionModel(
            command='',
            action=ActionType.RUN_COMMAND
        )

    TaskActionModel(
        command='echo success',
        action=ActionType.RUN_COMMAND
    )

@pytest.mark.asyncio
async def test_safe_duplicate_multiple_paths(mocker, create_workspace):
    workspace = await create_workspace

    task_model = TaskModel(
        title='Test Task',
        description='Test Description',
        task_steps=[
            TaskStepModel(
                title='Test Step',
                task_actions=[
                    TaskActionModel(
                        action=ActionType.ADD_FILE,
                        path='test/path1',
                        target='',
                        detailed_requirement='Test requirement 1',
                        references=["file://file1"]
                    ),
                    TaskActionModel(
                        action=ActionType.MODIFY_FILE,
                        path='test/path1',
                        target='',
                        detailed_requirement='Test requirement 2',
                        references=[]
                    ),
                    TaskActionModel(
                        action=ActionType.MODIFY_FILE,
                        path='test/path2',
                        target='',
                        detailed_requirement='Test requirement 2',
                        references=[]
                    ),
                    TaskActionModel(
                        action=ActionType.RUN_COMMAND,
                        command='cp a b',
                        lifetime=CommandLifetimeType.SHORT,
                    )
                ]
            ),
            TaskStepModel(
                task_actions=[
                    TaskActionModel(
                        action=ActionType.MODIFY_FILE,
                        path='test/path1',
                        target='',
                        command='',
                        detailed_requirement='Test requirement 3',
                        references=["file://file3"]
                    ),
                    TaskActionModel(
                        action=ActionType.RUN_COMMAND,
                        command='npm install',
                        lifetime=CommandLifetimeType.LONG,
                    ),
                ]
            )
        ]
    )

    # file `test/path1`, `test/path2` does not exist, will change `modify`` to `add`
    mocker.patch.object(workspace.tools, 'read_file', side_effect=IDEServerFileNotFoundError('File not found'))
    new_task = await task_model.safe_duplicate(workspace.tools.read_file, workspace.logger.warning)
    assert len(new_task.task_steps) == 2
    assert len(new_task.task_steps[0].task_actions) == 2
    assert len(new_task.task_steps[1].task_actions) == 2
    assert new_task.task_steps[0].task_actions[0].path == 'test/path2'
    assert new_task.task_steps[0].task_actions[0].action == ActionType.ADD_FILE
    assert new_task.task_steps[1].task_actions[0].references == ["file://file1", "file://file3"]

    # file `test/path1`, `test/path2` already exists, will change `add` to `modify`
    mocker.patch.object(workspace.tools, 'read_file', return_value='file content')
    new_task = await task_model.safe_duplicate(workspace.tools.read_file, workspace.logger.warning)
    assert new_task.task_steps[0].task_actions[0].action == ActionType.MODIFY_FILE
    assert new_task.task_steps[1].task_actions[0].action == ActionType.MODIFY_FILE
    assert new_task.task_steps[1].title == 'Test Task'

@pytest.mark.asyncio
async def test_safe_duplicate_persistent_command(mocker, create_workspace):
    workspace = await create_workspace

    task_model = TaskModel(
        title='Test Task',
        description='Test Description',
        task_steps=[
            TaskStepModel(
                title='Test Step',
                task_actions=[
                    TaskActionModel(
                        action=ActionType.DELETE_FILE,
                        path='test/path',
                        target='',
                        command='',
                        lifetime=CommandLifetimeType.NA,
                        detailed_requirement='',
                        references=[]
                    ),
                    TaskActionModel(
                        action=ActionType.RUN_COMMAND,
                        path='',
                        target='',
                        command='persistent_command',
                        lifetime=CommandLifetimeType.PERSISTENT,
                        detailed_requirement='',
                        references=[]
                    )
                ]
            )
        ]
    )

    mocker.patch.object(workspace.tools, 'read_file', side_effect=IDEServerFileNotFoundError('File not found'))
    # 都是无效 action，清除后 steps 为空，应该返回 None
    task_model = await task_model.safe_duplicate(workspace.tools.read_file, workspace.logger.warning)
    assert not task_model

@pytest.mark.asyncio
async def test_safe_duplicate_empty_title(mocker, create_workspace):
    workspace = await create_workspace

    task_model = TaskModel(
        title='Test Task',
        description='Test Description',
        task_steps=[
            TaskStepModel(
                title='Test Step',
                task_actions=[
                    TaskActionModel(
                        action=ActionType.ADD_FILE,
                        path='test/path',
                        target='',
                        command='',
                        lifetime=CommandLifetimeType.NA,
                        detailed_requirement='Test requirement',
                        references=[]
                    )
                ]
            )
        ]
    )

    mocker.patch.object(workspace.tools, 'read_file', side_effect=IDEServerFileNotFoundError('File not found'))
    new_task = await task_model.safe_duplicate(workspace.tools.read_file, workspace.logger.warning)
    assert new_task.task_steps[0].task_actions[0].path == 'test/path'
