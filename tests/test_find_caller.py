import pytest
from heracles.core.utils.find_caller import find_caller

class TestA:
    def direct_call(self):
        b = TestB()
        return b.aask()
    def inrect_call(self):
        b = TestB()
        return b.aask2()

class TestB:
    def aask(self):
        caller_name = find_caller()
        return caller_name

    def aask2(self):
        return self.aask()

class TestCBase:
    def aask(self):
        caller_name = find_caller()
        return caller_name

    def aask2(self):
        return self.aask()

class TestC(TestCBase):
    def direct_call(self):
        return self.aask()
    def inrect_call(self):
        return self.aask2()

@pytest.mark.asyncio
async def test_find_caller():
    a = TestA()
    caller = a.direct_call()
    assert caller == 'direct_call'
    caller = a.inrect_call()
    assert caller == 'inrect_call'

    c = TestC()
    assert c.direct_call() == 'direct_call'
    assert c.inrect_call() == 'inrect_call'
