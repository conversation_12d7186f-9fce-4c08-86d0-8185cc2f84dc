import litellm
import pytest
from unittest.mock import AsyncMock
from openai_function_calling import FunctionInferrer
from litellm.utils import token_counter

from heracles.core.exceptions import AgentLLMCallParamsException
from heracles.agent_controller.llm import LLM
from heracles.core.schema import LLMAbilityType
from heracles.core.utils.trim_messages import trim_messages, del_unsupported_key_in_messages


@pytest.fixture(autouse=True)
def mock_llm_acomplete(mocker):
    # FIXME 这种写法目前存在问题, 没有及时返回所需要的值
    async def response_with_tools(*args, **kwargs):
        mock_response = AsyncMock()
        if 'tools' in kwargs and kwargs['tools']:
            mock_response.choices = [
                AsyncMock(
                    message=AsyncMock(content="这是一个模拟的回答"),
                    function={ "name": "get_weather",
                           "arguments": '{"location": "New York", "unit": "celsius"}'
                            })]
        else:
            mock_response.choices = [
                AsyncMock(message=AsyncMock(content="这是一个模拟的回答"))
            ]
        return mock_response
    mocker.patch('litellm.acompletion', side_effect=response_with_tools)

def run_cmd_test(cmd: str, timeout: int = 10) -> str:
        """
        func_call: 在终端执行命令并返回执行结果
        这个版本实现了将 ``cmd`` 传入终端, 执行完并返回结果的功能

        :param cmd: 命令内容
        :param timeout: 默认为10, 等待命令超时时间

        :return: 是全部命令执行结果内容

        注: 暂时不能区分命令执行是否成功, 需要用 AI 去判断.
        当使用 top 等无法返回的命令时, 结果会超时.
        该函数会尝试先发出一个ctrl+c去检查或清理持续运行的命令, 再真正执行命令
        """
        return "hello world"

def test_llm_config(monkeypatch):
    monkeypatch.setenv('LLM_MODEL', 'strong')

    monkeypatch.setenv('LLM_NORMAL_MODEL_OPTIONAL', 'normal')
    monkeypatch.setenv('LLM_NORMAL_API_KEY_OPTIONAL', 'normal_api')

    llm = LLM()
    config = llm._get_llm_config()
    assert config['model'] == 'normal'

    llm = LLM(LLMAbilityType.NORMAL)
    config = llm._get_llm_config()
    assert config['model'] == 'normal'

    llm = LLM(LLMAbilityType.STRONG)
    config = llm._get_llm_config()
    assert config['model'] == 'strong'

@pytest.mark.asyncio
async def test_llm_async_send_1():
    llm = LLM()
    await llm.async_send(user_message='hello world')
    await llm.async_send(user_message='hello world', system_message='you are a cooker')
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get the current weather in a given location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city and state, e.g. San Francisco, CA"
                        },
                        "unit": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"]
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]

    await llm.async_send(user_message="What's the weather like in beijing", tools=tools, tool_choice='auto')
    # FIXME 修复 mock 后加回来
    # assert res[0].function.name == 'get_weather'

@pytest.mark.asyncio
async def test_chunk_message():
    res = []
    async def chunk_callback(word):
        print(word)
        res.append(word)

    llm = LLM()
    async_res = await llm.async_send(user_message='hi', chunk_callback=chunk_callback)
    assert async_res == ''.join(res)

@pytest.mark.asyncio
async def test_wrong_async_param():
    async def chunk_test(word):
        pass
    llm = LLM()
    with pytest.raises(AgentLLMCallParamsException):
        await llm.async_send(user_message='hi', origin_messages=[{ 'role': 'user', 'content': '123' }])

@pytest.mark.asyncio
async def test_tools():
    run_cmd_test_function = FunctionInferrer.infer_from_function_reference(run_cmd_test)
    print(run_cmd_test_function.to_json_schema())

@pytest.mark.asyncio
async def test_llm_call_trim_messages(mocker):
    mocker.patch(
        'heracles.agent_controller.llm.MODEL_NAME_DICT',
        {'bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0': 'us.anthropic.claude-3-7-sonnet-20250219-v1:0'},
    )
    mocker.patch.object(
        LLM,
        '_get_llm_config',
        return_value={
            'model': 'litellm_proxy/bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0',
            'api_key': 'test_key',
            'base_url': 'test_url',
        },
    )
    llm = LLM()
    mocker.patch.object(
        litellm,
        'model_cost',
        {
            'us.anthropic.claude-3-7-sonnet-20250219-v1:0': {
                'max_tokens': 8192,
                'max_input_tokens': 400,
                'max_output_tokens': 8192,
                'input_cost_per_token': 0.000003,
                'output_cost_per_token': 0.000015,
                'litellm_provider': 'bedrock_converse',
                'mode': 'chat',
                'supports_function_calling': True,
                'supports_vision': True,
                'supports_assistant_prefill': True,
                'supports_prompt_caching': True,
                'supports_response_schema': True,
                'supports_pdf_input': True,
                'supports_tool_choice': True,
                'supports_reasoning': True,
            }
        },
    )
    user_message = 'apple ' * 1000  # 共计1008Token 按规则会削减至max_token(400)*trim_ratio(0.75) 差不多300token(实际297)
    mock_acompletion = mocker.patch('litellm.acompletion', wraps=litellm.acompletion)
    await llm.async_send(user_message=user_message)
    for call in mock_acompletion.call_args_list:
        assert token_counter(model=llm.model_real_name, messages=call.kwargs['messages']) < 400


@pytest.mark.parametrize(
    'raw_messages',
    [
        [
            1000,
            {
                'role': 'system',
                'content': '## Objective\nYour goal is to analyze the project information.' + 'apple ' * 400,
            },
            {
                'role': 'user',
                'content': 'Given repo structure, You need to describe the key information of the project.\n' + 'apple ' * 400,
            },
            {
                'role': 'assistant',
                'content': '',
                'tool_calls': [
                    {
                        'index': 0,
                        'function': {'arguments': '{"path": "README.md"}', 'name': 'read_file'},
                        'id': 'tooluse_4G1zXjS7RuK0KJ1TTkDzmw',
                        'type': 'function',
                    }
                ],
            },
            {
                'role': 'tool',
                'tool_call_id': 'tooluse_4G1zXjS7RuK0KJ1TTkDzmw',
                'name': 'read_file',
                'content': '# Getting Started with Create React App\n' + 'apple ' * 400,
            },
        ],
        [
            1000,
            {
                'role': 'system',
                'content': '## Objective\nYour goal is to analyze the project information.\n\n' + 'apple ' * 400,
            },
            {
                'role': 'user',
                'content': 'Given repo structure, You need to describe the key information of the project.',
            },
            {
                'role': 'assistant',
                'content': '',
                'tool_calls': [
                    {
                        'index': 0,
                        'function': {'arguments': '{"path": "README.md"}', 'name': 'read_file'},
                        'id': 'tooluse_7ytxL1bPST2Pya4-tqkP5Q',
                        'type': 'function',
                    }
                ],
            },
            {
                'role': 'tool',
                'tool_call_id': 'tooluse_7ytxL1bPST2Pya4-tqkP5Q',
                'name': 'read_file',
                'content': '# Getting Started with Create React App' + 'apple ' * 400,
            },
            {
                'role': 'assistant',
                'content': '',
                'tool_calls': [
                    {
                        'index': 0,
                        'function': {'arguments': '{"path": "package.json"}', 'name': 'read_file'},
                        'id': 'tooluse_WlhwamM9TeK_7vjvRork5w',
                        'type': 'function',
                    }
                ],
            },
            {
                'role': 'tool',
                'tool_call_id': 'tooluse_WlhwamM9TeK_7vjvRork5w',
                'name': 'read_file',
                'content': '{\n  "name": "react-todolist",\n  "version": "0.1.0"' + 'apple ' * 400 + 'apple ' * 400,
            },
        ],
        [
            1000,
            {
                'role': 'system',
                'content': 'You are a helpful assistant that can analyze images and provide detailed descriptions.',
            },
            {
                'role': 'user',
                'content': [
                    {'type': 'text', 'text': 'Whats in this image?' + 'apple ' * 400},
                    {
                        'type': 'image_url',
                        'image_url': {'url': 'data:image/jpeg;base64,/9j/4AAQSkZJRg=='},
                    },
                ],
            },
            {
                'role': 'assistant',
                'content': 'This appears to be a screenshot of a code editor showing a React application. I can see the main App component with some basic structure.',  # noqa
            },
            {
                'role': 'user',
                'content': 'Can you explain the code structure in more detail?' + 'apple ' * 400,
            }
        ],
        [
            200000,
            {
                'role': 'system',
                'content': ('You are a helpful assistant that can analyze images and provide detailed descriptions.' 'apple ' * 20000),
            },
            {
                'role': 'user',
                'content': 'Can you explain the code structure in more detail?',
            },
            {
                'role': 'assistant',
                'content': '',
                'tool_calls': [
                    {
                        'index': 0,
                        'function': {'arguments': '{"path": "README.md"}', 'name': 'read_file'},
                        'id': 'tooluse_4G1zXjS7RuK0KJ1TTkDzmw',
                        'type': 'function',
                    }
                ],
            },
            {
                'role': 'tool',
                'tool_call_id': 'tooluse_4G1zXjS7RuK0KJ1TTkDzmw',
                'name': 'read_file',
                'content': 'apple ' * 50000,
            },
            {
                'role': 'assistant',
                'content': 'The code shows a React application with a functional component structure. It includes imports from React, useState hook usage, and a basic component layout with some styling.',  # noqa
            },
            {
                'role': 'user',
                'content': ('Can you explain the code structure in more detail?' 'apple ' * 140000),
            },
        ],
        [
            10,
            {
                "role": "system",
                "content": "1",
            },
            {
                "role": "user",
                "content": ""
            }
        ],
        [
            100,
        ],
        [
            1000,
            {
                "role": "system",
                "content": "1",
            },
            {
                "role": "user",
                "content": ""
            }
        ],
        [
            1000,
            {
                "role": "system",
                "content": "apple " * 1500,
            },
            {
                "role": "user",
                "content": "What is the capital of France?",
            }
        ],
        [
            1000,
            {
                "role": "system",
                "content": "apple " * 500,
            },
            {
                "role": "system",
                "content": "apple " * 500,
            },
            {
                "role": "system",
                "content": "apple " * 500,
            },
            {
                "role": "user",
                "content": "What is the capital of France?",
            }
        ],
        [
            1000,
            {
                "role": "system",
                "content": "apple " * 500,
            },
        ],
[
            1000,
            {
                "role": "system",
                "content": "apple " * 500,
            },
            {
                "role": "system",
                "content": "apple " * 500,
            },
            {
                "role": "system",
                "content": "apple " * 500,
                "cache_control": {'type': 'ephemeral'}
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "123", "cache_control": {'type': 'ephemeral'}}
                ],
            }
        ],
    ],
)
@pytest.mark.asyncio
async def test_llm_call_trim_messages_with_tools(mocker, raw_messages):
    mocker.patch(
        'heracles.agent_controller.llm.MODEL_NAME_DICT',
        {'bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0': 'us.anthropic.claude-3-7-sonnet-20250219-v1:0'},
    )
    mocker.patch.object(
        LLM,
        '_get_llm_config',
        return_value={
            'model': 'litellm_proxy/bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0',
            'api_key': 'test_key',
            'base_url': 'test_url',
        },
    )
    max_tokens = raw_messages[0]
    raw_messages = raw_messages[1:]
    llm = LLM()
    mocker.patch.object(
        litellm,
        'model_cost',
        {
            'us.anthropic.claude-3-7-sonnet-20250219-v1:0': {
                'max_tokens': 8192,
                'max_input_tokens': max_tokens,
                'max_output_tokens': 8192,
                'input_cost_per_token': 0.000003,
                'output_cost_per_token': 0.000015,
                'litellm_provider': 'bedrock_converse',
                'mode': 'chat',
                'supports_function_calling': True,
                'supports_vision': True,
                'supports_assistant_prefill': True,
                'supports_prompt_caching': True,
                'supports_response_schema': True,
                'supports_pdf_input': True,
                'supports_tool_choice': True,
                'supports_reasoning': True,
            }
        },
    )
    messages = trim_messages(raw_messages, model=llm.model_real_name)
    assert token_counter(model=llm.model_real_name, messages=del_unsupported_key_in_messages(messages)) < max_tokens + 10 # noqa

    assert not messages or messages[0]['role'] == 'system'
    non_system_messages = [m for m in messages if m.get('role') != 'system']
    assert not non_system_messages or non_system_messages[0]['role'] == 'user'

    if len(messages) > 1:
        for i in range(1, len(messages)):
            if messages[i].get('role') == 'assistant' and messages[i].get('tool_calls'):
                tool_calls = messages[i]['tool_calls']
                for j in range(len(tool_calls)):
                    if i + j + 1 < len(messages):
                        assert messages[i + j + 1]['role'] == 'tool'
                        assert messages[i + j + 1]['tool_call_id'] == tool_calls[j]['id']
                        assert messages[i + j + 1]['name'] == tool_calls[j]['function']['name']
