import pytest
from heracles.agent_workspace.system_playbook_loader import SystemPlaybookLoader, clone_system_playbook
from heracles.agent_workspace.playbook import Playbook

@pytest.fixture
def mock_playbook_file(tmp_path):
    """创建一个模拟的 playbook 文件"""
    playbook_content = """---
title: Test Playbook
description: This is a test playbook
tags: [test, example]
applicable_rules: [rule1, rule2]
---
This is the content of the playbook.
"""
    playbook_file = tmp_path / "test_playbook.md"
    playbook_file.write_text(playbook_content)
    return playbook_file

@pytest.fixture
def mock_invalid_playbook_file(tmp_path):
    """创建一个无效的 playbook 文件"""
    invalid_content = """---
title: Invalid Playbook
---
Missing content section
"""
    invalid_file = tmp_path / "invalid_playbook.md"
    invalid_file.write_text(invalid_content)
    return invalid_file

def test_extract_field():
    """测试从元数据中提取字段"""
    metadata = """
title: Test Title
description: Test Description
    """
    assert SystemPlaybookLoader.extract_field(metadata, 'title') == 'Test Title'
    assert SystemPlaybookLoader.extract_field(metadata, 'description') == 'Test Description'
    assert SystemPlaybookLoader.extract_field(metadata, 'nonexistent') is None

def test_extract_list_field():
    """测试从元数据中提取列表字段"""
    metadata = """
tags: [tag1, tag2, tag3]
rules: [rule1]
    """
    assert SystemPlaybookLoader.extract_list_field(metadata, 'tags') == ['tag1', 'tag2', 'tag3']
    assert SystemPlaybookLoader.extract_list_field(metadata, 'rules') == ['rule1']
    assert SystemPlaybookLoader.extract_list_field(metadata, 'nonexistent') == []

def test_parse_playbook_file(mock_playbook_file):
    """测试解析有效的 playbook 文件"""
    playbook = SystemPlaybookLoader.parse_playbook_file(str(mock_playbook_file))
    assert isinstance(playbook, Playbook)
    assert playbook.title == 'Test Playbook'
    assert playbook.description == 'This is a test playbook'
    assert playbook.tags == ['test', 'example']
    assert playbook.applicable_rules == ['rule1', 'rule2']
    assert playbook.original_content == 'This is the content of the playbook.'
    assert playbook.source == 'system'

def test_parse_invalid_playbook_file(mock_invalid_playbook_file):
    """测试解析无效的 playbook 文件"""
    playbook = SystemPlaybookLoader.parse_playbook_file(str(mock_invalid_playbook_file))
    assert playbook is None

def test_parse_nonexistent_file():
    """测试解析不存在的文件"""
    playbook = SystemPlaybookLoader.parse_playbook_file('nonexistent.md')
    assert playbook is None

def test_clone_system_playbook(mock_playbook_file, monkeypatch):
    """测试克隆系统 playbook"""
    # 模拟系统 playbook
    original_playbook = Playbook(
        title='Test Playbook',
        description='Test Description',
        tags=['test'],
        applicable_rules=['rule1'],
        original_content='Test content',
        source='system'
    )

    # 替换系统 playbook 列表
    monkeypatch.setattr(SystemPlaybookLoader, 'system_playbook', [original_playbook])

    # 克隆并验证
    cloned_playbooks = clone_system_playbook()
    assert len(cloned_playbooks) == 1
    assert cloned_playbooks[0].title == original_playbook.title
    assert cloned_playbooks[0].description == original_playbook.description
    assert cloned_playbooks[0].tags == original_playbook.tags
    assert cloned_playbooks[0].applicable_rules == original_playbook.applicable_rules
    assert cloned_playbooks[0].original_content == original_playbook.original_content
    assert cloned_playbooks[0].source == original_playbook.source

    # 验证克隆是深拷贝
    cloned_playbooks[0].title = 'Modified Title'
    assert original_playbook.title == 'Test Playbook'
