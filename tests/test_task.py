import pytest
import json
from typing import cast

from heracles.core.utils.redis_cache import redis_cache
from heracles.core.schema.models.task_model import TaskModel, TaskStepModel, TaskActionModel
from heracles.core.schema.task import Task, ActionType, TaskAction, TaskStep, FileActionObject, CommandActionObject, CommandLifetimeType
from heracles.core.exceptions import AgentRunException
from heracles.agent_roles.plan_role.create_test_task import create_test_task


@pytest.fixture
def default_task():
    task: Task = create_test_task()
    yield task

def test_add_step_action_to_task(default_task):
    task_step = TaskStep(title='添加新步骤')
    task_step_id = default_task.add_task_step(task_step)
    assert task_step_id is not None

    task_step_action = TaskAction(
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path='新路径.rb',
            target='createNewFile',
            detailed_requirement="test requirement"
        )
    )  # 添加 action
    task_step.add_task_action(task_step_action)

    task_step_action = TaskAction(
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path='新路径2.rb',
            target='createNewFile',
            detailed_requirement="test requirement"
        )
    )  # 添加 action
    task_action_id = default_task.add_task_action_by_task(task_step_id, task_step_action)
    assert task_action_id is not None

    default_task.modify_task_step(task_step_id, 'new title')
    assert task_step.title == 'new title'

    default_task.modify_task_action(task_action_id, {'action_object': {'path': 'path1'}})
    action_object = cast(FileActionObject, task_step_action.action_object)
    assert action_object.path == 'path1'

def test_task_action_abnormal(default_task):
    task = default_task
    task.dict()

    with pytest.raises(AgentRunException):
        task.find_step_by_id('noexist')

    with pytest.raises(AgentRunException):
        task.find_action_by_id('noexist')

    task_action = task.find_action_by_id('1-1')
    task_action.set_status_completed('x' * 2002)
    assert task_action.result == 'x' * 2002
    task_action.set_status_abandoned('y' * 2002)
    assert task_action.result == 'y' * 2002

    task_step = task.find_step_by_id('1')
    assert task_step.dict()['id'] == '1'
    with pytest.raises(AgentRunException):
        task_step.set_id('2')

    task_step_dict = {
        'title': 'title1',
        'task_actions': [{
            'action': 'add_file',
            'action_object': {
                'path': 'xxx',
                'detailed_requirement': 'test requirement'
            }
        }]
    }

    task_action_dict = task_step_dict['task_actions'][0]
    with pytest.raises(AgentRunException, match='Unable to find the task step'):
        task.add_task_action_from_dict('noexist', task_action_dict)

    with pytest.raises(AgentRunException, match='The specified task_action_id could not be found'):
        task.find_step_by_action_id('noexist')

def test_task_validate_title():
    # 验证 task.title 为空
    with pytest.raises(ValueError):
        Task()

def test_task_action_invalid_detailed_requirement():
    # 验证 action_object.detailed_requirement 不能超过5000字符
    with pytest.raises(ValueError, match="detailed_requirement cannot exceed 5000 characters"):
        TaskAction(
            action=ActionType.MODIFY_FILE,
            action_object=FileActionObject(
                path="controller.rb",
                target="writeControllerClass",
                detailed_requirement="x" * 5001
            )
        )

def test_task_action_invalid_references():
    # 验证 action_object.references 每个元素不能超过1000字符
    with pytest.raises(ValueError, match="each reference item cannot exceed 1000 characters"):
        TaskAction(
            action=ActionType.MODIFY_FILE,
            action_object=FileActionObject(
                path="controller.rb",
                target="writeControllerClass",
                references=["x" * 1001]
            )
        )

def test_task_action_invalid_action_object():
    # 验证 action 指定 action 类型下 detailed_requirement 为空
    with pytest.raises(ValueError, match="detailed_requirement must be set for file actions"):
        TaskAction(
            action=ActionType.MODIFY_FILE,
            action_object=FileActionObject(
                path="controller.rb",
                target="writeControllerClass"
            )
        )

    # 验证 action 指定 action 类型下 action_object 为正确类型
    with pytest.raises(ValueError, match="action_object should be FileActionObject"):
        TaskAction(
            action=ActionType.MODIFY_FILE,
            action_object=CommandActionObject(
                command='cp .env.example .env',
                lifetime=CommandLifetimeType.SHORT
            )
        )
    with pytest.raises(ValueError, match="action_object should be CommandActionObject"):
        TaskAction(
            action=ActionType.RUN_COMMAND,
            action_object=FileActionObject(
                path="controller.rb",
                target="writeControllerClass"
            )
        )

def test_task_action_invalid_path():
    # 验证 action_object.path 不能是'/'开头
    with pytest.raises(ValueError, match="path cannot start with '/'"):
        TaskAction(
            action=ActionType.MODIFY_FILE,
            action_object=FileActionObject(
                path="/app/controller.rb",
                target="writeControllerClass"
            )
        )

def test_task_serialization():
    # 验证 step.task_actions 不能重复修改同一 path
    task = Task(
        title='Test Task',
        description='Test Description',
        task_steps=[
            TaskStep(
                title='Test Step',
                task_actions=[
                    TaskAction(
                        action=ActionType.ADD_FILE,
                        action_object=FileActionObject(
                            path='test/path1',
                            detailed_requirement='Test requirement 1'
                        )
                    )
                ]
            )
        ]
    )

    new_file_action = TaskAction(
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path='test/path1',
            detailed_requirement='Test requirement 1'
        )
    )
    with pytest.raises(ValueError):
        task.task_steps[0].add_task_action(new_file_action)

    new_command_action = TaskAction(
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command='bundle install',
            lifetime=CommandLifetimeType.SHORT
        )
    )
    task.task_steps[0].add_task_action(new_command_action)
    # 验证输出序列化按照指定格式
    serialized_task = task.dict()
    assert 'detailed_requirement' not in serialized_task['task_steps'][0]['task_actions'][0]
    assert 'lifetime' not in serialized_task['task_steps'][0]['task_actions'][1]


def test_task_delete_logic(default_task):
    task = default_task

    task.find_action_by_id('1-1')
    task.delete_task_action('1-1')
    with pytest.raises(AgentRunException):
        task.find_action_by_id('1-1')

    task.find_action_by_id('1-2')
    task.delete_task_step('1')
    with pytest.raises(AgentRunException):
        task.find_step_by_id('1')
    task.find_step_by_id('2')


def test_task_creation_from_task_model():
    task_model = TaskModel(
        title='Test Task',
        description='Test Description',
        task_steps=[
            TaskStepModel(
                title='Test Step',
                task_actions=[
                    TaskActionModel(
                        action=ActionType.ADD_FILE,
                        path='test/path1',
                        target='',
                        detailed_requirement='Test requirement 1',
                        references=[]
                    ),
                    TaskActionModel(
                        action=ActionType.MODIFY_FILE,
                        path='test/path2',
                        target='',
                        detailed_requirement='Test requirement 2',
                        references=[]
                    ),
                    TaskActionModel(
                        action=ActionType.RUN_COMMAND,
                        command='ps aux',
                        lifetime=CommandLifetimeType.SHORT
                    )
                ]
            )
        ]
    )

    task = Task(**task_model.to_dict())
    assert len(task.task_steps) == 1
    assert len(task.task_steps[0].task_actions) == 3

def test_task_cache(default_task):
    task: Task = default_task
    playground_id = 'playground-1'
    task.enable_autosave(playground_id)
    new_task = task.load_from_redis(playground_id)

    assert new_task == default_task

def test_task_cache_dup_autosave(default_task):
    task: Task = default_task
    playground_id = 'playground-1'
    task.enable_autosave(playground_id)
    # 一样的id没问题
    task.enable_autosave(playground_id)
    # 不一样的报错
    with pytest.raises(AgentRunException, match='Enable autosave failed'):
        task.enable_autosave('playground-2')

def test_task_cache_playground_not_exist(default_task):
    task: Task = default_task
    playground_id = 'playground-not-exist'

    assert Task.load_from_redis(playground_id) is None

    with pytest.raises(AgentRunException, match='Cannot generate redis key'):
        task._generate_redis_key()

    with pytest.raises(AgentRunException, match='Cannot save task'):
        task.save()

def test_task_cache_add_task_action(default_task):
    task: Task = default_task
    playground_id = 'playground-1'
    task.enable_autosave(playground_id)

    task_step_action = TaskAction(
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path='新路径.rb',
            target='createNewFile',
            detailed_requirement="test requirement"
        )
    )  # 添加 action
    task.task_steps[0].add_task_action(task_step_action)

    key = task_step_action._generate_redis_key()
    assert redis_cache.get(key) is not None

def test_task_cache_delete_task_action(default_task):
    task: Task = default_task
    playground_id = 'playground-1'
    task.enable_autosave(playground_id)

    task_action = task.find_action_by_id('1-1')

    key = task_action._generate_redis_key()
    assert redis_cache.get(key) is not None


    task.delete_task_action('1-1')
    assert redis_cache.get(key) is None

def test_task_cache_delete_task_step(default_task):
    task: Task = default_task
    playground_id = 'playground-1'
    task.enable_autosave(playground_id)

    task_step = task.find_step_by_id('1')

    key = task_step._generate_redis_key()
    assert redis_cache.get(key) is not None

    task.delete_task_step('1')
    assert redis_cache.get(key) is None

def test_task_cache_modify_task_action(default_task):
    task: Task = default_task
    playground_id = 'playground-1'
    task.enable_autosave(playground_id)

    task_action = default_task.find_action_by_id('1-1')

    task.modify_task_action('1-1', {'action_object': {'path': 'test-1'}})

    key = task_action._generate_redis_key()
    assert json.loads(redis_cache.get(key))['action_object']['path'] == 'test-1'

def test_task_modify_file_action(default_task):
    task: Task = default_task

    task.modify_task_action('1-1', {
        'action_object': {
            'path': 'new-path',
            'detailed_requirement': 'test requirement',
            'references': ['test']
        }
    })
    task_action = default_task.find_action_by_id('1-1')
    assert task_action.action_object.path == 'new-path'
    assert task_action.action_object.detailed_requirement == 'test requirement'
    assert task_action.action_object.references == ['test']

    with pytest.raises(AgentRunException, match='No task action fields to update'):
        # 没有匹配要求更新的字段：file 类型没有 command 或 lifetime 字段可供更新
        task.modify_task_action('1-1', {
            'action_object': {
                'command': 'new-command',
                'lifetime': 'long'
            }
        })

def test_task_add_and_modify_command_action(default_task):
    task: Task = default_task

    new_command_action = TaskAction(
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command='bundle install',
            lifetime=CommandLifetimeType.SHORT
        )
    )
    task_action_id = task.add_task_action_by_task('1', new_command_action)
    task.modify_task_action(task_action_id, {'action_object': {'command': 'ls', 'lifetime': 'long'}})
    assert isinstance(new_command_action.action_object, CommandActionObject)
    assert new_command_action.action_object.command == 'ls'
    assert new_command_action.action_object.lifetime == CommandLifetimeType.LONG

def test_task_cache_delete_task(default_task):
    task: Task = default_task
    playground_id = 'playground-1'

    with pytest.raises(AgentRunException):
        task.delete()

    task.enable_autosave(playground_id)

    task_action = default_task.find_action_by_id('1-1')
    key = task_action._generate_redis_key()
    assert json.loads(redis_cache.get(key))['action_object'] is not None

    task.delete()
    assert redis_cache.get(key) is None

def test_task_cache_load_from_redis(default_task):
    task: Task = default_task
    playground_id = 'playground-test-cache-1'
    task.enable_autosave(playground_id)

    new_task = Task.load_from_redis(playground_id)
    assert task == new_task

def test_pretty_print(default_task):
    task: Task = default_task
    task.pretty_print()
