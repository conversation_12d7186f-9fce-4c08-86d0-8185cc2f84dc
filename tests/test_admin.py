from typing import Any
import pytest
from fastapi.testclient import TestClient
from pydantic import BaseModel
import litellm
from fastembed import SparseTextEmbedding, SparseEmbedding


class Record(BaseModel):
    id: str
    payload: Any


class Point(BaseModel):
    id: str
    version: int
    score: float
    payload: Any


class PointResponse(BaseModel):
    points: list[Point]


class MockAsyncQdrantClient:
    def __init__(self, *args, **kwargs):
        self.record = Record(
            id='9bbf91ea-043b-44d9-a043-fa5a615cb110',
            payload={
                'title': 'title',
                'description': '',
                'tags': ['initialization'],
                'original_content': 'original_content',
                'source': 'user',
                'status': 'ready',
            },
        )

    async def collection_exists(self, *args, **kwargs):
        return False

    async def recreate_collection(self, *args, **kwargs):
        return False

    async def get_collections(self, *args, **kwargs):
        return {
            'status': 'ok',
            'statusCode': 200,
            'error_msg': '',
            'data': {'collections': [{'name': 'test'}]},
        }

    async def scroll(self, *args, **kwargs):
        next_page_offset = 10
        return ([self.record], next_page_offset)

    async def upsert(self, *args, **kwargs):
        return True

    async def delete(self, *args, **kwargs):
        return True

    async def count(self, *args, **kwargs):
        return 10

    async def query_points(self, *args, **kwargs):
        playbook_dict = {
            'title': 'title',
            'description': '',
            'tags': ['initialization'],
            'original_content': 'original_content',
            'source': 'user',
            'status': 'ready',
        }
        point = {
            'id': '9bbf91ea-043b-44d9-a043-fa5a615cb110',
            'version': 3,
            'score': 0.75,
            'payload': playbook_dict,
        }
        point_res = PointResponse(points=[Point.model_validate(point)])
        return point_res

    async def retrieve(self, *args, **kwargs):
        return [self.record]


def init_mock_client(mocker):
    mock_res = litellm.EmbeddingResponse.model_validate({'model': 'test', 'data': [{'embedding': [0, 0, 1]}]})
    mocker.patch.object(litellm, 'embedding', return_value=mock_res)

    def mock_init(self, *args, **kwargs):
        pass

    mocker.patch.object(SparseTextEmbedding, '__init__', mock_init)
    mocker.patch.object(SparseTextEmbedding, 'embed', return_value=iter([SparseEmbedding([1], [1])] * 20))

    mock_vector_db_client = MockAsyncQdrantClient()
    mocker.patch('heracles.server.admin.admin.vector_db_client', mock_vector_db_client)

    from heracles.server.admin.admin import app

    return TestClient(app)


@pytest.mark.asyncio
async def test_load_model_and_db(mocker):
    init_mock_client(mocker)
    from heracles.server.admin.admin import load_model_and_db

    await load_model_and_db()


def test_fetch_collections(mocker):
    client = init_mock_client(mocker)

    response = client.get('/collections')
    assert response.status_code == 200


def test_fetch_playbooks(mocker):
    client = init_mock_client(mocker)

    response = client.get('/playbooks', params={'page_count': 10, 'page': 1})
    assert response.status_code == 200


def test_create_playbook(mocker):
    client = init_mock_client(mocker)
    playbook_dict = {
        'id': '9bbf91ea-043b-44d9-a043-fa5a615cb110',
        'title': 'title',
        'description': '',
        'tags': ['initialization'],
        'original_content': 'original_content',
        'source': 'user',
        'status': 'ready',
    }
    response = client.post('/playbooks', json=playbook_dict)
    assert response.status_code == 200


def test_delete_playbook(mocker):
    client = init_mock_client(mocker)
    response = client.delete('/playbooks/9bbf91ea-043b-44d9-a043-fa5a615cb110')
    assert response.status_code == 200


def test_get_collection_count(mocker):
    client = init_mock_client(mocker)
    response = client.get('/playbooks/count')
    assert response.status_code == 200


def test_get_playbook(mocker):
    client = init_mock_client(mocker)
    response = client.get('/playbooks/9bbf91ea-043b-44d9-a043-fa5a615cb110')
    assert response.status_code == 200


def test_query_playbook(mocker):
    client = init_mock_client(mocker)
    response = client.post('/playbooks/query', json={'text': 'test'})
    assert response.status_code == 200
