import pytest

from heracles.agent_workspace.auto_fix import AutoFix


class TestAutoFix:
    """AutoFix类的测试用例"""

    @pytest.mark.asyncio
    async def test_init(self, create_workspace):
        """测试AutoFix初始化"""
        workspace = await create_workspace
        auto_fix = AutoFix(workspace)

        assert auto_fix.enable

        await auto_fix.set_auto_fix_enable(False)
        assert not auto_fix.enable

    @pytest.mark.asyncio
    async def test_trigger_methods(self, create_workspace):
        """测试所有触发器方法"""
        workspace = await create_workspace
        auto_fix = AutoFix(workspace)

        await auto_fix.trigger_auto_fix_start('start message')
        await auto_fix.trigger_auto_fix_stopped('stopped message')
        await auto_fix.trigger_auto_fix_completed('completed message')
        await workspace.trigger_project_errors_message("some error")
        await workspace.trigger(
            'add_task_step_confirmation',
            {'task_step_dict': {'step_id': 'step-123', 'description': 'Fix linting errors', 'status': 'pending'},
             'auto_merge': False}
        )

    @pytest.mark.asyncio
    async def test_update_status_and_event(self, create_workspace):
        workspace = await create_workspace
        auto_fix = AutoFix(workspace)

        # turn 0 - 初始状态已经是'init'，所以这个调用会直接返回
        assert auto_fix.status == 'init'  # 验证初始状态
        await auto_fix.update_status_and_event('init')  # 相同状态，直接返回
        assert auto_fix.status == 'init'  # 状态不变

        await auto_fix.update_status_and_event('start')
        assert auto_fix.status == 'start'

        await auto_fix.update_status_and_event('check_errors')  # type: ignore[unreachable]
        assert auto_fix.status == 'check_errors'

        await auto_fix.update_status_and_event('fix_errors', {})
        assert auto_fix.status == 'fix_errors'

        await auto_fix.update_status_and_event('fix_completed')
        assert auto_fix.status == 'fix_completed'

        auto_fix.status = 'init'
        await auto_fix.update_status_and_event('start')
        assert auto_fix.status == 'start'

        await auto_fix.update_status_and_event('check_errors')
        assert auto_fix.status == 'check_errors'

        await auto_fix.update_status_and_event('fix_stopped')
        assert auto_fix.status == 'fix_stopped'

        # turn 1
        await auto_fix.update_status_and_event('init')
        auto_fix.fix_turn = 1
        await auto_fix.update_status_and_event('start')
        assert auto_fix.status == 'start'

        await auto_fix.update_status_and_event('check_errors')
        assert auto_fix.status == 'check_errors'

        await auto_fix.update_status_and_event('fix_errors', {})
        assert auto_fix.status == 'fix_errors'

        await auto_fix.update_status_and_event('fix_completed')
        assert auto_fix.status == 'fix_completed'

        # stopped
        await auto_fix.update_status_and_event('init')
        await auto_fix.update_status_and_event('start')
        assert auto_fix.status == 'start'

        await auto_fix.update_status_and_event('check_errors')
        assert auto_fix.status == 'check_errors'

        await auto_fix.update_status_and_event('fix_errors', {})
        assert auto_fix.status == 'fix_errors'

        await auto_fix.update_status_and_event('fix_stopped')
        assert auto_fix.status == 'fix_stopped'
