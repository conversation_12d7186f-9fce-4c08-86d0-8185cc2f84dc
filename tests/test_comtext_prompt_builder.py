import pytest
from heracles.core.utils.context_prompt_builder import Pro<PERSON><PERSON><PERSON><PERSON>
from heracles.core.schema.task import TaskAction, TaskStep, ActionType, ActionStatus, FileActionObject, Task

@pytest.mark.asyncio
async def test_recent_file_changes_cache_and_diff(create_workspace, mocker):
    # 获取 mock workspace
    workspace = await create_workspace

    # mock logger，便于捕获 warning
    workspace.logger = mocker.Mock()

    # 构造两个文件的 action，分别有不同的 snapshot 和当前内容
    file1 = "foo.py"
    file2 = "bar.py"
    snapshot1 = "a = 1\nb = 2"
    current1 = "a = 1\nb = 3"
    snapshot2 = "x = 1"
    current2 = "x = 1\ny = 2"

    # mock tools
    workspace.tools.query_snapshot_file_by_uuid = mocker.AsyncMock(side_effect=lambda file_path, snapshot_uuid: {
        file1: snapshot1, file2: snapshot2
    }[file_path])
    workspace.tools.read_file_content = mocker.AsyncMock(side_effect=lambda file_path, silent: {
        file1: current1, file2: current2
    }[file_path])

    # 构造 actions
    action1 = TaskAction(
        id="action1",
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(path=file1, snapshot_uuid="snap1", detailed_requirement="req"),
        status=ActionStatus.COMPLETED_STATUS
    )
    action2 = TaskAction(
        id="action2",
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(path=file2, snapshot_uuid="snap2", detailed_requirement="req"),
        status=ActionStatus.COMPLETED_STATUS
    )
    # 构造 step
    step = TaskStep(id="1", title="step1", task_actions=[action1, action2])
    # 构造 task
    workspace.task = Task(id="task1", title="t", task_steps=[step])

    # 构造 PromptBuilder
    template = "{RECENT_FILE_CHANGES}"
    builder = PromptBuilder(template, workspace)

    # 第一次调用，应该生成 diff 并缓存
    result1 = await builder.format()
    assert "foo.py" in result1 and "-b = 2" in result1 and "+b = 3" in result1
    assert "bar.py" in result1 and "+y = 2" in result1
    assert workspace.tools.query_snapshot_file_by_uuid.await_count == 2
    assert workspace.tools.read_file_content.await_count == 2

    # 第二次调用，action 没变，应该直接用缓存，不再调用 tools
    workspace.tools.query_snapshot_file_by_uuid.reset_mock()
    workspace.tools.read_file_content.reset_mock()
    result2 = await builder.format()
    assert result2 == result1
    workspace.tools.query_snapshot_file_by_uuid.assert_not_awaited()
    workspace.tools.read_file_content.assert_not_awaited()

    # 修改 action2 的 id，模拟有新变更
    action2.id = "action2_new"
    # 现在只 bar.py 需要重新 diff
    result3 = await builder.format()
    assert "foo.py" in result3 and "bar.py" in result3
    assert workspace.tools.query_snapshot_file_by_uuid.await_count == 1
    assert workspace.tools.read_file_content.await_count == 1
    workspace.tools.query_snapshot_file_by_uuid.assert_awaited_with("bar.py", "snap2")
    workspace.tools.read_file_content.assert_awaited_with("bar.py", silent=True)
