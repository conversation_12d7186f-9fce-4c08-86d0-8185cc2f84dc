import pytest
from unittest.mock import patch
from heracles.core.utils.paas_agent import paas_request
from heracles.core.exceptions import PaasAgentException


class MockResponse:
    def __init__(self, status, json_data):
        self.status = status
        self._json_data = json_data

    async def json(self):
        return self._json_data

    async def __aenter__(self):
        return self

    async def __aexit__(self, *_args):
        pass


class MockSession:
    def __init__(self, response):
        self._response = response
        self.request_calls = []

    def request(self, method, url, json=None):
        self.request_calls.append((method, url, json))
        return self._response

    async def __aenter__(self):
        return self

    async def __aexit__(self, *_args):
        pass


class TestPaasRequest:
    """Test paas_request function with full coverage"""

    @pytest.mark.asyncio
    async def test_paas_request_success(self):
        """Test successful paas_request call - covers lines 4-15"""
        mock_response_data = {
            "code": 200,
            "msg": "success",
            "data": {"result": "test_data"}
        }

        mock_response = MockResponse(200, mock_response_data)
        mock_session = MockSession(mock_response)

        with patch('heracles.core.utils.paas_agent.aiohttp.ClientSession') as mock_session_class:
            mock_session_class.return_value = mock_session

            result = await paas_request("POST", "http://test.com/api", {"key": "value"}, 30)

            # Verify the result
            assert result == {"result": "test_data"}

            # Verify ClientSession was created with correct timeout
            mock_session_class.assert_called_once()
            call_args = mock_session_class.call_args
            assert call_args[1]['timeout'].total == 30

            # Verify request was made with correct parameters
            assert len(mock_session.request_calls) == 1
            method, url, json_data = mock_session.request_calls[0]
            assert method == "POST"
            assert url == "http://test.com/api"
            assert json_data == {"key": "value"}

    @pytest.mark.asyncio
    async def test_paas_request_http_error_status(self):
        """Test paas_request with non-200 HTTP status - covers lines 9-10"""
        mock_response = MockResponse(404, {})
        mock_session = MockSession(mock_response)

        with patch('heracles.core.utils.paas_agent.aiohttp.ClientSession') as mock_session_class:
            mock_session_class.return_value = mock_session

            with pytest.raises(PaasAgentException) as exc_info:
                await paas_request("GET", "http://test.com/api")

            assert "Request error, status code: 404" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_paas_request_response_code_error(self):
        """Test paas_request with response code != 200 - covers lines 12-13"""
        mock_response_data = {
            "code": 500,
            "msg": "Internal server error",
            "data": None
        }

        mock_response = MockResponse(200, mock_response_data)
        mock_session = MockSession(mock_response)

        with patch('heracles.core.utils.paas_agent.aiohttp.ClientSession') as mock_session_class:
            mock_session_class.return_value = mock_session

            with pytest.raises(PaasAgentException) as exc_info:
                await paas_request("POST", "http://test.com/api", {"test": "data"})

            assert "Internal server error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_paas_request_no_data_field(self):
        """Test paas_request when response has no data field - covers line 15"""
        mock_response_data = {
            "code": 200,
            "msg": "success"
            # No "data" field
        }

        mock_response = MockResponse(200, mock_response_data)
        mock_session = MockSession(mock_response)

        with patch('heracles.core.utils.paas_agent.aiohttp.ClientSession') as mock_session_class:
            mock_session_class.return_value = mock_session

            result = await paas_request("GET", "http://test.com/api")

            # Should return None when data field is missing
            assert result is None

    @pytest.mark.asyncio
    async def test_paas_request_default_timeout(self):
        """Test paas_request with default timeout - covers line 5"""
        mock_response_data = {
            "code": 200,
            "msg": "success",
            "data": {"test": "result"}
        }

        mock_response = MockResponse(200, mock_response_data)
        mock_session = MockSession(mock_response)

        with patch('heracles.core.utils.paas_agent.aiohttp.ClientSession') as mock_session_class:
            mock_session_class.return_value = mock_session

            # Call without timeout parameter (should use default 10)
            result = await paas_request("GET", "http://test.com/api")

            # Verify default timeout was used
            call_args = mock_session_class.call_args
            assert call_args[1]['timeout'].total == 10

            assert result == {"test": "result"}

    @pytest.mark.asyncio
    async def test_paas_request_no_data_parameter(self):
        """Test paas_request without data parameter - covers line 8"""
        mock_response_data = {
            "code": 200,
            "msg": "success",
            "data": {"success": True}
        }

        mock_response = MockResponse(200, mock_response_data)
        mock_session = MockSession(mock_response)

        with patch('heracles.core.utils.paas_agent.aiohttp.ClientSession') as mock_session_class:
            mock_session_class.return_value = mock_session

            # Call without data parameter (should pass None)
            result = await paas_request("GET", "http://test.com/api")

            # Verify request was made with json=None
            assert len(mock_session.request_calls) == 1
            method, url, json_data = mock_session.request_calls[0]
            assert method == "GET"
            assert url == "http://test.com/api"
            assert json_data is None

            assert result == {"success": True}
