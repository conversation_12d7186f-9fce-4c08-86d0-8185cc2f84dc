import pytest
from heracles.core.utils.template_registry import Template<PERSON><PERSON><PERSON>ry, GLOBAL_PRE_PROMPT
from heracles.core.utils.prompt_builder_templates import PROJECT_KNOWLEDGE
from heracles.agent_workspace.agent_workspace import AgentWorkspace
from heracles.agent_workspace.workspace_analyze_item import WorkspaceAnalyzeItem, WorkspaceStatus
from heracles.core.schema.knowledge import ProjectKnowledge, DetailedInfo, LanguageType, FrameworkType
from jinja2 import Template
from datetime import datetime


def test_template_registry_initialization():
    """测试模板注册器的初始化"""
    registry = TemplateRegistry()
    assert registry._templates == {}
    assert registry._env.globals['isinstance'] == isinstance


@pytest.mark.asyncio
async def test_async_render():
    """测试异步渲染功能"""
    registry = TemplateRegistry()
    template_str = "Hello {{ name }}!"
    registry.register("greeting", template_str)

    result = await registry.get("greeting").render(name="World")
    assert result == "Hello World!"


@pytest.mark.asyncio
async def test_async_render_project_knowledge(mocker):
    """测试异步渲染 PROJECT_KNOWLEDGE 模板"""
    # 创建模拟的 playground
    mock_playground = mocker.MagicMock()
    mock_playground.playground_id = "test_playground"

    # 创建 workspace
    workspace = AgentWorkspace(mock_playground)

    # 创建 DetailedInfo 对象
    basic_info = DetailedInfo(
        app_dir=".",
        language=LanguageType.PYTHON,
        runtime_version="3.11",
        framework=FrameworkType.FASTAPI,
        framework_version="0.109.0",
        dependency_command="pip install -r requirements.txt",
        run_command="uvicorn app.main:app --reload",
        project_dependencies="pytest, fastapi, uvicorn",
        project_components="API endpoints, database models, authentication",
        middlewares=['redis', 'postgres']
    )

    # 设置 workspace 的 project_knowledge
    workspace.project_knowledge = ProjectKnowledge(
        basic_info_list=[basic_info],
        project_structure="这是一个基于 FastAPI 的 Web 服务项目- requirements.txt: 项目依赖",
        runtime_environment={
            "runtime": "python",
            "version": "3.11",
            "os": "linux"
        }
    )

    # 创建 analyze_item
    analyze_item = WorkspaceAnalyzeItem(
        workspace=workspace,
        name="project_structure",
        description="项目文件结构分析",
        estimated_seconds=30,
        section_header="项目结构分析",
        instruction_prompt="分析项目的主要文件结构，描述项目的用途和场景"
    )

    # 设置 analyze_item 的状态
    analyze_item.status = WorkspaceStatus.DONE
    analyze_item.started_at = datetime.now().isoformat()
    analyze_item.last_updated_at = datetime.now().isoformat()
    analyze_item.version = 1

    # 使用全局模板注册器渲染
    result = await GLOBAL_PRE_PROMPT.get("PROJECT_KNOWLEDGE").render(item=analyze_item, workspace=workspace)

    # 直接使用 Jinja2 模板渲染作为对照
    expected_result = Template(PROJECT_KNOWLEDGE).render(item=analyze_item, workspace=workspace)

    # 验证结果
    assert result == expected_result


@pytest.mark.asyncio
async def test_async_render_with_missing_template():
    """测试渲染不存在的模板时的错误处理"""
    registry = TemplateRegistry()
    with pytest.raises(ValueError) as exc_info:
        registry.get("nonexistent")
    assert "Template 'nonexistent' not found" in str(exc_info.value)


def test_global_pre_prompt_registration():
    """测试全局模板注册器中的模板注册"""
    assert "FILE_TREE" in GLOBAL_PRE_PROMPT._templates
    assert "PROJECT_KNOWLEDGE" in GLOBAL_PRE_PROMPT._templates
    assert "FILE_SNIPPETS" in GLOBAL_PRE_PROMPT._templates
    assert "PLAYBOOKS" in GLOBAL_PRE_PROMPT._templates
    assert "PLAYBOOK_LIST" in GLOBAL_PRE_PROMPT._templates
    assert "ERRORS" in GLOBAL_PRE_PROMPT._templates
    assert "TASK" in GLOBAL_PRE_PROMPT._templates
    assert "IN_PROGRESS_TASK_ACTION" in GLOBAL_PRE_PROMPT._templates
    assert "RECENT_FILE_CHANGES" in GLOBAL_PRE_PROMPT._templates
    assert "REFERENCES" in GLOBAL_PRE_PROMPT._templates
    assert "WEB_PAGES" in GLOBAL_PRE_PROMPT._templates
    assert "CLACKY_RULES" in GLOBAL_PRE_PROMPT._templates
