import pytest

from heracles.core.utils.llm_json import dumps, loads
from heracles.core.exceptions import AgentLLMJSONEx<PERSON>

def test_dumps_and_loads():
    json_dict = {
            "a": 1,
            }
    res = dumps(json_dict)
    loads(res)

def test_loads_wrong_json():
    json_str = "{xxx"
    with pytest.raises(AgentLLMJSONException):
        loads(json_str)

    json_str = "{xx:1}"
    loads(json_str)

    json_str = "{'xx':1}"
    loads(json_str)

    json_str = "\\n{'xx':1}\\n"
    loads(json_str)

    json_str = "```json\n{'xx':1}\\n```"
    loads(json_str)

    json_str = "```json\n{'xx':1}```\n```json{'yy':1}```"
    loads(json_str)

    json_str = '{ "name": "<PERSON>", "birthdate": new Date(1990, 1, 1) }'
    loads(json_str)
