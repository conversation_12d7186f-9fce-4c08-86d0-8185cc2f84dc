import os

import pytest
import pytest_asyncio
import socketio

from heracles.agent_roles.plan_role.create_test_task import create_test_task
from heracles.core.schema import PlaygroundStatusType
from heracles.core.schema.test import Context, MockSocketIOServer
from heracles.core.exceptions import IDEServerConnectException
from heracles.server.main import connect, disconnect, health_check, index, on_any, playground_manager, shutdown_event, \
    startup_event

sid = 'test-connect-sid-1'
playground_id = 'playground-test-main-1'

@pytest_asyncio.fixture
async def setup_connect(create_blank_playground, mocker):
    context = Context()

    async def _setup_connect():
        mock_server = MockSocketIOServer()
        mocker.patch('heracles.server.main.socketio_server', mock_server)

        # 模拟调试模式以跳过JWT验证
        os.environ["IDE_SERVER_DEBUG_OPTIONAL"] = "True"

        playground = await create_blank_playground(playground_id)
        playground_manager.add_playground(playground)
        context.playground = playground
        await connect(sid, {}, {'playgroundId': playground_id})

    try:
        yield _setup_connect
    finally:
        await disconnect(sid)
        if playground := context.playground:
            playground_manager.remove_playground(playground)


@pytest.mark.asyncio
async def test_connect_no_auth(setup_connect):
    await setup_connect()

    with pytest.raises(socketio.exceptions.ConnectionRefusedError, match='Auth data error'):
        await connect(sid, {}, None)

@pytest.mark.asyncio
async def test_connect_extra_param(setup_connect):
    await setup_connect()
    await connect(sid, {'QUERY_STRING': f'EIO=x&playgroundId={playground_id}'}, {})


@pytest.mark.asyncio
async def test_connect_isRoot(setup_connect):
    await setup_connect()
    await connect(sid, {}, {'playgroundId': playground_id, 'isRoot': True})
    playground = playground_manager.find_by_playground_id(playground_id)
    assert playground.is_root

@pytest.mark.asyncio
async def test_connect_ideserver_connnect_refused(setup_connect, mocker):
    await setup_connect()
    playground = playground_manager.find_by_playground_id(playground_id)
    playground.playground_status = PlaygroundStatusType.DRAFTED

    mocker.patch.object(playground, 'connect_ide_server', side_effect=IDEServerConnectException('x'))
    with pytest.raises(socketio.exceptions.ConnectionRefusedError, match='Connecting to IDEServer fail'):
        await connect(sid, {'QUERY_STRING': f'playgroundId={playground_id}'}, {})

    mocker.patch.object(playground, 'connect_ide_server', side_effect=ValueError('x'))
    with pytest.raises(socketio.exceptions.ConnectionRefusedError, match='Fatal error'):
        await connect(sid, {'QUERY_STRING': f'playgroundId={playground_id}'}, {})

@pytest.mark.asyncio
async def test_on_any(setup_connect):
    await setup_connect()

    # FIXME: 耗时增加，需要分析原因
    res = await on_any('message', sid, 'hello')
    assert res is True
    res = await on_any('uResetMessageSession', sid)
    assert res['status'] == 'ok'
    res = await on_any('noexist', sid)
    assert res['status'] == 'fail' and res['message'].startswith('Received unhandled event')

    res = await on_any('uResetMessageSession', sid, 'wrong_param')
    assert res['status'] == 'fail' and res['message'].startswith('UserEventArgumentException')

    res = await on_any('uBuildWorkspace', sid, 'wrong_param', 'wrong_param2')
    assert res['status'] == 'fail' and res['message'].startswith('UserEventArgumentException')

@pytest.mark.asyncio
async def test_on_any_validation_error(setup_connect):
    await setup_connect()
    # 测试 on_user_event 时 ValidationError 的输出格式优化
    playground = playground_manager.find_by_playground_id(playground_id)
    task = create_test_task()
    playground.agent_controller.workspace.set_task(task)
    playground.agent_controller.task_state.current_state = playground.agent_controller.task_state.done
    step_dict = {
        'title': 'step1',
        'task_actions': [
            {
                'title': 'action1',
                'action_object': {
                    'path': 'file1.txt',
                    'target': 'testClass',
                    'detailed_requirement': 'xxx'
                }
            }
        ],
    }
    res = await on_any('uAddStep', sid, step_dict)
    assert res['status'] == 'fail' and res['message'] == 'Validation Error:\n- action: Field required'

@pytest.mark.asyncio
async def test_on_any_error(setup_connect, mocker):
    """测试 Exception 情况"""
    async def empty_method(self): pass
    mock = mocker.create_autospec(empty_method, side_effect=ValueError('Fatal error'))

    mocker.patch('heracles.server.clacky.playground_channel.PlaygroundChannel.on_u_reset_task', mock)

    await setup_connect()
    res = await on_any('uResetTask', sid)
    assert res['status'] == 'fatal' and res['message'].startswith('Fatal error')


@pytest.mark.asyncio
async def test_health_check():
    res = await health_check()
    assert res == 'OK'

    res = await index()


@pytest.mark.asyncio
async def test_shutdown():
    await shutdown_event()

@pytest.mark.asyncio
async def test_startup():
    await startup_event()

@pytest.mark.asyncio
async def test_disconnect_error(setup_connect, mocker):
    """测试断开连接时的异常情况"""
    await setup_connect()

    # 测试当playground不存在时的情况
    non_existent_sid = "non-existent-sid"
    await disconnect(non_existent_sid)

    # 测试在断开连接过程中发生异常的情况
    playground = playground_manager.find_by_playground_id(playground_id)
    mocker.patch.object(playground, 'disconnect_ide_server', side_effect=Exception('Disconnect error'))
    await disconnect(sid)  # 即使发生异常，disconnect 也应该正常完成

    # 测试重复断开连接的情况
    await disconnect(sid)  # 应该能正常处理重复断开的情况


@pytest.mark.asyncio
async def test_disconnect_logger_error(setup_connect, mocker):
    """测试断开连接时logger相关的异常情况"""
    await setup_connect()

    # 模拟获取playground_logger失败的情况
    mocker.patch('heracles.server.main.get_playground_logger', side_effect=Exception('Logger error'))
    await disconnect(sid)  # 应该能正常处理logger获取失败的情况


@pytest.mark.asyncio
async def test_connect_project_id_mismatch(setup_connect, mocker):
    await setup_connect()
    mock_transmit = mocker.patch('heracles.server.clacky.playground_channel.PlaygroundChannel.transmit')

    await connect(sid, {}, {'playgroundId': playground_id, 'projectId': 'project_id1'})

    await connect(sid, {}, {'playgroundId': playground_id, 'projectId': 'project_id2'})

    assert mock_transmit.call_args[0][0] == 'projectIdMismatch'
    assert mock_transmit.call_args[0][1] == f"Project id mismatch. sid={sid}, playground_id={playground_id}"

@pytest.mark.asyncio
async def test_on_any_project_id_mismatch(setup_connect, mocker):
    await setup_connect()
    mock_transmit = mocker.patch('heracles.server.clacky.playground_channel.PlaygroundChannel.transmit')

    await connect(sid, {}, {'playgroundId': playground_id, 'projectId': 'project_id1'})

    sid2 = "test-sid2"
    await connect(sid2, {}, {'playgroundId': playground_id, 'projectId': 'project_id2'})
    await on_any('uResetTask', sid2)

    assert mock_transmit.call_args[0][0] == 'projectIdMismatch'
    assert mock_transmit.call_args[0][1] == f"Project id mismatch. sid={sid2}, playground_id={playground_id}"

    await disconnect(sid)
    await disconnect("test-sid2")
