import pytest
import statemachine  # type: ignore

from heracles.core.schema.task_state import TaskState
from heracles.core.exceptions import Agent<PERSON><PERSON>Exception

def test_machine():
    task_state = TaskState()
    assert task_state.inited.is_active

    task_state.plan_task()
    assert task_state.planning.is_active

    with pytest.raises(statemachine.exceptions.TransitionNotAllowed):
        task_state.pause_task()

    task_state.start_task()
    task_state.end_task()
    task_state.append_task()

    assert task_state.appended.is_active
    task_state.start_task()
    assert task_state.working.is_active

    assert task_state.current_state.id == 'working'

def test_machine_appended_limit():
    task_state = TaskState()
    task_state.appended_limit = 3
    task_state.current_state = task_state.done

    task_state.append_task()
    assert task_state.appended_count == 1

    task_state.start_task()
    task_state.end_task()
    task_state.append_task()
    assert task_state.appended_count == 2

    task_state.start_task()
    task_state.end_task()
    task_state.append_task()
    assert task_state.appended_count == 3

    task_state.start_task()
    task_state.end_task()
    with pytest.raises(AgentRunException):
        task_state.append_task()

async def trigger_event(state):
    print(f'trigger event async: {state}')

def trigger_event_sync(state):
    print(f'trigger event sync: {state}')

@pytest.mark.asyncio
async def test_machine_trigger_event():
    task_state = TaskState(trigger_event)
    task_state.plan_task()

    task_state = TaskState(trigger_event_sync)
    task_state.plan_task()

def test_modify_step_permissions():
    task_state = TaskState()
    # 在planning状态允许修改
    task_state.plan_task()
    assert task_state.can_modify_step() is True

    # 在working状态禁止修改
    task_state.start_task()
    assert task_state.can_modify_step() is False

    task_state.end_task()

    # 在appended状态允许修改
    task_state.append_task()
    assert task_state.can_modify_step() is True

def test_modify_action_permissions():
    task_state = TaskState()
    # 在planning状态允许修改
    task_state.plan_task()
    assert task_state.can_modify_action() is True

    # 在working状态禁止修改
    task_state.start_task()
    assert task_state.can_modify_action() is False

    task_state.end_task()

    # 在appended状态允许修改
    task_state.append_task()
    assert task_state.can_modify_action() is True

    # 在working状态禁止修改
    task_state.start_task()
    assert task_state.can_modify_action() is False

def test_can_add_step():
    task_state = TaskState()
    task_state.plan_task()
    assert task_state.can_add_step() is True

    task_state.start_task()
    assert task_state.can_add_step() is False

    task_state.pause_task()
    assert task_state.can_add_step() is True

    task_state.append_task()
    assert task_state.can_add_step() is True

    task_state.start_task()

    task_state.end_task()
    assert task_state.can_add_step() is True
