import pytest

from heracles.core.schema.task import ActionStatus
from heracles.agent_roles.plan_role.create_test_task import create_test_task
from heracles.core.exceptions import UserTaskStatusException

async def action_executor(task_action, is_rerun=False):
    print(f"working on: {task_action}, is_rerun: {is_rerun}")

@pytest.mark.asyncio
async def test_task_role(create_workspace, mocker):
    workspace = await create_workspace

    task = create_test_task()
    workspace.set_task(task)
    task_role = workspace.playground.agent_controller.task_role
    task_role.task_state.current_state = task_role.task_state.working
    await task_role.run(action_executor, delay_time=0)
    assert task_role.task_state.done.is_active

@pytest.mark.asyncio
async def test_task_role_rerun(create_workspace, mocker):
    workspace = await create_workspace

    task = create_test_task()
    workspace.set_task(task)
    task_role = workspace.playground.agent_controller.task_role
    task_role.task_state.current_state = task_role.task_state.working
    await task_role.run(action_executor)
    task_action = task.find_action_by_id("1-1")
    assert task_action.status == ActionStatus.COMPLETED_STATUS

    task_action.status = ActionStatus.ABANDONED_STATUS
    await task_role.run_action(action_executor, task_action, is_rerun=True)
    assert task_action.status == ActionStatus.COMPLETED_STATUS

@pytest.mark.asyncio
async def test_task_role_append(create_workspace, mocker):
    workspace = await create_workspace

    task = create_test_task()
    task_role = workspace.playground.agent_controller.task_role

    def create_step_dict(file_path):
        return {
            'title': '追加步骤',
            'task_actions': [{
                'title': '添加文件',
                'action': 'add_file',
                'action_object': {
                    'path': file_path,
                    'target': 'createNewFile',
                    'detailed_requirement': 'test requirement'
                }
            }]
        }
    with pytest.raises(UserTaskStatusException, match='task need to be in Done/Planning/Canceled/Pausing/Appended state'):
        await workspace.playground.agent_controller.on_u_add_step(create_step_dict('new_file.rb'))

    workspace.set_task(task)

    # 在planning状态时，可以无限增加step和action
    task_role.task_state.current_state = task_role.task_state.planning
    for i in range(10):
        await workspace.playground.agent_controller.on_u_add_step(create_step_dict(f'new_file_in_planning{i}.rb'))

    task_role.task_state.current_state = task_role.task_state.working
    await task_role.run(action_executor, delay_time=0)
    assert task_role.task_state.done.is_active

    # 在done状态时，追加后会变为appended状态
    step_id = await workspace.playground.agent_controller.on_u_add_step(create_step_dict('new_file.rb'))
    assert task_role.task_state.appended.is_active
    assert task.find_step_by_id(step_id).get_next_runnable_action() is not None

    # 最多追加3个step
    await workspace.playground.agent_controller.on_u_add_step(create_step_dict('new_file2.rb'))
    await workspace.playground.agent_controller.on_u_add_step(create_step_dict('new_file3.rb'))
    with pytest.raises(UserTaskStatusException, match='reached maximum limit of 3 steps'):
        await workspace.playground.agent_controller.on_u_add_step(create_step_dict('new_file4.rb'))

    task_role.task_state.start_task()
    await task_role.run(action_executor, delay_time=0)
    assert task_role.task_state.done.is_active
