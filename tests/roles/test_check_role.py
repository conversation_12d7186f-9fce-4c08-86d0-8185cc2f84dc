import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from heracles.core.schema.models import TestCaseRuleModels, TestCaseRuleModel, ErrorFoundModel
from heracles.agent_roles.check_role import CheckRole, ObserverLog
from heracles.core.schema import ProjectErrorMessage


def abnormal_rule_model(*args, **kwargs):
    return TestCaseRuleModels(
        rules=[
            TestCaseRuleModel(
                title='Test Rule 1',
                score=1,
                weight=0.0,
                ref_id='TaskAction/1-1'
            )
        ]
    )

def failed_rule_model(*args, **kwargs):
    return TestCaseRuleModels(
        rules=[
            TestCaseRuleModel(
                title='Test Rule 1',
                score=1,
                weight=1.0,
                ref_id='TaskAction/1-1'
            ),
            TestCaseRuleModel(
                title='Test Rule 2',
                score=0,
                weight=1.0,
                ref_id='TaskAction/1-1'
            )
        ]
    )

def success_rule_model(*args, **kwargs):
    return TestCaseRuleModels(
        rules=[
            TestCaseRuleModel(
                title='Test Rule 1',
                score=1,
                weight=1.0,
                ref_id='TaskAction/1-1'
            )
        ]
    )

# @pytest.mark.asyncio
# async def test_check_role_check_task(create_workspace, mocker):
#     workspace = await create_workspace
#     check_role = CheckRole(workspace)
#     mocker.patch.object(check_role, 'aask', side_effect=success_rule_model)

#     workspace.set_task(Task(title='Test Task', description='Test Description', task_steps=[
#         TaskStep(title='Test Step', task_actions=[
#             TaskAction(title='Test Action 1', action=ActionType.ADD_FILE,
#             action_object=FileActionObject(path='test/path1', detailed_requirement='Test requirement 1')
#             )]
#         )
#     ]))

#     errors = await check_role.check_task()
#     assert len(errors) == 0

#     mocker.patch.object(check_role, 'aask', side_effect=failed_rule_model)
#     errors = await check_role.check_task()
#     assert len(errors) == 1

@pytest.mark.asyncio
async def test_check_role_monitor_errors(create_workspace, mocker):
    workspace = await create_workspace
    emitter = workspace.playground.ide_server_client.event_emitter
    workspace.observations.on_terminal(workspace.playground.agent_controller.check_role.on_terminal)

    assert emitter.has_listener('terminal')

    await workspace.smart_detect.set_status('monitoring_errors')
    terminal_id = 'test'
    check_role = workspace.playground.agent_controller.check_role
    mocker.patch.object(check_role, 'aask', return_value=ErrorFoundModel(is_new_error=True, title='Test Error'))
    await emitter.emit('terminal', 'error console line 1', terminal_id, 'goAgent')
    # 在 Python 中，range(2, 10) 会生成从 2 到 9 的序列（不包含 10）
    for i in range(2, 10):
        await emitter.emit('terminal', f'console line {i}', terminal_id, 'goAgent')
        await asyncio.sleep(1)
    await asyncio.sleep(5)
    assert len(workspace.smart_detect.errors) == 1
    # TODO: asyncio.sleep 有误差。虽然有 9 行输出，但因为延迟防抖 5 秒，只有第一行有 error，所以只有前 6 行会被记录
    # assert len(workspace.smart_detect.errors[0].content.split('\n')) == 6

    for i in range(1, 10):
        await emitter.emit('terminal', f'error console line {i}', terminal_id, 'goAgent')
        await asyncio.sleep(1)
    await asyncio.sleep(5)
    assert len(workspace.smart_detect.errors) == 2
    # TODO: asyncio.sleep 有误差。前面剩余的 7-9 行共 3 行 + 后边增加的 9 行，有 12 行会被记录
    # assert len(workspace.smart_detect.errors[1].content.split('\n')) == 12

# @pytest.mark.asyncio
# async def test_check_role_no_task_exception(create_workspace):
#     """测试没有任务时的异常处理（43, 47行）"""
#     workspace = await create_workspace
#     check_role = CheckRole(workspace)

#     # 测试没有任务时的check_task
#     with pytest.raises(Exception, match="check task failed, reason: no task here"):
#         await check_role.check_task()

#     # 测试没有任务时的check_task_with_rules
#     with pytest.raises(Exception, match="check task failed, reason: no task here"):
#         await check_role.check_task_with_rules()

# @pytest.mark.asyncio
# async def test_check_role_abnormal_response(create_workspace, mocker):
#     """测试异常响应处理（71行）"""
#     workspace = await create_workspace
#     check_role = CheckRole(workspace)

#     # 设置任务
#     workspace.set_task(Task(title='Test Task', description='Test Description', task_steps=[
#         TaskStep(title='Test Step', task_actions=[
#             TaskAction(title='Test Action 1', action=ActionType.ADD_FILE,
#             action_object=FileActionObject(path='test/path1', detailed_requirement='Test requirement 1')
#             )]
#         )
#     ]))

#     # 模拟返回非TestCaseRuleModels的响应
#     mocker.patch.object(check_role, 'aask', return_value="Invalid response")

#     with pytest.raises(Exception, match="check task failed, reason: Invalid response"):
#         await check_role.check_task()

@pytest.mark.asyncio
async def test_check_role_run_and_check_project_scenarios(create_workspace, mocker):
    """测试运行和检查项目的不同场景"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 模拟工具方法
    mocker.patch.object(workspace.tools, 'run_status', return_value='RUNNING')
    mocker.patch.object(workspace.tools, 'stop_project', new_callable=AsyncMock)
    mocker.patch.object(workspace.tools, 'run_project', new_callable=AsyncMock)
    mocker.patch.object(workspace.tools, 'get_lint_errors', new_callable=AsyncMock)
    mocker.patch.object(workspace, 'trigger_general_loading', new_callable=AsyncMock)
    mocker.patch('heracles.core.utils.wait_for', side_effect=Exception('timeout'))

    try:
        await check_role.run_and_check_project()
    except Exception:
        pass  # 预期会有异常，因为我们模拟了timeout

    # 验证调用了相关方法
    workspace.tools.stop_project.assert_called_once()
    workspace.tools.run_project.assert_called_once()

@pytest.mark.asyncio
async def test_check_role_terminal_processing_edge_cases(create_workspace):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    terminal_id = 'new_terminal'
    assert terminal_id not in check_role.log_dict

    await check_role.on_terminal('test line', terminal_id, 'goAgent')

    assert terminal_id in check_role.log_dict
    assert check_role.log_dict[terminal_id].type == 'terminal'

@pytest.mark.asyncio
async def test_check_role_process_line_timing_logic(create_workspace, mocker):
    """测试处理行的时间逻辑（115, 125行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 设置监控状态
    await workspace.smart_detect.set_status('monitoring_errors')

    # 创建日志对象
    log = ObserverLog('terminal')

    # 模拟时间超过10秒的情况
    import time
    original_time = time.time

    def mock_time():
        return original_time() + 20  # 模拟20秒后

    mocker.patch('time.time', side_effect=mock_time)

    # 添加一些数据到日志
    log.lines.push('old line')
    log.last_line_timestamp = original_time() - 15  # 15秒前

    # 处理新行
    await check_role._process_line('new line without error', log)

    # 验证日志被清空并添加了新行
    assert 'old line' not in [item for item in log.lines.items]

@pytest.mark.asyncio
async def test_check_role_error_handling_monitoring(create_workspace, mocker):
    """测试错误处理监控（130-134行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 设置监控状态
    await workspace.smart_detect.set_status('monitoring_errors')

    log = ObserverLog('terminal')

    # 模拟handle_error方法
    mocker.patch.object(check_role, 'handle_error')

    # 处理包含错误关键字的行
    await check_role._process_line('Error: something went wrong', log)

    # 验证handle_error被调用
    check_role.handle_error.assert_called_once()

@pytest.mark.asyncio
async def test_check_role_check_if_new_error_duplicate(create_workspace, mocker):
    """测试检查重复错误（147行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)
    log = ObserverLog('terminal')
    log.lines.push('Error: duplicate error message')

    # 添加现有错误到workspace
    existing_error = ProjectErrorMessage(
        title='Existing Error',
        ref_id='test/1234',
        category='terminal',
        content='Error: duplicate error message'
    )
    workspace.smart_detect.errors = [existing_error]

    # 模拟precise_ratio返回高相似度
    mocker.patch('heracles.agent_roles.code_role.utils.precise_ratio', return_value=0.9)

    result = await check_role.check_if_new_error(log)

    # 验证返回不是新错误
    assert not result.is_new_error

@pytest.mark.asyncio
async def test_check_role_trigger_callbacks_error_handling(create_workspace, mocker):
    """测试触发回调的错误处理（166, 169-174, 177行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 测试trigger_error_handler
    mocker.patch.object(workspace.playground, 'trigger_error_handler')
    await check_role.trigger_error_handler('test error message')
    workspace.playground.trigger_error_handler.assert_called_once_with('test error message')

    # 测试trigger_tool_callback异常处理 - 只验证不会抛出异常
    invalid_tool_call = {'invalid': 'format'}

    # 不应该抛出异常，而是记录错误日志
    try:
        await check_role.trigger_tool_callback(invalid_tool_call, 'end')
        # 验证没有抛出异常
        assert True
    except Exception as e:
        # 如果抛出异常，测试失败
        raise AssertionError("trigger_tool_callback should not raise exception for invalid tool_call") from e

# 删除测试不存在方法的测试用例

# 删除测试不存在方法的测试用例

# 删除测试不存在方法的测试用例

@pytest.mark.asyncio
async def test_run_method(create_workspace):
    workspace = await create_workspace
    check_role = CheckRole(workspace)
    await check_role.run()

@pytest.mark.asyncio
async def test_run_and_check_project_with_logs(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    check_role.log_dict['test'] = ObserverLog('terminal')
    check_role.log_dict['test'].lines.push('test line')

    mocker.patch.object(workspace.tools, 'run_status', return_value='STOPPED')
    mocker.patch.object(workspace.tools, 'run_project', new_callable=AsyncMock)
    mocker.patch.object(workspace.tools, 'get_lint_errors', new_callable=AsyncMock)
    mocker.patch.object(workspace, 'trigger_general_loading', new_callable=AsyncMock)
    mocker.patch('heracles.core.utils.wait_for', side_effect=Exception('timeout'))

    try:
        await check_role.run_and_check_project()
    except Exception:
        pass  # 预期会有异常

@pytest.mark.asyncio
async def test_on_terminal_non_goagent(create_workspace):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    await check_role.on_terminal('test line', 'terminal1', 'other_type')
    assert 'terminal1' in check_role.log_dict  # 修正：应该在log_dict中

@pytest.mark.asyncio
async def test_analyze_browser_console_logs_no_ports(create_workspace):
    """测试analyze_browser_console_logs方法 - 无端口情况"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 模拟没有http端口
    workspace.playground.ide_server_client.http_ports = []

    result = await check_role.analyze_browser_console_logs()
    assert result is None

@pytest.mark.asyncio
async def test_analyze_browser_console_logs_with_logs(create_workspace, mocker):
    """测试analyze_browser_console_logs方法 - 有日志情况"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 模拟有http端口
    workspace.playground.ide_server_client.http_ports = ['8080']

    # 模拟browser_console_logs返回日志
    mock_logs = ['Error: test error', 'Warning: test warning']
    mocker.patch.object(workspace.tools, 'browser_console_logs', new_callable=AsyncMock, return_value=mock_logs)
    mocker.patch.object(check_role, '_process_line', new_callable=AsyncMock)

    await check_role.analyze_browser_console_logs()

    # 验证创建了browser_console日志
    assert 'browser_console' in check_role.log_dict
    assert check_role.log_dict['browser_console'].type == 'browser_console'

    # 验证处理了每个日志行
    assert check_role._process_line.call_count == 2  # type: ignore

@pytest.mark.asyncio
async def test_analyze_browser_console_logs_exception(create_workspace, mocker):
    """测试analyze_browser_console_logs方法 - 异常情况"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 模拟有http端口
    workspace.playground.ide_server_client.http_ports = ['8080']

    # 模拟browser_console_logs抛出异常
    mocker.patch.object(workspace.tools, 'browser_console_logs', new_callable=AsyncMock, side_effect=Exception('test error'))

    # 应该不抛出异常，而是记录警告
    await check_role.analyze_browser_console_logs()

@pytest.mark.asyncio
async def test_analyze_screenshot_with_screenshot(create_workspace, mocker):
    """测试analyze_screenshot方法 - 有截图情况"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 模拟browser_screenshot返回截图
    mock_screenshot = 'screenshot_url'
    mocker.patch.object(workspace.tools, 'browser_screenshot', new_callable=AsyncMock, return_value=mock_screenshot)

    # 模拟check_if_new_error返回新错误
    mock_error_result = Mock()
    mock_error_result.is_new_error = True
    mock_error_result.title = 'Test Error'
    mocker.patch.object(check_role, 'check_if_new_error', new_callable=AsyncMock, return_value=mock_error_result)

    await check_role.analyze_screenshot()

    # 验证添加了错误到workspace
    assert len(workspace.smart_detect.errors) == 1
    error = workspace.smart_detect.errors[0]
    assert error.title == 'Test Error'
    assert error.category == 'screenshot'
    assert error.content == mock_screenshot

@pytest.mark.asyncio
async def test_analyze_screenshot_no_screenshot(create_workspace, mocker):
    """测试analyze_screenshot方法 - 无截图情况"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 模拟browser_screenshot返回None
    mocker.patch.object(workspace.tools, 'browser_screenshot', new_callable=AsyncMock, return_value=None)

    await check_role.analyze_screenshot()

    # 验证没有添加错误
    assert len(workspace.smart_detect.errors) == 0

@pytest.mark.asyncio
async def test_handle_error_not_new_error(create_workspace, mocker):
    """测试handle_error方法 - 不是新错误的情况"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    log = ObserverLog('terminal')
    log.lines.push('test error')

    # 模拟check_if_new_error返回不是新错误
    mock_error_result = Mock()
    mock_error_result.is_new_error = False
    mocker.patch.object(check_role, 'check_if_new_error', new_callable=AsyncMock, return_value=mock_error_result)

    await check_role.handle_error(log)

    # 验证没有添加错误到workspace
    assert len(workspace.smart_detect.errors) == 0

@pytest.mark.asyncio
async def test_check_if_new_error_with_image(create_workspace, mocker):
    """测试check_if_new_error方法 - 使用图片的情况"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 模拟aask方法
    mock_result = Mock()
    mock_result.is_new_error = True
    mock_result.title = 'Screenshot Error'
    mocker.patch.object(check_role, 'aask', new_callable=AsyncMock, return_value=mock_result)

    result = await check_role.check_if_new_error(image_url='test_image_url')

    # 验证调用了aask方法并传入了图片
    check_role.aask.assert_called_once()  # type: ignore
    call_args = check_role.aask.call_args  # type: ignore
    assert 'images' in call_args.kwargs
    assert call_args.kwargs['images'] == ['test_image_url']
    assert result == mock_result

@pytest.mark.asyncio
async def test_process_line_non_monitoring(create_workspace):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    await workspace.smart_detect.set_status('init')
    log = ObserverLog('terminal')

    await check_role._process_line('test\nline', log)
    assert len(log.lines.items) > 0

@pytest.mark.asyncio
async def test_handle_error_not_new(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    log = ObserverLog('terminal')
    log.lines.push('error line')

    mocker.patch.object(check_role, 'check_if_new_error', return_value=ErrorFoundModel(is_new_error=False, title='Old Error'))

    await check_role.handle_error(log)

@pytest.mark.asyncio
async def test_handle_error_new_error(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    log = ObserverLog('terminal')
    log.lines.push('new error line')

    mocker.patch.object(check_role, 'check_if_new_error', return_value=ErrorFoundModel(is_new_error=True, title='New Error'))

    await check_role.handle_error(log)
    await asyncio.sleep(0.1)
    assert len(workspace.smart_detect.errors) >= 0

@pytest.mark.asyncio
async def test_check_if_new_error_with_llm(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    log = ObserverLog('terminal')
    log.lines.push('unique error message')

    workspace.smart_detect.errors = []
    mocker.patch.object(check_role, 'aask', return_value=ErrorFoundModel(is_new_error=True, title='New Error'))

    result = await check_role.check_if_new_error(log)
    assert result.is_new_error

@pytest.mark.asyncio
async def test_trigger_tool_callback_success(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    mocker.patch('heracles.agent_roles.check_role.tool_call_to_argument_pair', return_value=('test_tool', 'test_value'))

    await check_role.trigger_tool_callback({'tool': 'test'}, 'end')

@pytest.mark.asyncio
async def test_trigger_chunk_message(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    mocker.patch.object(workspace, 'trigger')

    await check_role.trigger_chunk_message('test message')

# 删除测试不存在方法的测试用例

@pytest.mark.asyncio
async def test_run_and_check_project_no_terminal_output(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    mocker.patch.object(workspace.tools, 'run_status', return_value='STOPPED')
    mocker.patch.object(workspace.tools, 'run_project', new_callable=AsyncMock)
    mocker.patch.object(workspace.tools, 'get_lint_errors', new_callable=AsyncMock)
    mocker.patch.object(workspace, 'trigger_general_loading', new_callable=AsyncMock)
    mocker.patch('heracles.core.utils.wait_for', side_effect=Exception('timeout'))

    try:
        await check_role.run_and_check_project()
    except Exception:
        pass  # 预期会有异常

@pytest.mark.asyncio
async def test_process_line_with_time_check(create_workspace):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    await workspace.smart_detect.set_status('monitoring_errors')
    log = ObserverLog('terminal')
    log.lines.push('old line')

    import time
    log.last_line_timestamp = time.time() - 15

    await check_role._process_line('new\nline', log)
    assert len(log.lines.items) > 0

# 删除测试不存在方法的测试用例

@pytest.mark.asyncio
async def test_trigger_tool_callback_exception_handling(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Mock tool_call_to_argument_pair to raise an exception
    mocker.patch('heracles.agent_roles.check_role.tool_call_to_argument_pair', side_effect=Exception('test error'))
    mocker.patch.object(workspace, 'trigger', new_callable=AsyncMock)

    tool_call = Mock()
    await check_role.trigger_tool_callback(tool_call, 'end')

    # Should not call workspace.trigger due to exception
    workspace.trigger.assert_not_called()

@pytest.mark.asyncio
async def test_trigger_tool_callback_non_end_status(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    mocker.patch.object(workspace, 'trigger', new_callable=AsyncMock)

    tool_call = Mock()
    await check_role.trigger_tool_callback(tool_call, 'start')

    # Should not call workspace.trigger for non-end status
    workspace.trigger.assert_not_called()

@pytest.mark.asyncio
async def test_trigger_error_handler(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    mocker.patch.object(workspace.playground, 'trigger_error_handler', new_callable=AsyncMock)

    message = Mock()
    await check_role.trigger_error_handler(message)

    workspace.playground.trigger_error_handler.assert_called_once_with(message)

@pytest.mark.asyncio
async def test_run_and_check_project_clacky_log_has_output(create_workspace, mocker):
    """测试run_and_check_project方法 - clacky log有输出的情况（覆盖99-100行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 模拟相关方法
    mocker.patch.object(workspace.tools, 'run_cmd', new_callable=AsyncMock)
    mocker.patch.object(workspace.tools, 'run_status', return_value='STOPPED')
    mocker.patch.object(workspace.tools, 'run_project', new_callable=AsyncMock)
    mocker.patch.object(workspace.tools, 'get_lint_errors', new_callable=AsyncMock)
    mocker.patch.object(workspace, 'trigger_general_loading', new_callable=AsyncMock)

    # 设置log_dict，模拟browser_console有goAgent类型的日志且有输出
    mock_log = ObserverLog('browser_console')
    mock_log.lines.push('some log output')  # 添加一些输出，这会让len(log.lines.items) > 0为True
    check_role.log_dict['browser_console'] = mock_log
    check_role.terminal_id_type['browser_console'] = 'goAgent'

    # 模拟wait_for立即返回（有输出）
    mocker.patch('heracles.agent_roles.check_role.wait_for', new_callable=AsyncMock)

    # 模拟workspace.tools.local_url和browser_goto
    mocker.patch.object(workspace.tools, 'local_url', new_callable=AsyncMock, return_value='http://localhost:3000')
    mocker.patch.object(workspace.tools, 'browser_goto', new_callable=AsyncMock)

    # 模拟http_ports存在
    workspace.playground.ide_server_client.http_ports = ['3000']

    await check_role.run_and_check_project()

@pytest.mark.asyncio
async def test_run_and_check_project_wait_clacky_log_success(create_workspace, mocker):
    """测试run_and_check_project方法 - 等待clacky log成功的情况（覆盖105-116行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 模拟相关方法
    mocker.patch.object(workspace.tools, 'run_cmd', new_callable=AsyncMock)
    mocker.patch.object(workspace.tools, 'run_status', return_value='STOPPED')
    mocker.patch.object(workspace.tools, 'run_project', new_callable=AsyncMock)
    mocker.patch.object(workspace.tools, 'get_lint_errors', new_callable=AsyncMock)
    mocker.patch.object(workspace, 'trigger_general_loading', new_callable=AsyncMock)

    # 设置log_dict，模拟browser_console有goAgent类型的日志但初始无输出
    mock_log = ObserverLog('browser_console')
    check_role.log_dict['browser_console'] = mock_log
    check_role.terminal_id_type['browser_console'] = 'goAgent'

    # 模拟wait_for成功等待到输出
    call_count = 0
    async def mock_wait_for(func, timeout, interval):
        nonlocal call_count
        call_count += 1
        if call_count == 1:
            # 第一次调用时，添加一些输出
            mock_log.lines.push('some log output')
            return True

    mocker.patch('heracles.agent_roles.check_role.wait_for', side_effect=mock_wait_for)

    # 模拟workspace.tools.local_url和browser_goto
    mocker.patch.object(workspace.tools, 'local_url', new_callable=AsyncMock, return_value='http://localhost:3000')
    mocker.patch.object(workspace.tools, 'browser_goto', new_callable=AsyncMock)

    # 模拟http_ports存在
    workspace.playground.ide_server_client.http_ports = ['3000']

    await check_role.run_and_check_project()

@pytest.mark.asyncio
async def test_handle_error_early_return_coverage(create_workspace, mocker):
    """测试handle_error方法 - 覆盖177行的early return"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    log = ObserverLog('terminal')
    log.lines.push('test error line')

    # 模拟check_if_new_error返回不是新错误
    mock_error_result = ErrorFoundModel(is_new_error=False, title='Existing Error')
    mocker.patch.object(check_role, 'check_if_new_error', new_callable=AsyncMock, return_value=mock_error_result)

    # 调用handle_error方法，由于使用了@delayed_debouncer装饰器，需要等待一下
    await check_role.handle_error(log)

    # 等待装饰器的延迟执行
    import asyncio
    await asyncio.sleep(2.1)  # 等待超过装饰器的delay时间

    # 验证check_if_new_error被调用
    check_role.check_if_new_error.assert_called_once_with(log)

    # 验证没有添加错误到workspace（因为early return）
    assert len(workspace.smart_detect.errors) == 0
