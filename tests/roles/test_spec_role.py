import pytest

from heracles.core.schema.spec import Spec
from heracles.agent_roles.spec_role import Spec<PERSON><PERSON>
from heracles.core.exceptions import AgentRunException

@pytest.mark.asyncio
async def test_spec_role(create_workspace, mocker):
    workspace = await create_workspace
    sample_spec = Spec(
        suggested_name='test_name',
        suggested_branch='test_branch',
        goal='test',
        goal_detail='test',
        current_list=['this is an current example info'],
        proposed_list=['this is an proposed_list example info'],
        proposed_filechange_list=['app/new/page.tsx'],
        task_scale='micro'
    )

    def side_effect(*args, **kwargs):
        if kwargs.get('response_model') == Spec:
            return sample_spec

    spec_role = SpecRole(workspace)
    mocker.patch.object(spec_role, 'aask', side_effect=side_effect)

    await spec_role.run('normal spec', 'test goal')
    await spec_role.run('test need_confirm', 'test goal')
    await spec_role.run('test unclarified', 'test goal')

    res = await spec_role.run('Initialize the Development Environment', 'test goal')
    assert res.task_scale == 'micro'

    sample_spec.proposed_filechange_list = ['file' + str(i) + '.txt' for i in range(8)]
    mocker.patch.object(spec_role, 'aask', return_value=sample_spec)
    res = await spec_role.run('test filechange scale', 'test goal')
    assert res.task_scale == 'small'

    sample_spec.proposed_filechange_list = ['file' + str(i) + '.txt' for i in range(15)]
    mocker.patch.object(spec_role, 'aask', return_value=sample_spec)
    res = await spec_role.run('test filechange scale', 'test goal')
    assert res.task_scale == 'medium'

@pytest.mark.asyncio
async def test_refine_prompt(create_workspace, mocker):
    workspace = await create_workspace
    spec_role = SpecRole(workspace)

    # Mock the aask method to return a predefined response
    expected_response = "Refined project requirements"
    mocker.patch.object(spec_role, 'aask', return_value=expected_response)

    # Mock workspace tools
    mocker.patch.object(workspace.tools, 'validate_image_url', return_value="https://www.tomatolist.com/1.png")
    mocker.patch.object(workspace.tools, 'read_webpage', return_value="Webpage content")

    # Test with new_project scene
    prompt = "帮我做一个todolist@@user_context[webpage://https://www.tomatolist.com/timer.html]@@"
    scene = "new_project"
    result = await spec_role.refine_prompt(prompt, scene)

    prompt = "帮我做一个todolist@@user_context[image://https://www.tomatolist.com/1.png]@@"
    scene = "new_project"
    result = await spec_role.refine_prompt(prompt, scene)

    assert result == expected_response

    # Test with invalid scene
    with pytest.raises(AgentRunException, match="Invalid scene"):
        await spec_role.refine_prompt(prompt, "invalid_scene")
