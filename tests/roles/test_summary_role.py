import pytest

from heracles.core.schema.task import Task
from heracles.agent_roles.summary_role import Summary<PERSON><PERSON>, Procedure
from heracles.agent_workspace.playbook import Playbook

@pytest.mark.asyncio
async def test_summary_role(create_workspace, mocker):
    workspace = await create_workspace
    task_dict = {
        'id': None,
        'title': 'Add read_cars API to FastAPI app',
        'description': 'Implement the /car GET endpoint to return all car data, ensuring consistency with the cars data model.',
        'task_steps': [
            {
                'id': '1',
                'task_id': None,
                'title': 'Create the GET /car endpoint in FastAPI app',
                'task_actions': [
                    {
                        'id': '1-1',
                        'task_step_id': '1',
                        'task_id': None,
                        'title': 'Modify app/main.py to add new GET endpoint',
                        'action': 'modify_file',
                        'action_object': {
                            'path': 'app/main.py',
                            'target': '',
                            'detailed_requirement': "Define a new GET endpoint at the route '/car'. Use the app.get() decorator to route requests to a new handler function that will eventually call 'api.read_cars()' to obtain the list of cars from the JSON file.",  # noqa: E501
                            'references': ['file://app/api/api.py', 'file://app/main.py'],
                            'snapshot_uuid': None,
                        },
                        'status': 'inited',
                        'result': None,
                    }
                ],
            },
            {
                'id': '2',
                'task_id': None,
                'title': 'Implement the read_cars function to load car data',
                'task_actions': [
                    {
                        'id': '2-1',
                        'task_step_id': '2',
                        'task_id': None,
                        'title': 'Modify app/api/api.py to add read_cars function',
                        'action': 'modify_file',
                        'action_object': {
                            'path': 'app/api/api.py',
                            'target': '',
                            'detailed_requirement': "Add a new function 'read_cars()' that reads data from 'data/cars.json' using 'json.load'. Return the loaded car data.",  # noqa: E501
                            'references': ['file://app/api/api.py', 'file://data/cars.json'],
                            'snapshot_uuid': None,
                        },
                        'status': 'inited',
                        'result': None,
                    }
                ],
            },
            {
                'id': '3',
                'task_id': None,
                'title': 'Update cars data model for compatibility',
                'task_actions': [
                    {
                        'id': '3-1',
                        'task_step_id': '3',
                        'task_id': None,
                        'title': 'Modify app/db/models.py to define Car model',
                        'action': 'modify_file',
                        'action_object': {
                            'path': 'app/db/models.py',
                            'target': '',
                            'detailed_requirement': "Define a new Pydantic BaseModel class 'Car' corresponding to the JSON structure in 'cars.json'. Add attributes: id (int), name (str), fuel (str), price (str), category (str), and link (str) to this class.",  # noqa: E501
                            'references': ['file://app/db/models.py', 'file://data/cars.json'],
                            'snapshot_uuid': None,
                        },
                        'status': 'inited',
                        'result': None,
                    }
                ],
            },
        ],
    }

    procedure_dict = {
        'title': 'Implement /car GET endpoint in FastAPI application',
        'description': "This update introduces a new GET endpoint '/car' to the FastAPI application, which returns a list of cars read from a JSON file. The corresponding read function is also implemented in the API module, along with a new Pydantic model for type validation.",  # noqa: E501
        'steps': [
            {
                'title': 'Add GET endpoint for car data in main.py',
                'description': "Modify 'app/main.py' to include a new route definition for the GET endpoint at '/car'. This endpoint will call the 'read_car' function from the API to fetch the car data.",  # noqa: E501
            },
            {
                'title': 'Implement read_car function in api.py',
                'description': "In 'app/api/api.py', add the 'read_car' function. This function will open 'data/cars.json', load the data using 'json.load', and return the loaded list of cars.",  # noqa: E501
            },
            {
                'title': 'Define Car model in models.py',
                'description': "Create a new class in 'app/db/models.py' using Pydantic's BaseModel to define the data structure for each car in the JSON. Include attributes: id, name, fuel, price, category, and link.",  # noqa: E501
            },
        ],
    }
    task = Task.model_validate(task_dict)
    llm_res = Procedure.model_validate(procedure_dict)
    mocker.patch.object(SummaryRole, 'aask', return_value=llm_res)
    mocker.patch.object(workspace.playbook_manager, 'upload', return_value=None)
    mocker.patch.object(workspace, 'task', return_value=task)

    summary_role = SummaryRole(workspace)
    playbook = await summary_role.run()
    assert isinstance(playbook, Playbook)
    assert playbook.title
    assert playbook.description
    # 从 workspace.knowledge 中提取 tags
    assert playbook.tags
    assert playbook.original_content
