import litellm
import pytest

from heracles.agent_roles.utils import sanitize_generated_json
from heracles.agent_workspace.workspace_analyze_item import WorkspaceAnalyzeItem
from heracles.core.schema.knowledge import LanguageType
from heracles.core.schema.task import Task, ActionType
from heracles.core.schema.models.task_model import TaskModel, TaskStepModel, TaskActionModel
from heracles.core.schema.task import CommandLifetimeType
from heracles.agent_roles.plan_role import Plan<PERSON><PERSON>
from heracles.core.exceptions import AgentRunException
from heracles.agent_roles.role_action import AgentRoleNullAction

@pytest.mark.asyncio
async def test_plan_role(create_workspace, mocker):
    workspace = await create_workspace

    # mock 加速测试执行速度
    plan_role = PlanRole(workspace)
    task_str = '{"title":"Test Task","description":"Test Description","task_steps":[{"title":"Test Step","task_actions":[{"action":"add_file","path":"test/path1","target":"","command":"","lifetime":"N/A","detailed_requirement":"Test requirement 1","references":[]},{"action":"modify_file","path":"test/path2","target":"","command":"","lifetime":"N/A","detailed_requirement":"Test requirement 2","references":[]}]}]}'  # noqa
    task_json = TaskModel(**sanitize_generated_json(task_str)).to_dict()

    # Create two separate mock functions for streaming and non-streaming
    async def mock_litellm_acompletion_stream(*args, **kwargs):
        yield litellm.ModelResponse(choices=[{'delta': {'role': 'assistant', 'content': task_str}, 'finish_reason': None}], stream=True)

    async def mock_litellm_acompletion_non_stream(*args, **kwargs):
        return {'choices': [{'message': {'content': task_str, 'tool_calls': None}, 'finish_reason': None}]}

    # Use a wrapper function to choose between streaming and non-streaming
    async def mock_litellm_acompletion(*args, **kwargs):
        stream = kwargs.get('stream', False)
        if stream:
            return mock_litellm_acompletion_stream(*args, **kwargs)
        else:
            return await mock_litellm_acompletion_non_stream(*args, **kwargs)

    mocker.patch.object(litellm, 'acompletion', side_effect=mock_litellm_acompletion)
    mocker.patch.object(workspace.rag_searcher, 'search', return_value=[])
    mocker.patch.object(workspace.playbook_manager, 'search_playbook_from_db', return_value=[])

    workspace.language = LanguageType.PYTHON
    basic_info_analyze_item = WorkspaceAnalyzeItem.find_by_name(workspace, 'project_basic_info')
    await basic_info_analyze_item.set_done()

    plan_role = PlanRole(workspace)
    mock_trigger1 = mocker.patch.object(plan_role, 'trigger_task_planned')
    task1 = await plan_role.run('Initialize the Development Environment', 'goal_detail')
    assert isinstance(task1, Task)
    mock_trigger1.assert_any_call(task_json)

    plan_role = PlanRole(workspace)
    mock_trigger2 = mocker.patch.object(plan_role, 'trigger_task_planned')
    task2 = await plan_role.run('Initialize the Development Environment', 'goal_detail', ['proposed_list'])
    assert isinstance(task2, Task)
    mock_trigger2.assert_any_call(task_json)

    with pytest.raises(AgentRunException, match='workspace analyze item: not_exist not found'):
        WorkspaceAnalyzeItem.find_by_name(workspace, 'not_exist')

    task_str = '[{]'
    plan_role = PlanRole(workspace)

    mock_trigger3 = mocker.patch.object(plan_role, 'trigger_task_planned')
    with pytest.raises(AgentRunException, match='LLM model validation error and reached max retries'):
        await plan_role.run('test', 'test')
    mock_trigger3.assert_not_called()


@pytest.mark.asyncio
async def test_partial_think_callback(create_workspace, mocker):
    think_str = '{"items":["1.xxxx", "2.xxx", "3.xxx"]}'

    async def mock_litellm_acompletion_stream(*args, **kwargs):
        yield litellm.ModelResponse(choices=[{'delta': {'role': 'assistant', 'content': think_str}, 'finish_reason': None}], stream=True)

    async def mock_litellm_acompletion_non_stream(*args, **kwargs):
        return {'choices': [{'message': {'content': think_str, 'tool_calls': None}, 'finish_reason': None}]}

    # Use a wrapper function to choose between streaming and non-streaming
    async def mock_litellm_acompletion(*args, **kwargs):
        stream = kwargs.get('stream', False)
        if stream:
            return mock_litellm_acompletion_stream(*args, **kwargs)
        else:
            return await mock_litellm_acompletion_non_stream(*args, **kwargs)

    workspace = await create_workspace
    plan_role = PlanRole(workspace)
    original_partial_think_callback = plan_role.partial_think_callback

    mocker.patch.object(litellm, 'acompletion', side_effect=mock_litellm_acompletion)
    mock_trigger = mocker.patch.object(plan_role, 'partial_think_callback')
    await plan_role._think_about_plan('test', 'test', [], [])
    mock_trigger.assert_any_call(think_str)

    think_str = '{"items": "invalid_type"}'  # items should be a list, not a string
    mocker.patch.object(litellm, 'acompletion', side_effect=mock_litellm_acompletion)
    mock_trigger = mocker.patch.object(plan_role, 'partial_think_callback', wraps=original_partial_think_callback)
    with pytest.raises(AgentRunException, match='LLM model validation error and reached max retries'):
        await plan_role._think_about_plan('test', 'test', [], [])
    mock_trigger.assert_any_call(think_str)

@pytest.mark.asyncio
async def test_make_additional_step(create_workspace, mocker):
    workspace = await create_workspace
    plan_role = PlanRole(workspace)

    # 构造 append_step_intent
    append_step_intent = {
        'goal': 'Add a new feature',
        'plan_draft': 'Implement a new API endpoint',
        'references': []
    }

    mocker.patch.object(plan_role, 'aask', return_value=TaskStepModel(
        title='Test Step',
        task_actions=[TaskActionModel(
            action=ActionType.ADD_FILE,
            path='test/path1',
            detailed_requirement='Test requirement 1',
            references=[]
        )]
    ))
    result = await plan_role.make_additional_step(append_step_intent)
    assert isinstance(result, TaskStepModel)

    mock_trigger = mocker.patch.object(workspace, 'trigger')
    mocker.patch.object(plan_role, 'aask', return_value=TaskStepModel(
        title='Test Step',
        task_actions=[TaskActionModel(
            action=ActionType.RUN_COMMAND,
            command='npm run dev',
            lifetime=CommandLifetimeType.PERSISTENT,
        )]
    ))
    result = await plan_role.make_additional_step(append_step_intent)
    # 验证 trigger 没有被调用 message
    calls = [call[0][1] for call in mock_trigger.call_args_list]
    # 检查每个 call 的字符串内容是否包含 'No valid actions found'
    # 检查至少有一个 call 的字符串内容包含 'No valid actions found'
    assert any('No valid actions found' in call_str for call_str in map(str, calls))
    assert isinstance(result, AgentRoleNullAction)
