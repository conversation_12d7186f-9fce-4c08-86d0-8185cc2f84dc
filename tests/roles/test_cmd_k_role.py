import pytest
from heracles.agent_roles.cmd_k_role import CmdK<PERSON><PERSON>
from heracles.core.exceptions import UserEventArgumentException


@pytest.mark.asyncio
async def test_cmd_k_run_method(create_workspace, mocker):
    workspace = await create_workspace
    cmd_k_role = CmdKRole(workspace)

    file_content = """import os
def main():
    print("hello world")
    return True

if __name__ == '__main__':
    main()
"""
    mocker.patch.object(workspace.tools, 'read_file_content', return_value=file_content)

    edited_content = 'print("hello")\nprint("world")\nreturn True\n'
    mocker.patch.object(cmd_k_role, 'aask', return_value=edited_content)

    mock_trigger_callback = mocker.Mock()

    result = await cmd_k_role.run('test_file.py', 3, 4, 'Make these lines better', mock_trigger_callback)

    assert result == '    print("hello")\n    print("world")\n    return True\n'


@pytest.mark.asyncio
async def test_check_cmd_k_params_validation(create_workspace):
    workspace = await create_workspace
    cmd_k_role = CmdKRole(workspace)

    # Valid parameters should not raise exceptions
    cmd_k_role.check_cmd_k_params('test.py', 1, 10)

    # Test start_line <= 0
    with pytest.raises(UserEventArgumentException) as e:
        cmd_k_role.check_cmd_k_params('test.py', 0, 10)
    assert 'start_line and end_line must be positive integers' in str(e.value)

    # Test end_line <= 0
    with pytest.raises(UserEventArgumentException) as e:
        cmd_k_role.check_cmd_k_params('test.py', 5, 0)
    assert 'start_line and end_line must be positive integers' in str(e.value)

    # Test start_line > end_line
    with pytest.raises(UserEventArgumentException) as e:
        cmd_k_role.check_cmd_k_params('test.py', 10, 5)
    assert 'start_line must be less than or equal to end_line' in str(e.value)
