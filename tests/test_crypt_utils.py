import pytest
from heracles.core.exceptions import Agent<PERSON><PERSON><PERSON>x<PERSON>
from heracles.core.utils.crypt_utils import async_encrypt_content, async_decrypt_content


@pytest.mark.asyncio
async def test_encrypt_decrypt_roundtrip():
    key = '32-byte-long-secret-key-12345678'
    content = 'test'
    encrypted = await async_encrypt_content(content, key)
    assert encrypted
    decrypted = await async_decrypt_content(encrypted, key)
    assert decrypted == content


@pytest.mark.asyncio
async def test_decrypt_with_invalid_key():
    key = '32-byte-long-secret-key-12345678'
    content = 'test'
    encrypted = await async_encrypt_content(content, key)
    assert encrypted
    with pytest.raises(AgentRunException):
        wrong_key = '1234byte-long-secret-key-12345678'
        await async_decrypt_content(encrypted, wrong_key)
