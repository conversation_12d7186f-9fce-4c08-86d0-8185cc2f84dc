import pytest
from unittest.mock import AsyncMock, patch

from heracles.agent_workspace.rag_searcher import <PERSON>g<PERSON><PERSON>cher
from heracles.core.schema import FileSnippet
from heracles.core.exceptions import IDEServerFunCallException


class TestRagSearcher:
    @pytest.fixture
    async def rag_searcher(self, create_workspace):
        """创建RagSearcher实例"""
        workspace = await create_workspace
        searcher = RagSearcher(workspace)
        return searcher

    @pytest.mark.asyncio
    async def test_search_with_non_iterable_result(self, rag_searcher):
        """测试search方法返回非可迭代结果 - 覆盖行37"""
        searcher = await rag_searcher

        with patch.object(searcher.playground, 'func_call', new_callable=AsyncMock) as mock_func_call:
            mock_func_call.return_value = 123  # 数字不可迭代

            with patch.object(searcher, 'expand_queries', new_callable=AsyncMock) as mock_expand:
                mock_expand.return_value = ['test_query']

                result = await searcher.search('test_query', need_expand=True)

                assert result == []

    @pytest.mark.asyncio
    async def test_search_with_ide_server_exception(self, rag_searcher):
        """测试search方法遇到IDEServerFunCallException - 覆盖行39-40"""
        searcher = await rag_searcher

        with patch.object(searcher.playground, 'func_call', new_callable=AsyncMock) as mock_func_call:
            mock_func_call.side_effect = IDEServerFunCallException('Test error')

            with patch.object(searcher, 'expand_queries', new_callable=AsyncMock) as mock_expand:
                mock_expand.return_value = ['test_query']

                result = await searcher.search('test_query', need_expand=True)

                assert result == []

    @pytest.mark.asyncio
    async def test_search_without_expand(self, rag_searcher):
        """测试search方法不扩展查询 - 覆盖行46-62"""
        searcher = await rag_searcher
        mock_snippets = [{'origin_file': 'test.py', 'content': 'test content', 'row_start': 1, 'row_end': 10}]

        with patch.object(searcher.playground, 'func_call', new_callable=AsyncMock) as mock_func_call:
            mock_func_call.return_value = mock_snippets

            with patch('heracles.agent_workspace.rag_searcher.add_line_numbers') as mock_add_lines:
                mock_add_lines.return_value = 'numbered content'

                result = await searcher.search('test_query', need_expand=False)

                assert len(result) == 1
                assert isinstance(result[0], FileSnippet)
                assert result[0].path == 'test.py'
                assert result[0].content == 'numbered content'
                assert result[0].row_start == 1
                assert result[0].row_end == 10

    @pytest.mark.asyncio
    async def test_convert_snippets_to_prompt(self, rag_searcher):
        """测试convert_snippets_to_prompt方法 - 覆盖行68-70"""
        searcher = await rag_searcher
        snippets = [
            FileSnippet(path='test1.py', content='content1', row_start=1, row_end=5),
            FileSnippet(path='test2.py', content='content2', row_start=10, row_end=15),
        ]

        result = searcher.convert_snippets_to_prompt(snippets, 'CUSTOM_SPAN')

        assert 'CUSTOM_SPAN' in result
        assert 'test1.py' in result
        assert 'test2.py' in result
        assert 'content1' in result
        assert 'content2' in result
        assert 'index="0"' in result
        assert 'index="1"' in result

    @pytest.mark.asyncio
    async def test_parse_queries_with_multiple_queries(self, rag_searcher):
        """测试parse_queries方法解析多个查询"""
        searcher = await rag_searcher
        res = """
        <query>first query</query>
        Some text
        <query>second query</query>
        <query>   third query with spaces   </query>
        <query></query>
        """

        queries = searcher.parse_queries(res)

        assert len(queries) == 3
        assert queries[0] == 'first query'
        assert queries[1] == 'second query'
        assert queries[2] == 'third query with spaces'

    @pytest.mark.asyncio
    async def test_parse_queries_with_empty_queries(self, rag_searcher):
        """测试parse_queries方法处理空查询"""
        searcher = await rag_searcher
        res = """
        <query></query>
        <query>   </query>
        <query>valid query</query>
        """

        queries = searcher.parse_queries(res)

        assert len(queries) == 1
        assert queries[0] == 'valid query'

    @pytest.mark.asyncio
    async def test_expand_queries(self, rag_searcher):
        """测试expand_queries方法"""
        searcher = await rag_searcher
        llm_response = """
        <query>expanded query 1</query>
        <query>expanded query 2</query>
        """

        with patch.object(searcher.llm, 'async_send', new_callable=AsyncMock) as mock_send:
            mock_send.return_value = llm_response

            result = await searcher.expand_queries('original query')

            assert len(result) == 3  # original + 2 expanded
            assert result[0] == 'original query'  # 原始查询应该在首位
            assert 'expanded query 1' in result
            assert 'expanded query 2' in result
            mock_send.assert_called_once()

    @pytest.mark.asyncio
    async def test_parse_queries_with_multiline_content(self, rag_searcher):
        """测试parse_queries方法处理多行内容"""
        searcher = await rag_searcher
        res = """
        <query>
        This is a multiline
        query with
        multiple lines
        </query>
        """

        queries = searcher.parse_queries(res)

        assert len(queries) == 1
        expected = 'This is a multiline\n        query with\n        multiple lines'
        assert queries[0] == expected

    @pytest.mark.asyncio
    async def test_search_with_iterable_but_empty_result(self, rag_searcher):
        """测试search方法返回空的可迭代结果"""
        searcher = await rag_searcher

        with patch.object(searcher.playground, 'func_call', new_callable=AsyncMock) as mock_func_call:
            mock_func_call.return_value = []

            with patch.object(searcher, 'expand_queries', new_callable=AsyncMock) as mock_expand:
                mock_expand.return_value = ['test_query']

                result = await searcher.search('test_query', need_expand=True)

                assert result == []

    @pytest.mark.asyncio
    async def test_convert_snippets_to_prompt_empty_list(self, rag_searcher):
        """测试convert_snippets_to_prompt方法处理空列表"""
        searcher = await rag_searcher
        result = searcher.convert_snippets_to_prompt([], 'EMPTY_SPAN')

        assert result == ''

    @pytest.mark.asyncio
    async def test_parse_queries_no_matches(self, rag_searcher):
        """测试parse_queries方法没有匹配项"""
        searcher = await rag_searcher
        res = 'No query tags here'

        queries = searcher.parse_queries(res)

        assert queries == []
