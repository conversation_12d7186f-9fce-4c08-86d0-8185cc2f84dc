import unittest
from unittest.mock import patch
import jwt
from heracles.core.utils.auth import verify_jwt_token

class TestAuth(unittest.TestCase):

    @patch('heracles.core.utils.auth.get_env_var')
    def test_debug_mode_enabled(self, mock_get_env_var):
        # 模拟调试模式启用
        mock_get_env_var.return_value = 'true'

        # 验证在调试模式下，任何token（包括None）都会返回True
        self.assertTrue(verify_jwt_token(None))
        self.assertTrue(verify_jwt_token("invalid_token"))

    @patch('heracles.core.utils.auth.get_env_var')
    def test_token_is_none(self, mock_get_env_var):
        # 模拟非调试模式
        mock_get_env_var.side_effect = lambda key, default=None: 'false' if key == 'IDE_SERVER_DEBUG_OPTIONAL' else ''

        # 验证当token为None时返回False
        self.assertFalse(verify_jwt_token(None))

    @patch('heracles.core.utils.auth.get_env_var')
    def test_jwt_key_secret_not_set(self, mock_get_env_var):
        # 模拟JWT密钥未设置
        mock_get_env_var.side_effect = lambda key, default=None: 'false' if key == 'IDE_SERVER_DEBUG_OPTIONAL' else ''

        # 验证当JWT密钥未设置时返回False
        self.assertFalse(verify_jwt_token("some_token"))

    @patch('heracles.core.utils.auth.get_env_var')
    @patch('jwt.decode')
    def test_valid_token(self, mock_jwt_decode, mock_get_env_var):
        # 模拟JWT密钥已设置
        mock_get_env_var.side_effect = lambda key, default=None: {
            'IDE_SERVER_DEBUG_OPTIONAL': 'false',
            'AUTH_JWT_KEY_OPTIONAL': 'test_key',
            'AUTH_JWT_SECRET_OPTIONAL': 'test_secret'
        }.get(key, '')

        # 模拟成功解码
        mock_jwt_decode.return_value = {"sub": "test"}

        # 验证有效token返回True
        self.assertTrue(verify_jwt_token("valid_token"))

        # 验证jwt.decode被正确调用
        mock_jwt_decode.assert_called_once_with(
            "valid_token",
            "test_secret",
            algorithms=["HS256"],
            options={"verify_signature": True}
        )

    @patch('heracles.core.utils.auth.get_env_var')
    @patch('jwt.decode')
    def test_expired_token(self, mock_jwt_decode, mock_get_env_var):
        # 模拟JWT密钥已设置
        mock_get_env_var.side_effect = lambda key, default=None: {
            'IDE_SERVER_DEBUG_OPTIONAL': 'false',
            'AUTH_JWT_KEY_OPTIONAL': 'test_key',
            'AUTH_JWT_SECRET_OPTIONAL': 'test_secret'
        }.get(key, '')

        # 模拟过期token
        mock_jwt_decode.side_effect = jwt.ExpiredSignatureError()

        # 验证过期token返回False
        self.assertFalse(verify_jwt_token("expired_token"))

    @patch('heracles.core.utils.auth.get_env_var')
    @patch('jwt.decode')
    def test_invalid_token(self, mock_jwt_decode, mock_get_env_var):
        # 模拟JWT密钥已设置
        mock_get_env_var.side_effect = lambda key, default=None: {
            'IDE_SERVER_DEBUG_OPTIONAL': 'false',
            'AUTH_JWT_KEY_OPTIONAL': 'test_key',
            'AUTH_JWT_SECRET_OPTIONAL': 'test_secret'
        }.get(key, '')

        # 模拟无效token
        mock_jwt_decode.side_effect = jwt.InvalidTokenError()

        # 验证无效token返回False
        self.assertFalse(verify_jwt_token("invalid_token"))

    @patch('heracles.core.utils.auth.get_env_var')
    @patch('jwt.decode')
    def test_unexpected_exception(self, mock_jwt_decode, mock_get_env_var):
        # 模拟JWT密钥已设置
        mock_get_env_var.side_effect = lambda key, default=None: {
            'IDE_SERVER_DEBUG_OPTIONAL': 'false',
            'AUTH_JWT_KEY_OPTIONAL': 'test_key',
            'AUTH_JWT_SECRET_OPTIONAL': 'test_secret'
        }.get(key, '')

        # 模拟意外异常
        mock_jwt_decode.side_effect = Exception("Unexpected error")

        # 验证发生异常时返回False
        self.assertFalse(verify_jwt_token("problem_token"))

if __name__ == '__main__':
    unittest.main()
