import pytest
import redis
import asyncio
from typing import Awaitable, Optional, Iterable, Any
from pydantic import BaseModel
from datetime import datetime
import litellm
from fastembed import SparseTextEmbedding
import aiohttp

from heracles.agent_roles.analyze_role.utils import find_match_middlewares, find_best_match_env
from heracles.agent_workspace.paas_sdk.utils import bind_middleware_to_codezone, get_ticket, delete_codezone, \
    create_codezone, git_clone, bind_playground_info, TicketInfo, upgrade_codezone_environment, import_repository, \
    import_repository_task_check, fork_codezone, ImportRepositoryInfo, get_environments, get_template_list, \
    get_thread_template_list, import_codezone_file, get_playground_info, list_middlewares_of_codezone, \
    get_paas_middlewares, generate_token
from heracles.core.schema import ErrorHandlerMessageType
from heracles.core.exceptions import AgentLLMJ<PERSON>NException, AgentRunException
from heracles.core.utils.string import camelize, underscore, remove_ansi_escape_sequences, split_url
from heracles.core.utils.merge_dict import merge_dict
from heracles.core.utils.llm_json import loads, dumps
from heracles.core.utils.llm_function import llm_function_to_json, response_model_to_llm_tool_schema, response_model_to_json_schema
from heracles.core.logger import heracles_logger as logger
from heracles.core.logger import get_playground_logger
from heracles.core.config import get_env_var
from heracles.core.utils import add_asyncio_handler, cancel_asyncio_handler, wait_for, mock_sleep, delayed_debouncer
from heracles.core.utils.redis_cache import connect_to_redis, CacheNone
from heracles.core.utils.admin_utils import create_dense_embedding, create_sparse_embedding, playbook_to_point, record_to_playbook
from heracles.core.schema import AdminPlaybookModel
from heracles.agent_roles.utils import sanitize_generated_text


def test_logger_max_length(mocker):
    msg = 'x' * 2050
    logger.debug(msg)
    mocker.patch.dict('os.environ', {'LOG_LEVEL_OPTIONAL': 'wrongvalue'})
    new_logger = get_playground_logger('123')
    new_logger.info('you can see it')

def test_get_env_var(mocker):
    noexist = 'noexist'
    with pytest.raises(EnvironmentError):
        get_env_var(noexist, must=True)
    mocker.patch.dict('os.environ', {'V1': 'v1'})
    assert get_env_var('V1') == 'v1'
    assert get_env_var('defaultvalue', '123') == '123'
    assert get_env_var('defaultvalue') is None

def test_generate_token():
    nonce = '123456'
    timestamp = '1730880381085'
    token = generate_token(nonce, timestamp)
    assert token == 'erWq9f/ryvauGN3K5V/7uGIoQVYYg03KBn8DbCkDCs4='

def test_camelize_and_underscore():
    assert camelize("device_type") == "DeviceType"
    assert camelize("device_type", uppercase_first_letter=False) == "deviceType"
    assert underscore("DeviceType") == "device_type"

def test_remove_ansi_escape_sequences():
    assert remove_ansi_escape_sequences("abc") == "abc"
    assert remove_ansi_escape_sequences("\x1B[aabc") == "abc"
    assert remove_ansi_escape_sequences("\r\x1B[aabc") == "abc"
    s = '\r\x1b[K\r\x1b[0;32m➜ \x1b[00mapp\x1b[32m (setup-environment)\x1b[00m $ '
    assert remove_ansi_escape_sequences(s) == '➜ app (setup-environment) $ '
    s = 'touch 123 #AI \r\x1b[A'
    assert remove_ansi_escape_sequences(s) == 'touch 123 #AI '

def test_split_url():
    url = "ws://localhost:3000/hello"
    domain, path = split_url(url)
    assert domain == "ws://localhost:3000"
    assert path == "/hello"

def test_merge_dict():
    a = {
        "k1": "v1",
        "k2": "v2",
    }
    b = {
        "k1": None,
        "k2": "vb2",
        "k3": "v3"
    }
    c = merge_dict(a, b)
    assert c.get('k1') == "v1"
    assert c.get('k2') == "vb2"
    assert c.get('k3') == "v3"

def test_my_default_encoder():
    class CustomObject:
        pass
    obj = CustomObject()
    with pytest.raises(TypeError):
        dumps({"custom": obj})

def test_loads_dumps():
    a = {
        "k1": "v1",
        "k2": "v2",
    }

    s = dumps(a)
    # simple test
    assert loads(s) == a

    h_with_time = {
        "k1": "v1",
        "k2": datetime.now()
    }

    # no raise error
    dumps(h_with_time)

    s_wrong1 = """======== info
    { 'a': 1, 'b': 2, 'c': 3 }
    ===="""

    h = loads(s_wrong1)
    assert h["a"] == 1

    s_wrong2 = """
    { 'a': Date.now(), 'b': 2, 'c': 3 }
    """
    h = loads(s_wrong2)
    assert h["b"] == 2

    s_wrong3 = """
    { 'a': Date.now(), 'b': null, 'c': 3 }
    """
    h = loads(s_wrong3)
    assert h["b"] is None

    s_wrong4 = """
    { 'a': Date.now(), 'b': null, 'c': 3
    """
    with pytest.raises(AgentLLMJSONException):
        h = loads(s_wrong4)

def test_llm_function_to_json():
    def function1(a, b):
        """ function1
        :param a: aa
        :param b: bb

        :return
        """

    h = llm_function_to_json(function1)
    assert h['function']['name'] == 'function1'

    class TestBaseModel(BaseModel):
        a: str

    h = response_model_to_llm_tool_schema(TestBaseModel)
    assert h['function']['name'] == 'TestBaseModel'

def test_llm_function_to_json_2():
    class TestModel(BaseModel):
        name: str
        age: int
        description: Optional[str] = None

    schema = response_model_to_json_schema(Iterable[TestModel])

    assert isinstance(schema, dict)
    assert "properties" in schema
    assert "name" in schema["properties"]
    assert "age" in schema["properties"]

    tool_schema = response_model_to_llm_tool_schema(Iterable[TestModel])
    assert isinstance(tool_schema, dict)
    assert tool_schema["type"] == "function"
    assert "function" in tool_schema
    assert tool_schema["function"]["name"] == "IterableTestModel"
    assert "parameters" in tool_schema["function"]
    assert "properties" in tool_schema["function"]["parameters"]
    assert "items" in tool_schema["function"]["parameters"]["properties"]
    assert "type" in tool_schema["function"]["parameters"]["properties"]["items"]
    assert tool_schema["function"]["parameters"]["properties"]["items"]["type"] == "array"

@pytest.mark.asyncio
async def test_asyncio_handler():

    class TestHandlerClass:
        def __init__(self):
            self.handler: Optional[Awaitable] = None

    async def result_callback(msg):
        print('result_callback: {msg}')
        return msg

    async def trigger_callback(msg):
        if isinstance(msg, str):
            raise AgentRunException(msg)
        raise AgentRunException(msg.content)

    async def handler_task():
        return 'done'

    async def handler_task_error():
        raise ValueError("xxx")

    async def handler_task_validation_error():
        class TestModel(BaseModel):
            name: str
            age: int

        # Trigger validation error by passing wrong types
        TestModel(name=123, age="invalid")  # type: ignore
        return "done"

    a = TestHandlerClass()
    # 错误 handler_name
    with pytest.raises(AgentRunException, match="add_asyncio_handler error: `TestHandlerClass.wrong_handler_name` is None"):
        add_asyncio_handler(a, 'wrong_handler_name', handler_task(), trigger_callback, ErrorHandlerMessageType.DEFAULT, logger)
    # 正常
    add_asyncio_handler(a, 'handler', handler_task(), trigger_callback, ErrorHandlerMessageType.DEFAULT, logger)
    assert a.handler is not None
    # 重复
    with pytest.raises(AgentRunException, match="Another task `TestHandlerClass.handler` is running, please wait and try again later."): # noqa: E501
        add_asyncio_handler(a, 'handler', handler_task(), trigger_callback, ErrorHandlerMessageType.DEFAULT, logger)
    cancel_asyncio_handler(a, 'handler')

    with pytest.raises(AgentRunException, match="asyncio_handler `handler` error, xxx"):
        add_asyncio_handler(a, 'handler', handler_task_error(), trigger_callback, ErrorHandlerMessageType.DEFAULT, logger)
        await a.handler

    add_asyncio_handler(a, 'handler', handler_task(), trigger_callback, ErrorHandlerMessageType.DEFAULT, logger, result_callback)
    assert await a.handler == 'done'

    with pytest.raises(AgentRunException, match="- name: Input should be a valid string\n- age: Input should be a valid integer, unable to parse string as an integer"):  # noqa: E501
        add_asyncio_handler(a, 'handler', handler_task_validation_error(), trigger_callback, ErrorHandlerMessageType.DEFAULT, logger)
        await a.handler

@pytest.mark.asyncio
async def test_delayed_debouncer(mocker):
    mock_sleep(mocker)

    class TestHandlerClass:
        def __init__(self):
            self.sum = 0
            self.handler = None

        @delayed_debouncer(delay=5, task_handle_name="handler", error_callback_name="error_callback")
        async def handler_task(self):
            self.sum += 1
            return self.sum

        @delayed_debouncer(delay=5, task_handle_name="non_exist_handler", error_callback_name="error_callback")
        async def handler_task_2(self):
            raise ValueError("xxx")

        @delayed_debouncer(delay=5, task_handle_name="handler", error_callback_name="error_callback")
        async def handler_task_error(self):
            raise ValueError("xxx")

        async def error_callback(self, msg):
            self.sum = -1

    a = TestHandlerClass()
    for _ in range(10):
        await a.handler_task()
        await asyncio.sleep(0.1)
    await asyncio.sleep(10)
    assert a.sum == 1

    with pytest.raises(AgentRunException):
        await a.handler_task_2()
        await asyncio.sleep(10)

    await a.handler_task_error()
    await asyncio.sleep(10)
    assert a.sum == -1

@pytest.mark.asyncio
async def test_wait_for(mocker):

    def ok():
        return True

    def fail():
        return False

    mock_sleep(mocker)
    await wait_for(ok)

    with pytest.raises(AgentRunException):
        await wait_for(fail)

def test_redis_cache_none(mocker):
    mocker.patch.dict('os.environ', {'CACHE_REDIS_HOST_OPTIONAL': ''})
    cache = connect_to_redis()
    assert isinstance(cache, CacheNone)

def test_redis_cache_connect(mocker):
    mocker.patch.dict(
        'os.environ',
        {
            'CACHE_REDIS_HOST_OPTIONAL': 'localhost',
            'CACHE_REDIS_PORT_OPTIONAL': '22222',
            'CACHE_REDIS_DB_OPTIONAL': '0',
        },
    )
    with pytest.raises(redis.exceptions.ConnectionError):
        connect_to_redis()

def test_create_dense_embedding(mocker):
    mock_res = litellm.EmbeddingResponse.model_validate({'model': 'test', 'data': [{'embedding': [0, 0, 1]}]})
    mocker.patch.object(litellm, 'embedding', return_value=mock_res)
    assert create_dense_embedding('test')


def test_create_sparse_embedding(mocker):
    def mock_init(self):
        pass

    mocker.patch.object(SparseTextEmbedding, '__init__', mock_init)
    mocker.patch.object(SparseTextEmbedding, 'embed', return_value=iter([1, 2, 3]))
    assert create_sparse_embedding('test')


def test_record_to_playbook(mocker):
    class Record(BaseModel):
        id: str
        payload: Any

    record = Record(
        id='1',
        payload={
            'id': '9bbf91ea-043b-44d9-a043-fa5a615cb110',
            'title': 'title',
            'description': '',
            'tags': ['initialization'],
            'original_content': 'original_content',
            'source': 'user',
            'status': 'ready',
        },
    )
    assert record_to_playbook(record)


def test_playbook_to_point(mocker):
    playbook_dict = {
        'id': '9bbf91ea-043b-44d9-a043-fa5a615cb110',
        'title': 'title',
        'description': '',
        'tags': ['initialization'],
        'original_content': 'original_content',
        'source': 'user',
        'status': 'ready',
    }
    playbook = AdminPlaybookModel.model_validate(playbook_dict)
    assert playbook_to_point(playbook)

def test_sanitize_generated_text():
    content = "Hello <think>some thoughts</think> World"
    assert sanitize_generated_text(content) == "Hello  World"

    content = "```\nHello World"
    assert sanitize_generated_text(content) == "Hello World"

    content = "Hello World\n```"
    assert sanitize_generated_text(content) == "Hello World"

    content = "```\n\nHello World\n\n```"
    assert sanitize_generated_text(content) == "\nHello World\n"

    content = "```\nHello <think>thoughts</think> World\n```"
    assert sanitize_generated_text(content) == "Hello  World"

    content = "```\n\nHello <think>thoughts</think> World\n\n```"
    assert sanitize_generated_text(content) == "\nHello  World\n"

    assert sanitize_generated_text("") == ""

    content = "Hello World"
    assert sanitize_generated_text(content) == "Hello World"

    content = "\nHello World\n"
    assert sanitize_generated_text(content) == "\nHello World\n"

    content = "```\n\nLine 1\nLine 2<think>thoughts</think>\nLine 3\n\n```"
    assert sanitize_generated_text(content) == "\nLine 1\nLine 2\nLine 3\n"

@pytest.mark.asyncio
async def test_upload_files(mocker, tmp_path):
    """测试文件上传功能"""
    # 创建临时测试文件
    test_file = tmp_path / "test.zip"
    test_file.write_bytes(b"test content")

    async def mock_fetch(*args, **kwargs):
        return {'success': True, 'data': {'result': 'ok'}, 'error': None}

    mocker.patch('heracles.agent_workspace.paas_sdk.utils.fetch', side_effect=mock_fetch)

    from heracles.agent_workspace.paas_sdk.utils import upload_files
    result = await upload_files('zone1', {'file1': str(test_file)})
    assert result['success'] is True

@pytest.mark.asyncio
async def test_fetch_responses(mocker):
    """测试不同HTTP状态码的响应处理"""
    class MockResponse:
        def __init__(self, status, json_data):
            self.status = status
            self._json_data = json_data

        async def json(self):
            return self._json_data

        async def __aenter__(self):
            return self

        async def __aexit__(self, *args):
            pass

    class MockClientSession:
        async def __aenter__(self):
            return self

        async def __aexit__(self, *args):
            pass

        def request(self, *args, **kwargs):
            # 模拟不同状态码的响应
            if args[1].endswith('/success'):
                return MockResponse(200, {'data': {'result': 'ok'}})
            elif args[1].endswith('/created'):
                return MockResponse(201, {'data': {'result': 'created'}})
            elif args[1].endswith('/unauthorized'):
                return MockResponse(401, {})
            elif args[1].endswith('/forbidden'):
                return MockResponse(403, {})
            elif args[1].endswith('/not_found'):
                return MockResponse(404, {})
            elif args[1].endswith('/timeout'):
                return MockResponse(504, {})
            else:
                return MockResponse(500, {})

    mocker.patch('aiohttp.ClientSession', return_value=MockClientSession())

    from heracles.agent_workspace.paas_sdk.utils import fetch
    async with aiohttp.ClientSession() as session:
        # 测试成功响应
        result = await fetch(session, '/success')
        assert result['success'] is True
        assert result['data']['result'] == 'ok'

        # 测试201响应
        result = await fetch(session, '/created')
        assert result['success'] is True
        assert result['data']['result'] == 'created'

        # 测试401响应
        result = await fetch(session, '/unauthorized')
        assert result['success'] is False
        assert result['error'] == "Unauthorized: Check your token and tenant code."

        # 测试403响应
        result = await fetch(session, '/forbidden')
        assert result['success'] is False
        assert result['error'] == "Forbidden: You don't have permission to perform this action."

        # 测试404响应
        result = await fetch(session, '/not_found')
        assert result['success'] is False
        assert result['error'] == "Not Found: The specified resource does not exist."

        # 测试504响应
        result = await fetch(session, '/timeout')
        assert result['success'] is False
        assert result['error'] == "Gateway Timeout: The server is taking too long to respond."

        # 测试其他错误响应
        result = await fetch(session, '/error')
        assert result['success'] is False
        assert result['error'] == "Unexpected status code: 500"

@pytest.mark.asyncio
async def test_api_functions(mocker):
    """测试各种API调用函数"""
    class MockResponse:
        def __init__(self, status, json_data):
            self.status = status
            self._json_data = json_data

        async def json(self):
            return self._json_data

        async def __aenter__(self):
            return self

        async def __aexit__(self, *args):
            pass

    class MockClientSession:
        async def __aenter__(self):
            return self

        async def __aexit__(self, *args):
            pass

        def request(self, *args, **kwargs):
            return MockResponse(200, {'data': {'result': 'ok'}})

    mocker.patch('aiohttp.ClientSession', return_value=MockClientSession())


    # 测试各个API函数
    assert (await get_environments())['success'] is True
    assert (await get_template_list())['success'] is True
    assert (await get_thread_template_list())['success'] is True

    repository_info = ImportRepositoryInfo(
        environmentVerId='test',
        owner='test',
        repo='test',
        ref='main',
        username='test',
        purpose='test',
        token='test',
        privateKey='test'
    )
    assert (await import_repository(repository_info))['success'] is True
    assert (await import_repository_task_check('task-id'))['success'] is True
    assert (await fork_codezone('zone-id'))['success'] is True
    assert (await upgrade_codezone_environment('zone-id', 'env-ver'))['success'] is True

    ticket_info = TicketInfo(
        ticketId='test',
        tenantCode='test'
    )
    assert (await get_ticket(ticket_info))['success'] is True
    assert (await delete_codezone('zone-id'))['success'] is True
    assert (await create_codezone('env-ver-id'))['success'] is True
    assert (await git_clone('env-ver-id', 'owner', 'username', 'repo'))['success'] is True
    assert (await bind_playground_info('playground-id'))['success'] is True
    assert (await import_codezone_file('zone-id', 'test.py', 'content'))['success'] is True
    assert (await get_playground_info('playground-id'))['success'] is True
    assert (await list_middlewares_of_codezone(123))['success'] is True
    assert (await get_paas_middlewares())['success'] is True

    # 测试bind_middleware_to_codezone
    class MockMiddlewareClientSession:
        async def __aenter__(self):
            return self

        async def __aexit__(self, *args):
            pass

        def request(self, *args, **kwargs):
            if 'middlewares' in args[1]:
                return MockResponse(200, {'data': [{'id': 1, 'name': 'test-middleware'}]})
            return MockResponse(200, {'data': {'result': 'ok'}})

    mocker.patch('aiohttp.ClientSession', return_value=MockMiddlewareClientSession())
    result = await bind_middleware_to_codezone(123, 'test-middleware')
    assert result['success'] is True


@pytest.mark.asyncio
async def test_find_best_environment(mocker):
    environments = [
        {
            'name': 'test-environment',
            'versionList': [
                {
                    'id': '1',
                    'name': 'test-version',
                    'score': 1
                }
            ],
            'score': 1
        },
        {
            'name': 'test-environment-2',
            'versionList': [
                {
                    'id': '2',
                    'name': 'test-version-2',
                    'score': 0
                },
            ],
            'score': 0
        }
    ]
    best_environment = find_best_match_env(environments)
    assert isinstance(best_environment, dict)
    assert best_environment['id'] == '1'
    assert best_environment['name'] == 'test-version'
    assert best_environment['score'] == 1

@pytest.mark.asyncio
async def test_find_match_middlewares(mocker):
    middlewares = [
        {
            'id': '1',
            'name': 'test-middleware',
            'score': 1
        },
        {
            'id': '2',
            'name': 'test-middleware-2',
            'score': 0
        },
        {
            'id': '3',
            'name': 'test-middleware-3',
            'score': 1
        },
    ]
    match_middlewares = find_match_middlewares(middlewares)
    assert all(item in match_middlewares for item in [middlewares[0], middlewares[2]])
    assert middlewares[1] not in match_middlewares
