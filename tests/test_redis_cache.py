import pytest
from heracles.core.utils.redis_cache import CacheNone

@pytest.fixture
def cache_none():
    return CacheNone()

def test_get_method(cache_none):
    # 测试get方法总是返回None
    assert cache_none.get("any_key") is None

def test_set_method(cache_none):
    # 测试set方法可以正常调用且不抛出异常
    cache_none.set("test_key", "test_value")
    assert cache_none.cache_dict["test_key"] == "test_value"

def test_delete_method(cache_none):
    # 测试delete方法
    cache_none.cache_dict["test_key"] = "test_value"
    cache_none.delete("test_key")
    assert "test_key" not in cache_none.cache_dict
    # 测试删除不存在的键不会抛出异常
    cache_none.delete("non_existent_key")

def test_keys_method(cache_none):
    # 准备测试数据
    cache_none.cache_dict.update({
        "test1": "value1",
        "test2": "value2",
        "other": "value3"
    })

    # 测试通配符匹配
    assert set(cache_none.keys("test*")) == {"test1", "test2"}
    assert set(cache_none.keys("*est*")) == {"test1", "test2"}
    assert set(cache_none.keys("other")) == {"other"}
    assert set(cache_none.keys("non*")) == set()

def test_scan_iter_method(cache_none):
    # 准备测试数据
    test_keys = {"test1", "test2", "test3"}
    for key in test_keys:
        cache_none.cache_dict[key] = "value"

    # 测试scan_iter的结果
    result_keys = set(cache_none.scan_iter(match="test*"))
    assert result_keys == test_keys

    # 测试不匹配的模式
    assert set(cache_none.scan_iter(match="other*")) == set()
