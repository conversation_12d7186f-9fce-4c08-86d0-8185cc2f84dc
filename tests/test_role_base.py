import pytest
import json

from litellm import ChatCompletionMessageToolCall
from heracles.agent_controller.llm import LLM
from heracles.core.schema import LLMAbilityType, ToolCalls
from heracles.core.schema.models.task_model import TaskModel
from heracles.agent_roles.role_base import RoleBase
from heracles.agent_roles.plan_role import PlanRole
from heracles.core.exceptions import AgentRunException

task_json = json.dumps({
    "title": "test task",
    "description": "test description",
    "task_steps": [{
        "title": "为首页菜谱标题添加闪动广告牌效果",
        "task_actions": [
            {
                "action": "modify_file",
                "path": "path/to/your/file1",
                "detailed_requirement": "在首页的菜谱标题上增加闪动效果，添加包含标题的div，并为其添加类名\"sticker\"。",
                "references": []
            }
        ]
    }]}
)

class TestRole(RoleBase):
    async def run(self):
        return

    def get_llm_ability(self):
        return LLMAbilityType.NORMAL

@pytest.mark.asyncio
async def test_role_base_langfuse_id(create_blank_playground):
    p1 = await create_blank_playground('1')
    r1 = TestRole(p1.agent_controller.workspace)
    assert r1.llm.langfuse_option.trace_id == '1'

    p2 = await create_blank_playground('2')
    r2 = TestRole(p2.agent_controller.workspace)
    assert r1.llm.langfuse_option.trace_id == '1'
    assert r2.llm.langfuse_option.trace_id == '2'


@pytest.mark.asyncio
async def test_role_base_tool_callback(create_workspace, mocker):
    workspace = await create_workspace
    plan_role = PlanRole(workspace)

    # Mock LLM response for tool callback
    async def mock_async_multiple_tool_and_response_model(*args, **kwargs):
        tool_call = ChatCompletionMessageToolCall(function=dict(name="read_playbook", arguments='{"id": "Handling .gitignore File"}'))
        response_model = ChatCompletionMessageToolCall(function=dict(name="TaskModel", arguments='{"title": "initial fail task"}'))

        if kwargs.get('tools') and any(tool['function']['name'] == 'read_playbook' for tool in kwargs['tools']) and\
                any(tool['function']['name'] == 'TaskModel' for tool in kwargs['tools']):
            return ToolCalls(functions=[tool_call, response_model], message="")
        elif kwargs.get('tools') and any(tool['function']['name'] == 'read_playbook' for tool in kwargs['tools']):
            return ToolCalls(functions=[tool_call], message="")
        elif kwargs.get('tools') and any(tool['function']['name'] == 'TaskModel' for tool in kwargs['tools']):
            return ToolCalls(functions=[response_model], message="")
        else:
            return json.dumps({
                "title": "fixed failed task"
            })

    mocker.patch.object(LLM, 'async_send', side_effect=mock_async_multiple_tool_and_response_model)
    with pytest.raises(AgentRunException, match="LLM model validation error and reached max retries"):
        await plan_role.aask("Test message", tools=['read_playbook'], response_model=TaskModel)

    mocker.patch.object(plan_role, 'validate_model_with_retry', return_value=task_json)
    await plan_role.aask("Test message", tools=['read_playbook'], response_model=TaskModel)

@pytest.mark.asyncio
async def test_role_base_validate_model_success(create_workspace, mocker):
    workspace = await create_workspace
    plan_role = PlanRole(workspace)

    # Mock LLM response for validate fix model
    async def mock_async_validate_fix_model(*args, **kwargs):
        return task_json
    mocker.patch.object(LLM, 'async_send', side_effect=mock_async_validate_fix_model)
    fixed_task_model = await plan_role.validate_model_with_retry(TaskModel, json.dumps({
        "title": "test task"
    }))
    assert fixed_task_model.task_steps[0].title == "为首页菜谱标题添加闪动广告牌效果"

@pytest.mark.asyncio
async def test_role_base_merge_multiple_response_models(create_workspace, mocker):
    workspace = await create_workspace
    plan_role = PlanRole(workspace)

    # Mock LLM response for merging models
    async def mock_async_merge_models(*args, **kwargs):
        return json.dumps({
            "title": "merged task",
            "description": "merged from multiple models",
            "task_steps": [{
                "title": "Merged step",
                "task_actions": [
                    {
                        "action": "modify_file",
                        "path": "path/to/file",
                        "detailed_requirement": "Merged action requirement",
                        "references": []
                    }
                ]
            }]
        })

    mocker.patch.object(LLM, 'async_send', side_effect=mock_async_merge_models)

    # Create multiple model instances to merge
    model1 = ChatCompletionMessageToolCall(
        function=dict(
            name="TaskModel",
            arguments=json.dumps({
                "title": "task 1",
                "description": "first task"
            })
        )
    )

    model2 = ChatCompletionMessageToolCall(
        function=dict(
            name="TaskModel",
            arguments=json.dumps({
                "title": "task 2",
                "description": "second task"
            })
        )
    )

    models = [model1, model2]
    merged_json = await plan_role.merge_multiple_response_models(models, TaskModel)

    merged_model = TaskModel(**json.loads(merged_json))
    assert merged_model.title == "merged task"
    assert merged_model.description == "merged from multiple models"
    assert len(merged_model.task_steps) == 1
    assert merged_model.task_steps[0].title == "Merged step"
