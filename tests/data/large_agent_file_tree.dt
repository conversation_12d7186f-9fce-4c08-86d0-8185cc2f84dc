{'type': 'DIRECTORY', 'name': '.', 'path': '.', 'children': [{'type': 'FILE', 'name': '.1024', 'path': '.1024', 'children': []}, {'type': 'FILE', 'name': '.1024nix', 'path': '.1024nix', 'children': []}, {'type': 'FILE', 'name': '.env', 'path': '.env', 'children': []}, {'type': 'FILE', 'name': '.env.example', 'path': '.env.example', 'children': []}, {'type': 'FILE', 'name': '.eslintrc.json', 'path': '.eslintrc.json', 'children': []}, {'type': 'FILE', 'name': 'README.md', 'path': 'README.md', 'children': []}, {'type': 'DIRECTORY', 'name': 'app', 'path': 'app', 'children': [{'type': 'DIRECTORY', 'name': 'api', 'path': 'app/api', 'children': [{'type': 'DIRECTORY', 'name': 'chat', 'path': 'app/api/chat', 'children': [{'type': 'FILE', 'name': 'route.ts', 'path': 'app/api/chat/route.ts', 'children': []}]}]}, {'type': 'FILE', 'name': 'favicon.ico', 'path': 'app/favicon.ico', 'children': []}, {'type': 'FILE', 'name': 'globals.css', 'path': 'app/globals.css', 'children': []}, {'type': 'DIRECTORY', 'name': 'hooks', 'path': 'app/hooks', 'children': [{'type': 'FILE', 'name': 'useConfiguration.ts', 'path': 'app/hooks/useConfiguration.ts', 'children': []}]}, {'type': 'FILE', 'name': 'i18n.ts', 'path': 'app/i18n.ts', 'children': []}, {'type': 'FILE', 'name': 'layout.tsx', 'path': 'app/layout.tsx', 'children': []}, {'type': 'FILE', 'name': 'page.tsx', 'path': 'app/page.tsx', 'children': []}]}, {'type': 'DIRECTORY', 'name': 'components', 'path': 'components', 'children': [{'type': 'FILE', 'name': 'Bubble.tsx', 'path': 'components/Bubble.tsx', 'children': []}, {'type': 'FILE', 'name': 'Configure.tsx', 'path': 'components/Configure.tsx', 'children': []}, {'type': 'FILE', 'name': 'Dropdown.tsx', 'path': 'components/Dropdown.tsx', 'children': []}, {'type': 'FILE', 'name': 'Footer.tsx', 'path': 'components/Footer.tsx', 'children': []}, {'type': 'DIRECTORY', 'name': 'PromptSuggestions', 'path': 'components/PromptSuggestions', 'children': [{'type': 'FILE', 'name': 'PromptSuggestionButton.tsx', 'path': 'components/PromptSuggestions/PromptSuggestionButton.tsx', 'children': []}, {'type': 'FILE', 'name': 'PromptSuggestionsRow.tsx', 'path': 'components/PromptSuggestions/PromptSuggestionsRow.tsx', 'children': []}]}, {'type': 'FILE', 'name': 'ThemeButton.tsx', 'path': 'components/ThemeButton.tsx', 'children': []}, {'type': 'FILE', 'name': 'Toggle.tsx', 'path': 'components/Toggle.tsx', 'children': []}]}, {'type': 'FILE', 'name': 'next-env.d.ts', 'path': 'next-env.d.ts', 'children': []}, {'type': 'FILE', 'name': 'next.config.js', 'path': 'next.config.js', 'children': []}, {'type': 'DIRECTORY', 'name': 'node_modules', 'path': 'node_modules', 'children': [{'type': 'DIRECTORY', 'name': '.bin', 'path': 'node_modules/.bin', 'children': [{'type': 'TIPS', 'name': 'Hidden directory not show...', 'path': '', 'children': []}]}, {'type': 'FILE', 'name': '.package-lock.json', 'path': 'node_modules/.package-lock.json', 'children': []}, {'type': 'DIRECTORY', 'name': '@aashutoshrathi', 'path': 'node_modules/@aashutoshrathi', 'children': [{'type': 'DIRECTORY', 'name': 'word-wrap', 'path': 'node_modules/@aashutoshrathi/word-wrap', 'children': [{'type': 'FILE', 'name': 'LICENSE', 'path': 'node_modules/@aashutoshrathi/word-wrap/LICENSE', 'children': []}, {'type': 'FILE', 'name': 'README.md', 'path': 'node_modules/@aashutoshrathi/word-wrap/README.md', 'children': []}, {'type': 'FILE', 'name': 'index.d.ts', 'path': 'node_modules/@aashutoshrathi/word-wrap/index.d.ts', 'children': []}, {'type': 'FILE', 'name': 'index.js', 'path': 'node_modules/@aashutoshrathi/word-wrap/index.js', 'children': []}, {'type': 'FILE', 'name': 'package.json', 'path': 'node_modules/@aashutoshrathi/word-wrap/package.json', 'children': []}]}]}, {'type': 'DIRECTORY', 'name': '@alloc', 'path': 'node_modules/@alloc', 'children': [{'type': 'DIRECTORY', 'name': 'quick-lru', 'path': 'node_modules/@alloc/quick-lru', 'children': [{'type': 'FILE', 'name': 'index.d.ts', 'path': 'node_modules/@alloc/quick-lru/index.d.ts', 'children': []}, {'type': 'FILE', 'name': 'index.js', 'path': 'node_modules/@alloc/quick-lru/index.js', 'children': []}, {'type': 'FILE', 'name': 'license', 'path': 'node_modules/@alloc/quick-lru/license', 'children': []}, {'type': 'FILE', 'name': 'package.json', 'path': 'node_modules/@alloc/quick-lru/package.json', 'children': []}, {'type': 'FILE', 'name': 'readme.md', 'path': 'node_modules/@alloc/quick-lru/readme.md', 'children': []}]}]}, {'type': 'DIRECTORY', 'name': '@ampproject', 'path': 'node_modules/@ampproject', 'children': [{'type': 'DIRECTORY', 'name': 'remapping', 'path': 'node_modules/@ampproject/remapping', 'children': [{'type': 'FILE', 'name': 'LICENSE', 'path': 'node_modules/@ampproject/remapping/LICENSE', 'children': []}, {'type': 'FILE', 'name': 'README.md', 'path': 'node_modules/@ampproject/remapping/README.md', 'children': []}, {'type': 'DIRECTORY', 'name': 'dist', 'path': 'node_modules/@ampproject/remapping/dist', 'children': [{'type': 'FILE', 'name': 'remapping.mjs', 'path': 'node_modules/@ampproject/remapping/dist/remapping.mjs', 'children': []}, {'type': 'FILE', 'name': 'remapping.mjs.map', 'path': 'node_modules/@ampproject/remapping/dist/remapping.mjs.map', 'children': []}, {'type': 'FILE', 'name': 'remapping.umd.js', 'path': 'node_modules/@ampproject/remapping/dist/remapping.umd.js', 'children': []}, {'type': 'FILE', 'name': 'remapping.umd.js.map', 'path': 'node_modules/@ampproject/remapping/dist/remapping.umd.js.map', 'children': []}, {'type': 'DIRECTORY', 'name': 'types', 'path': 'node_modules/@ampproject/remapping/dist/types', 'children': [{'type': 'TIPS', 'name': 'Max files (1000) reached...', 'path': '', 'children': []}]}]}, {'type': 'FILE', 'name': 'package.json', 'path': 'node_modules/@ampproject/remapping/package.json', 'children': []}]}]}, {'type': 'DIRECTORY', 'name': '@anthropic-ai', 'path': 'node_modules/@anthropic-ai', 'children': [{'type': 'DIRECTORY', 'name': 'sdk', 'path': 'node_modules/@anthropic-ai/sdk', 'children': [{'type': 'FILE', 'name': 'CHANGELOG.md', 'path': 'node_modules/@anthropic-ai/sdk/CHANGELOG.md', 'children': []}, {'type': 'FILE', 'name': 'LICENSE', 'path': 'node_modules/@anthropic-ai/sdk/LICENSE', 'children': []}, {'type': 'FILE', 'name': 'README.md', 'path': 'node_modules/@anthropic-ai/sdk/README.md', 'children': []}, {'type': 'DIRECTORY', 'name': '_shims', 'path': 'node_modules/@anthropic-ai/sdk/_shims', 'children': [{'type': 'FILE', 'name': 'MultipartBody.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/_shims/MultipartBody.d.ts', 'children': []}, {'type': 'FILE', 'name': 'MultipartBody.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/_shims/MultipartBody.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'MultipartBody.js', 'path': 'node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js', 'children': []}, {'type': 'FILE', 'name': 'MultipartBody.js.map', 'path': 'node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js.map', 'children': []}, {'type': 'FILE', 'name': 'MultipartBody.mjs', 'path': 'node_modules/@anthropic-ai/sdk/_shims/MultipartBody.mjs', 'children': []}, {'type': 'FILE', 'name': 'MultipartBody.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/_shims/MultipartBody.mjs.map', 'children': []}, {'type': 'FILE', 'name': 'README.md', 'path': 'node_modules/@anthropic-ai/sdk/_shims/README.md', 'children': []}, {'type': 'DIRECTORY', 'name': 'auto', 'path': 'node_modules/@anthropic-ai/sdk/_shims/auto', 'children': [{'type': 'TIPS', 'name': 'Max files (1000) reached...', 'path': '', 'children': []}]}, {'type': 'FILE', 'name': 'bun-runtime.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/_shims/bun-runtime.d.ts', 'children': []}, {'type': 'FILE', 'name': 'bun-runtime.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/_shims/bun-runtime.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'bun-runtime.js', 'path': 'node_modules/@anthropic-ai/sdk/_shims/bun-runtime.js', 'children': []}, {'type': 'FILE', 'name': 'bun-runtime.js.map', 'path': 'node_modules/@anthropic-ai/sdk/_shims/bun-runtime.js.map', 'children': []}, {'type': 'FILE', 'name': 'bun-runtime.mjs', 'path': 'node_modules/@anthropic-ai/sdk/_shims/bun-runtime.mjs', 'children': []}, {'type': 'FILE', 'name': 'bun-runtime.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/_shims/bun-runtime.mjs.map', 'children': []}, {'type': 'FILE', 'name': 'index.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/_shims/index.d.ts', 'children': []}, {'type': 'FILE', 'name': 'index.js', 'path': 'node_modules/@anthropic-ai/sdk/_shims/index.js', 'children': []}, {'type': 'FILE', 'name': 'index.mjs', 'path': 'node_modules/@anthropic-ai/sdk/_shims/index.mjs', 'children': []}, {'type': 'FILE', 'name': 'manual-types.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/_shims/manual-types.d.ts', 'children': []}, {'type': 'FILE', 'name': 'manual-types.js', 'path': 'node_modules/@anthropic-ai/sdk/_shims/manual-types.js', 'children': []}, {'type': 'FILE', 'name': 'manual-types.mjs', 'path': 'node_modules/@anthropic-ai/sdk/_shims/manual-types.mjs', 'children': []}, {'type': 'FILE', 'name': 'node-runtime.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/_shims/node-runtime.d.ts', 'children': []}, {'type': 'FILE', 'name': 'node-runtime.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/_shims/node-runtime.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'node-runtime.js', 'path': 'node_modules/@anthropic-ai/sdk/_shims/node-runtime.js', 'children': []}, {'type': 'FILE', 'name': 'node-runtime.js.map', 'path': 'node_modules/@anthropic-ai/sdk/_shims/node-runtime.js.map', 'children': []}, {'type': 'FILE', 'name': 'node-runtime.mjs', 'path': 'node_modules/@anthropic-ai/sdk/_shims/node-runtime.mjs', 'children': []}, {'type': 'FILE', 'name': 'node-runtime.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/_shims/node-runtime.mjs.map', 'children': []}, {'type': 'FILE', 'name': 'node-types.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/_shims/node-types.d.ts', 'children': []}, {'type': 'FILE', 'name': 'node-types.js', 'path': 'node_modules/@anthropic-ai/sdk/_shims/node-types.js', 'children': []}, {'type': 'FILE', 'name': 'node-types.mjs', 'path': 'node_modules/@anthropic-ai/sdk/_shims/node-types.mjs', 'children': []}, {'type': 'FILE', 'name': 'registry.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/_shims/registry.d.ts', 'children': []}, {'type': 'TIPS', 'name': 'Max 30 items per level...', 'path': '', 'children': []}]}, {'type': 'FILE', 'name': 'error.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/error.d.ts', 'children': []}, {'type': 'FILE', 'name': 'error.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/error.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'error.js', 'path': 'node_modules/@anthropic-ai/sdk/error.js', 'children': []}, {'type': 'FILE', 'name': 'error.js.map', 'path': 'node_modules/@anthropic-ai/sdk/error.js.map', 'children': []}, {'type': 'FILE', 'name': 'error.mjs', 'path': 'node_modules/@anthropic-ai/sdk/error.mjs', 'children': []}, {'type': 'FILE', 'name': 'error.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/error.mjs.map', 'children': []}, {'type': 'FILE', 'name': 'index.d.mts', 'path': 'node_modules/@anthropic-ai/sdk/index.d.mts', 'children': []}, {'type': 'FILE', 'name': 'index.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/index.d.ts', 'children': []}, {'type': 'FILE', 'name': 'index.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/index.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'index.js', 'path': 'node_modules/@anthropic-ai/sdk/index.js', 'children': []}, {'type': 'FILE', 'name': 'index.js.map', 'path': 'node_modules/@anthropic-ai/sdk/index.js.map', 'children': []}, {'type': 'FILE', 'name': 'index.mjs', 'path': 'node_modules/@anthropic-ai/sdk/index.mjs', 'children': []}, {'type': 'FILE', 'name': 'index.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/index.mjs.map', 'children': []}, {'type': 'DIRECTORY', 'name': 'node_modules', 'path': 'node_modules/@anthropic-ai/sdk/node_modules', 'children': [{'type': 'DIRECTORY', 'name': '@types', 'path': 'node_modules/@anthropic-ai/sdk/node_modules/@types', 'children': [{'type': 'TIPS', 'name': 'Max files (1000) reached...', 'path': '', 'children': []}]}]}, {'type': 'FILE', 'name': 'package.json', 'path': 'node_modules/@anthropic-ai/sdk/package.json', 'children': []}, {'type': 'FILE', 'name': 'resource.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/resource.d.ts', 'children': []}, {'type': 'FILE', 'name': 'resource.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/resource.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'resource.js', 'path': 'node_modules/@anthropic-ai/sdk/resource.js', 'children': []}, {'type': 'FILE', 'name': 'resource.js.map', 'path': 'node_modules/@anthropic-ai/sdk/resource.js.map', 'children': []}, {'type': 'FILE', 'name': 'resource.mjs', 'path': 'node_modules/@anthropic-ai/sdk/resource.mjs', 'children': []}, {'type': 'FILE', 'name': 'resource.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/resource.mjs.map', 'children': []}, {'type': 'DIRECTORY', 'name': 'resources', 'path': 'node_modules/@anthropic-ai/sdk/resources', 'children': [{'type': 'FILE', 'name': 'completions.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/resources/completions.d.ts', 'children': []}, {'type': 'FILE', 'name': 'completions.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/resources/completions.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'completions.js', 'path': 'node_modules/@anthropic-ai/sdk/resources/completions.js', 'children': []}, {'type': 'FILE', 'name': 'completions.js.map', 'path': 'node_modules/@anthropic-ai/sdk/resources/completions.js.map', 'children': []}, {'type': 'FILE', 'name': 'completions.mjs', 'path': 'node_modules/@anthropic-ai/sdk/resources/completions.mjs', 'children': []}, {'type': 'FILE', 'name': 'completions.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/resources/completions.mjs.map', 'children': []}, {'type': 'FILE', 'name': 'index.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/resources/index.d.ts', 'children': []}, {'type': 'FILE', 'name': 'index.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/resources/index.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'index.js', 'path': 'node_modules/@anthropic-ai/sdk/resources/index.js', 'children': []}, {'type': 'FILE', 'name': 'index.js.map', 'path': 'node_modules/@anthropic-ai/sdk/resources/index.js.map', 'children': []}, {'type': 'FILE', 'name': 'index.mjs', 'path': 'node_modules/@anthropic-ai/sdk/resources/index.mjs', 'children': []}, {'type': 'FILE', 'name': 'index.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/resources/index.mjs.map', 'children': []}, {'type': 'FILE', 'name': 'top-level.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/resources/top-level.d.ts', 'children': []}, {'type': 'FILE', 'name': 'top-level.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/resources/top-level.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'top-level.js', 'path': 'node_modules/@anthropic-ai/sdk/resources/top-level.js', 'children': []}, {'type': 'FILE', 'name': 'top-level.js.map', 'path': 'node_modules/@anthropic-ai/sdk/resources/top-level.js.map', 'children': []}, {'type': 'FILE', 'name': 'top-level.mjs', 'path': 'node_modules/@anthropic-ai/sdk/resources/top-level.mjs', 'children': []}, {'type': 'FILE', 'name': 'top-level.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/resources/top-level.mjs.map', 'children': []}]}, {'type': 'DIRECTORY', 'name': 'shims', 'path': 'node_modules/@anthropic-ai/sdk/shims', 'children': [{'type': 'FILE', 'name': 'node.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/shims/node.d.ts', 'children': []}, {'type': 'FILE', 'name': 'node.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/shims/node.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'node.js', 'path': 'node_modules/@anthropic-ai/sdk/shims/node.js', 'children': []}, {'type': 'FILE', 'name': 'node.js.map', 'path': 'node_modules/@anthropic-ai/sdk/shims/node.js.map', 'children': []}, {'type': 'FILE', 'name': 'node.mjs', 'path': 'node_modules/@anthropic-ai/sdk/shims/node.mjs', 'children': []}, {'type': 'FILE', 'name': 'node.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/shims/node.mjs.map', 'children': []}, {'type': 'FILE', 'name': 'web.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/shims/web.d.ts', 'children': []}, {'type': 'FILE', 'name': 'web.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/shims/web.d.ts.map', 'children': []}, {'type': 'FILE', 'name': 'web.js', 'path': 'node_modules/@anthropic-ai/sdk/shims/web.js', 'children': []}, {'type': 'FILE', 'name': 'web.js.map', 'path': 'node_modules/@anthropic-ai/sdk/shims/web.js.map', 'children': []}, {'type': 'FILE', 'name': 'web.mjs', 'path': 'node_modules/@anthropic-ai/sdk/shims/web.mjs', 'children': []}, {'type': 'FILE', 'name': 'web.mjs.map', 'path': 'node_modules/@anthropic-ai/sdk/shims/web.mjs.map', 'children': []}]}, {'type': 'DIRECTORY', 'name': 'src', 'path': 'node_modules/@anthropic-ai/sdk/src', 'children': [{'type': 'DIRECTORY', 'name': '_shims', 'path': 'node_modules/@anthropic-ai/sdk/src/_shims', 'children': [{'type': 'TIPS', 'name': 'Max files (1000) reached...', 'path': '', 'children': []}]}, {'type': 'FILE', 'name': 'error.ts', 'path': 'node_modules/@anthropic-ai/sdk/src/error.ts', 'children': []}, {'type': 'FILE', 'name': 'index.ts', 'path': 'node_modules/@anthropic-ai/sdk/src/index.ts', 'children': []}, {'type': 'FILE', 'name': 'resource.ts', 'path': 'node_modules/@anthropic-ai/sdk/src/resource.ts', 'children': []}, {'type': 'DIRECTORY', 'name': 'resources', 'path': 'node_modules/@anthropic-ai/sdk/src/resources', 'children': [{'type': 'TIPS', 'name': 'Max files (1000) reached...', 'path': '', 'children': []}]}, {'type': 'DIRECTORY', 'name': 'shims', 'path': 'node_modules/@anthropic-ai/sdk/src/shims', 'children': [{'type': 'TIPS', 'name': 'Max files (1000) reached...', 'path': '', 'children': []}]}, {'type': 'FILE', 'name': 'streaming.ts', 'path': 'node_modules/@anthropic-ai/sdk/src/streaming.ts', 'children': []}, {'type': 'FILE', 'name': 'tsconfig.json', 'path': 'node_modules/@anthropic-ai/sdk/src/tsconfig.json', 'children': []}, {'type': 'FILE', 'name': 'uploads.ts', 'path': 'node_modules/@anthropic-ai/sdk/src/uploads.ts', 'children': []}, {'type': 'FILE', 'name': 'version.ts', 'path': 'node_modules/@anthropic-ai/sdk/src/version.ts', 'children': []}]}, {'type': 'FILE', 'name': 'streaming.d.ts', 'path': 'node_modules/@anthropic-ai/sdk/streaming.d.ts', 'children': []}, {'type': 'FILE', 'name': 'streaming.d.ts.map', 'path': 'node_modules/@anthropic-ai/sdk/streaming.d.ts.map', 'children': []}, {'type': 'TIPS', 'name': 'Max 30 items per level...', 'path': '', 'children': []}]}]}]}, {'type': 'FILE', 'name': 'package-lock.json', 'path': 'package-lock.json', 'children': []}, {'type': 'FILE', 'name': 'package.json', 'path': 'package.json', 'children': []}, {'type': 'FILE', 'name': 'postcss.config.js', 'path': 'postcss.config.js', 'children': []}, {'type': 'DIRECTORY', 'name': 'scripts', 'path': 'scripts', 'children': [{'type': 'FILE', 'name': 'populateDb.ts', 'path': 'scripts/populateDb.ts', 'children': []}, {'type': 'FILE', 'name': 'sample_data.json', 'path': 'scripts/sample_data.json', 'children': []}]}, {'type': 'FILE', 'name': 'tailwind.config.js', 'path': 'tailwind.config.js', 'children': []}, {'type': 'FILE', 'name': 'tsconfig.json', 'path': 'tsconfig.json', 'children': []}]} # noqa
