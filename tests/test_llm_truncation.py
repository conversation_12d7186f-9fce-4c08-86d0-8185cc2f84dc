import pytest
from unittest.mock import AsyncMock, patch
from heracles.agent_controller.llm import LLM
from heracles.agent_roles.utils import sanitize_generated_text

@pytest.mark.asyncio
async def test_llm_no_truncation():
    """测试正常情况下没有截断的情况"""
    response_dict = {'choices': [{'finish_reason': 'stop', 'message': {'content': '完整的响应', 'tool_calls': []}}]}

    with patch('litellm.acompletion', new_callable=AsyncMock) as mock_acompletion:
        mock_acompletion.return_value = response_dict
        llm = LLM()
        result = await llm.async_send(user_message='测试消息')
        assert result == '完整的响应'
        assert mock_acompletion.call_count == 1


@pytest.mark.asyncio
async def test_llm_with_truncation():
    """测试响应被截断时的情况"""
    responses = [
        {'choices': [{'finish_reason': 'length', 'message': {'content': '第一部分响应', 'tool_calls': []}}]},
        {'choices': [{'finish_reason': 'stop', 'message': {'content': '第二部分响应', 'tool_calls': []}}]},
    ]

    with patch('litellm.acompletion', new_callable=AsyncMock) as mock_acompletion:
        mock_acompletion.side_effect = responses

        llm = LLM()
        result = await llm.async_send(user_message='测试消息')

        assert result == '第一部分响应第二部分响应'
        assert mock_acompletion.call_count == 2

        # 验证第二次调用时的messages参数
        second_call_args = mock_acompletion.call_args_list[1][1]
        assert len(second_call_args['messages']) == 3
        assert second_call_args['messages'][-2]['role'] == 'assistant'
        assert second_call_args['messages'][-2]['content'] == '第一部分响应'
        assert second_call_args['messages'][-1]['role'] == 'user'
        assert (
            second_call_args['messages'][-1]['content'][-1]['text'] == 'continue (without additional "```"(code block format) and new line)'
        )


@pytest.mark.asyncio
async def test_llm_max_truncation():
    """测试达到最大截断次数的情况"""
    response_dict = {'choices': [{'finish_reason': 'length', 'message': {'content': '部分响应', 'tool_calls': []}}]}

    with patch('litellm.acompletion', new_callable=AsyncMock) as mock_acompletion:
        mock_acompletion.return_value = response_dict

        llm = LLM()
        result = await llm.async_send(user_message='测试消息')

        # 验证调用次数为最大重试次数（10次）
        assert mock_acompletion.call_count == 10
        # 验证最终结果是所有部分响应的拼接
        assert result == '部分响应' * 10


@pytest.mark.asyncio
async def test_llm_with_tools():
    """测试使用tools时不进行截断处理"""
    response_dict = {
        'choices': [
            {'finish_reason': 'stop', 'message': {'content': '工具调用响应', 'tool_calls': [{'name': 'test_tool', 'arguments': '{}'}]}}
        ]
    }

    with patch('litellm.acompletion', new_callable=AsyncMock) as mock_acompletion:
        mock_acompletion.return_value = response_dict

        llm = LLM()
        result = await llm.async_send(user_message='测试消息', tools=[{'name': 'test_tool', 'parameters': {}}])

        assert mock_acompletion.call_count == 1
        assert hasattr(result, 'functions')


@pytest.mark.asyncio
async def test_llm_with_truncation_code():
    """测试响应被截断时的情况"""
    responses = [
        {
            'choices': [
                {
                    'finish_reason': 'length',
                    'message': {
                        'content': '```\nimport logging\nfrom datetime import datetime\n\nfrom django.db.backends.ddl_references import (\n    Columns, ForeignKeyName, IndexName, Statement, Table,\n)\nfrom django.db.backends.utils import names_digest, split_identifier\nfrom django.db.models import Index\nfrom django.db.transaction import TransactionManagementError, atomic\nfrom django.utils import timezone\n\nclass BaseDatabaseSchemaEditor:\n\n    def _alter_field(self, model, old_field, new_field, old_type, new_type,\n                     old_db_params, new_db_params, strict=False):\n        """Perform a "physical" (non-ManyToMany) field update."""\n        # Change check constraints?\n        if old_db_params[\'check\'] != new_db_params[\'check\'] and old_db_params[\'check\']:\n            meta_constraint_names = {constraint.name for constraint in model._meta.constraints}\n            constraint_names = self._constraint_names(\n                model, [old_field.column], check=True,\n                exclude=meta_constraint_names,\n            )\n            if strict and len(constraint_names) != 1:\n                raise ValueError("Found wrong number (%s) of check constraints for %s.%s" % (\n                    len(constraint_names),\n                    model._meta.db_table,\n                    old_field.column,\n                ))\n            for constraint_name in constraint_names:\n                self.execute(self._delete_check_sql(model, constraint_name))\n        # Have they renamed the column?',  # noqa
                        'tool_calls': [],
                    },
                }
            ]
        },
        {
            'choices': [
                {
                    'finish_reason': 'stop',
                    'message': {
                        'content': '```\n        if old_field.column != new_field.column:\n            self.execute(self._rename_field_sql(model._meta.db_table, old_field, new_field, new_type))\n            # Rename all references to the renamed column.\n            for sql in self.deferred_sql:\n                if isinstance(sql, Statement):\n                    sql.rename_column_references(model._meta.db_table, old_field.column, new_field.column)\n        # Next, start accumulating actions to do\n        actions = []\n        null_actions = []\n        post_actions = []\n        # Type change?\n        # Reset connection if required\n        if self.connection.features.connection_persists_old_columns:\n            self.connection.close()\n\n\n    def _post_migrate_check(self, model):\n        # Verify that all unique_together constraints still exist\n        unique_constraints = model._meta.unique_together\n        for fields in unique_constraints:\n            constraint_names = self._constraint_names(\n                model, fields, unique=True, index=False\n            )\n            if not constraint_names:\n                raise MigrationError(f"Missing unique constraint for fields {fields}")\n```',  # noqa
                        'tool_calls': [],
                    },
                }
            ]
        },
    ]

    with patch('litellm.acompletion', new_callable=AsyncMock) as mock_acompletion:
        mock_acompletion.side_effect = responses

        llm = LLM()
        result = await llm.async_send(user_message='测试消息')
        result = sanitize_generated_text(result)

        import ast

        assert ast.parse(result)
