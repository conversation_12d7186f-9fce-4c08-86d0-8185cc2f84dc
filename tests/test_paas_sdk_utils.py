import pytest
from unittest.mock import patch
from heracles.agent_workspace.paas_sdk.utils import (
    fetch, generate_token, get_environments, get_template_list,
    get_thread_template_list, import_repository, import_repository_async,
    import_repository_task_check, fork_codezone, upgrade_codezone_environment,
    get_playground_id, get_ticket, async_delete_codezone, delete_codezone,
    create_codezone, upload_files, git_clone, bind_playground_info,
    import_codezone_file, get_playground_info, add_middleware_to_codezone,
    list_middlewares_of_codezone, get_paas_middlewares, bind_middleware_to_codezone,
    remove_middleware_from_codezone, stop_playground, fork_new_codezone_get_id,
    get_playground_id_from_codezone_id, fork_code_zone_get_id,
    ImportRepositoryInfo, TicketInfo, bind_middleware_to_playground
)


class TestFetchErrorCases:
    """测试 fetch 函数中各种 HTTP 状态码和异常情况"""

    @pytest.mark.asyncio
    async def test_fetch_200_with_status_success(self):
        """测试 200 状态码且 status 为 success 的情况 (第76行)"""

        class MockResponse:
            status = 200

            async def json(self):
                return {"status": "success", "result": "ok"}

            async def __aenter__(self):
                return self

            async def __aexit__(self, *args):
                pass

        class MockSession:
            def request(self, *args, **kwargs):
                return MockResponse()

        session = MockSession()
        result = await fetch(session, "/test")
        assert result['success'] is True
        assert result['data'] == {"status": "success", "result": "ok"}
        assert result['error'] is None

    @pytest.mark.asyncio
    async def test_fetch_200_with_status_not_success(self):
        """测试 200 状态码但 status 不是 success 的情况 (第72-78行)"""

        class MockResponse:
            status = 200

            async def json(self):
                return {"status": "failed", "message": "some error"}

            async def __aenter__(self):
                return self

            async def __aexit__(self, *args):
                pass

        class MockSession:
            def request(self, *args, **kwargs):
                return MockResponse()

        session = MockSession()
        result = await fetch(session, "/test")
        assert result['success'] is False
        assert result['data'] == {"status": "failed", "message": "some error"}
        assert result['error'] is None

    @pytest.mark.asyncio
    async def test_fetch_201_with_data(self):
        """测试 201 状态码 + data 字段的响应处理 (第75-78行)"""

        class MockResponse:
            status = 201

            async def json(self):
                return {"data": {"id": "123"}}

            async def __aenter__(self):
                return self

            async def __aexit__(self, *args):
                pass

        class MockSession:
            def request(self, *args, **kwargs):
                return MockResponse()

        session = MockSession()
        result = await fetch(session, "/test")
        assert result['success'] is True
        assert result['data'] == {"id": "123"}
        assert result['error'] is None

    @pytest.mark.asyncio
    async def test_fetch_201_with_status_success(self):
        """测试 201 状态码 + status success 的响应处理 (第83-86行)"""

        class MockResponse:
            status = 201

            async def json(self):
                return {"status": "success", "result": "ok"}

            async def __aenter__(self):
                return self

            async def __aexit__(self, *args):
                pass

        class MockSession:
            def request(self, *args, **kwargs):
                return MockResponse()

        session = MockSession()
        result = await fetch(session, "/test")
        assert result['success'] is True
        assert result['data'] == {"status": "success", "result": "ok"}
        assert result['error'] is None

    @pytest.mark.asyncio
    async def test_fetch_201_failure_case(self):
        """测试 201 状态码的失败情况 (第87-94行)"""

        class MockResponse:
            status = 201

            async def json(self):
                return {"error": "some error"}

            async def __aenter__(self):
                return self

            async def __aexit__(self, *args):
                pass

        class MockSession:
            def request(self, *args, **kwargs):
                return MockResponse()

        session = MockSession()
        result = await fetch(session, "/test")
        assert result['success'] is False
        assert result['data'] == {"error": "some error"}
        assert result['error'] is None

    @pytest.mark.asyncio
    async def test_fetch_exception_handling(self):
        """测试异常处理逻辑"""

        class MockSession:
            def request(self, *args, **kwargs):
                raise Exception("Connection failed")

        session = MockSession()
        result = await fetch(session, "/test")
        assert result['success'] is False
        assert result['data'] == {}
        assert "Request failed: Connection failed" in result['error']


class TestAsyncFunctions:
    """测试异步函数调用"""

    @pytest.mark.asyncio
    async def test_get_environments(self):
        """测试 get_environments 函数 (第132-133行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"environments": []}}

            await get_environments()

            mock_fetch.assert_called_once()
            args = mock_fetch.call_args[0]
            assert '/api/v1/sdk/environmentVersions' in args[1]

    @pytest.mark.asyncio
    async def test_get_template_list(self):
        """测试 get_template_list 函数 (第141-142行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"templates": []}}

            await get_template_list()

            mock_fetch.assert_called_once()
            args = mock_fetch.call_args[0]
            assert '/thread/template/list' in args[1]

    @pytest.mark.asyncio
    async def test_get_thread_template_list(self):
        """测试 get_thread_template_list 函数 (第146-147行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"templates": []}}

            await get_thread_template_list()

            mock_fetch.assert_called_once()
            args = mock_fetch.call_args[0]
            assert '/thread/template/list' in args[1]

    @pytest.mark.asyncio
    async def test_import_repository(self):
        """测试 import_repository 函数 (第151-153行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"id": "123"}}

            repository_info: ImportRepositoryInfo = {
                'environmentVerId': 'env_123',
                'owner': 'test_owner',
                'repo': 'test_repo',
                'ref': 'main',
                'username': 'test_user',
                'purpose': 'test',
                'token': 'token_123',
                'privateKey': 'key_123'
            }

            await import_repository(repository_info)

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'POST'
            assert '/api/v1/sdk/codeZones/github' in args[1]
            assert kwargs['headers']['userId'] == 'test_user'

    @pytest.mark.asyncio
    async def test_import_repository_async(self):
        """测试 import_repository_async 函数 (第165-167行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"id": "123"}}

            repository_info: ImportRepositoryInfo = {
                'environmentVerId': 'env_123',
                'owner': 'test_owner',
                'repo': 'test_repo',
                'ref': 'main',
                'username': 'test_user',
                'purpose': 'test',
                'token': 'token_123',
                'privateKey': 'key_123'
            }

            await import_repository_async(repository_info)

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'POST'
            assert '/api/v1/sdk/codeZones/github/async' in args[1]
            assert kwargs['headers']['userId'] == 'test_user'

    @pytest.mark.asyncio
    async def test_import_repository_task_check(self):
        """测试 import_repository_task_check 函数 (第179-180行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"status": "completed"}}

            await import_repository_task_check("task_123")

            mock_fetch.assert_called_once()
            args = mock_fetch.call_args[0]
            assert 'task_123' in args[1]

    @pytest.mark.asyncio
    async def test_fork_codezone(self):
        """测试 fork_codezone 函数 (第187-188行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"id": "forked_123"}}

            await fork_codezone("codezone_123")

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'POST'
            assert '/api/v1/sdk/codeZones/fork' in args[1]

    @pytest.mark.asyncio
    async def test_upgrade_codezone_environment(self):
        """测试 upgrade_codezone_environment 函数 (第202-203行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"upgraded": True}}

            await upgrade_codezone_environment("codezone_123", "env_v2")

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'PUT'
            assert 'codezone_123' in args[1]
            assert kwargs['json']['environmentVerCode'] == 'env_v2'

    @pytest.mark.asyncio
    async def test_get_playground_id(self):
        """测试 get_playground_id 函数 (第214-215行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"id": "playground_123"}}

            await get_playground_id("codezone_123")

            mock_fetch.assert_called_once()
            args = mock_fetch.call_args[0]
            assert 'codezone_123' in args[1]
            assert 'playground' in args[1]

    @pytest.mark.asyncio
    async def test_get_ticket(self):
        """测试 get_ticket 函数 (第219-220行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"ticket": "ticket_123"}}

            ticket_info: TicketInfo = {
                'ticketId': 'ticket_123',
                'tenantCode': 'tenant_123'
            }

            await get_ticket(ticket_info)

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'POST'
            assert '/api/v1/sdk/ticket' in args[1]

    @pytest.mark.asyncio
    async def test_async_delete_codezone(self):
        """测试 async_delete_codezone 函数 (第214-215行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"deleted": True}}

            await async_delete_codezone("codezone_123")

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'DELETE'
            assert 'codezone_123' in args[1]

    @pytest.mark.asyncio
    async def test_delete_codezone(self):
        """测试 delete_codezone 函数 (第244-250行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"deleted": True}}

            await delete_codezone("codezone_123")

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'DELETE'
            assert 'codezone_123' in args[1]
            assert kwargs['json']['codeZoneId'] == 'codezone_123'

    @pytest.mark.asyncio
    async def test_create_codezone(self):
        """测试 create_codezone 函数 (第254-255行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"id": "new_codezone"}}

            await create_codezone("env_123")

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'POST'
            assert '/api/v1/sdk/codeZones' in args[1]
            assert kwargs['json']['environmentVerId'] == 'env_123'

    @pytest.mark.asyncio
    async def test_upload_files(self):
        """测试 upload_files 函数 (第229-230行)"""
        import tempfile
        import os

        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.zip') as tmp:
            tmp.write('test content')
            tmp_path = tmp.name

        try:
            with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
                mock_fetch.return_value = {"success": True, "data": {"uploaded": True}}

                files = {"file1": tmp_path}
                await upload_files("codezone_123", files)

                mock_fetch.assert_called_once()
                args, kwargs = mock_fetch.call_args
                assert kwargs['method'] == 'POST'
                assert 'codezone_123' in args[1]
                assert 'data' in kwargs
        finally:
            # 清理临时文件
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

    @pytest.mark.asyncio
    async def test_git_clone(self):
        """测试 git_clone 函数 (第279-280行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"cloned": True}}

            await git_clone("env_123", "owner", "username", "repo")

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'POST'
            assert '/api/v1/sdk/codeZones/github' in args[1]
            assert kwargs['json']['environmentVerId'] == 'env_123'
            assert kwargs['json']['owner'] == 'owner'
            assert kwargs['json']['username'] == 'username'
            assert kwargs['json']['repo'] == 'repo'

    @pytest.mark.asyncio
    async def test_bind_playground_info(self):
        """测试 bind_playground_info 函数 (第298-299行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"bound": True}}

            await bind_playground_info("playground_123")

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert 'playground_123' in args[1]
            assert kwargs['json']['bindType'] == 'CODE_ZONE'

    @pytest.mark.asyncio
    async def test_import_codezone_file(self):
        """测试 import_codezone_file 函数 (第310-311行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"imported": True}}

            await import_codezone_file("codezone_123", "test.py", "print('hello')")

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'POST'
            assert '/api/v1/sdk/codeZones/import' in args[1]
            assert kwargs['json']['codeZoneId'] == 'codezone_123'

    @pytest.mark.asyncio
    async def test_get_playground_info(self):
        """测试 get_playground_info 函数 (第321-322行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"info": "playground_info"}}

            await get_playground_info("playground_123")

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert 'playground_123' in args[1]
            assert kwargs['json']['bindType'] == 'CODE_ZONE'

    @pytest.mark.asyncio
    async def test_add_middleware_to_codezone(self):
        """测试 add_middleware_to_codezone 函数 (第333-334行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"added": True}}

            await add_middleware_to_codezone(123, 456)

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'POST'
            assert '123' in args[1]
            assert '456' in args[1]

    @pytest.mark.asyncio
    async def test_list_middlewares_of_codezone(self):
        """测试 list_middlewares_of_codezone 函数 (第338-339行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"middlewares": []}}

            await list_middlewares_of_codezone(123)

            mock_fetch.assert_called_once()
            args = mock_fetch.call_args[0]
            assert '123' in args[1]
            assert 'middlewareDefines/config' in args[1]

    @pytest.mark.asyncio
    async def test_get_paas_middlewares(self):
        """测试 get_paas_middlewares 函数 (第346-347行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"middlewares": []}}

            await get_paas_middlewares()

            mock_fetch.assert_called_once()
            args = mock_fetch.call_args[0]
            assert '/api/v1/sdk/middlewares' in args[1]


class TestMiddlewareFunctions:
    """测试中间件相关函数"""

    @pytest.mark.asyncio
    async def test_bind_middleware_to_codezone_found(self):
        """测试找到匹配中间件的情况 (第362-363行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.get_paas_middlewares') as mock_get_middlewares:
            with patch('heracles.agent_workspace.paas_sdk.utils.add_middleware_to_codezone') as mock_add_middleware:
                mock_get_middlewares.return_value = {
                    'data': [
                        {'id': 1, 'name': 'redis'},
                        {'id': 2, 'name': 'mysql'},
                        {'id': 3, 'name': 'postgresql'}
                    ]
                }
                mock_add_middleware.return_value = {"success": True}

                await bind_middleware_to_codezone(123, 'redis')

                mock_add_middleware.assert_called_once_with(123, 1)

    @pytest.mark.asyncio
    async def test_bind_middleware_to_codezone_not_found(self):
        """测试没有找到匹配中间件的情况 (第367-368行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.get_paas_middlewares') as mock_get_middlewares:
            with patch('heracles.agent_workspace.paas_sdk.utils.add_middleware_to_codezone') as mock_add_middleware:
                mock_get_middlewares.return_value = {
                    'data': [
                        {'id': 1, 'name': 'redis'},
                        {'id': 2, 'name': 'mysql'}
                    ]
                }

                await bind_middleware_to_codezone(123, 'mongodb')

                # 没有找到匹配的中间件，不应该调用 add_middleware_to_codezone
                mock_add_middleware.assert_not_called()

    @pytest.mark.asyncio
    async def test_remove_middleware_from_codezone(self):
        """测试 remove_middleware_from_codezone 函数"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"removed": True}}

            await remove_middleware_from_codezone(123, 456)

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'DELETE'
            assert '123' in args[1]
            assert '456' in args[1]

    @pytest.mark.asyncio
    async def test_stop_playground(self):
        """测试 stop_playground 函数"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fetch') as mock_fetch:
            mock_fetch.return_value = {"success": True, "data": {"stopped": True}}

            await stop_playground(123)

            mock_fetch.assert_called_once()
            args, kwargs = mock_fetch.call_args
            assert kwargs['method'] == 'POST'
            assert '123' in args[1]
            assert 'pause' in args[1]


class TestHelperFunctions:
    """测试辅助函数"""

    @pytest.mark.asyncio
    async def test_fork_new_codezone_get_id_success(self):
        """测试 fork_new_codezone_get_id 成功情况 (第372-377行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fork_codezone') as mock_fork:
            mock_fork.return_value = {'data': {'id': 'new_codezone_123'}}

            result = await fork_new_codezone_get_id('old_codezone_123')

            assert result == 'new_codezone_123'

    @pytest.mark.asyncio
    async def test_fork_new_codezone_get_id_error(self):
        """测试 fork_new_codezone_get_id 异常情况"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fork_codezone') as mock_fork:
            mock_fork.return_value = {'error': 'fork failed'}

            with pytest.raises(Exception) as exc_info:
                await fork_new_codezone_get_id('old_codezone_123')

            assert 'fork codezone error' in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_playground_id_from_codezone_id(self):
        """测试 get_playground_id_from_codezone_id 函数 (第381-382行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.get_playground_id') as mock_get_playground:
            mock_get_playground.return_value = {'data': {'id': 'playground_123'}}

            result = await get_playground_id_from_codezone_id('codezone_123')

            assert result == 'playground_123'

    @pytest.mark.asyncio
    async def test_fork_code_zone_get_id(self):
        """测试 fork_code_zone_get_id 函数 (第386-387行)"""
        with patch('heracles.agent_workspace.paas_sdk.utils.fork_codezone') as mock_fork:
            mock_fork.return_value = {'data': {'id': 'forked_codezone_123'}}

            result = await fork_code_zone_get_id('original_codezone_123')

            assert result == 'forked_codezone_123'

    @pytest.mark.asyncio
    async def test_bind_middleware_to_playground_upgrade(self):
        """测试 bind_middleware_to_playground 函数 - 升级中间件版本场景"""
        with patch('heracles.agent_workspace.paas_sdk.utils.get_playground_info') as mock_get_info, \
            patch('heracles.agent_workspace.paas_sdk.utils.list_middlewares_of_codezone') as mock_list_middlewares, \
            patch('heracles.agent_workspace.paas_sdk.utils.get_paas_middlewares') as mock_get_middlewares, \
            patch('heracles.agent_workspace.paas_sdk.utils.upgrade_middleware_to_codezone') as mock_upgrade:
            # 模拟 playground 信息
            mock_get_info.return_value = {'data': {'codeZoneId': 'codezone_123'}}

            # 模拟当前中间件列表
            mock_list_middlewares.return_value = {
                'data': [{
                    'name': 'redis 1.0.0',
                    'middlewareConfigId': 'config_123',
                    'middlewareDefineId': 'define_123'
                }]
            }

            # 模拟支持的中间件列表
            mock_get_middlewares.return_value = {
                'data': [
                    {'id': 'define_456', 'name': 'redis 2.0.0'}
                ]
            }

            # 模拟升级结果
            mock_upgrade.return_value = {'success': True, 'data': {'upgraded': True}}

            result = await bind_middleware_to_playground('playground_123', 'redis 2.0.0')

            assert result['success']

            mock_upgrade.assert_called_once_with(
                'codezone_123',
                'config_123',
                'define_123',
                'define_456'
            )

    @pytest.mark.asyncio
    async def test_bind_middleware_to_playground_match(self):
        """测试 bind_middleware_to_playground 函数 - 匹配现有中间件场景"""
        with patch('heracles.agent_workspace.paas_sdk.utils.get_playground_info') as mock_get_info, \
            patch('heracles.agent_workspace.paas_sdk.utils.list_middlewares_of_codezone') as mock_list_middlewares, \
            patch('heracles.agent_workspace.paas_sdk.utils.get_paas_middlewares') as mock_get_middlewares:
            # 模拟 playground 信息
            mock_get_info.return_value = {'data': {'codeZoneId': 'codezone_123'}}

            # 模拟当前中间件列表
            mock_list_middlewares.return_value = {
                'data': [{
                    'name': 'redis 1.0.0',
                    'middlewareConfigId': 'config_123',
                    'middlewareDefineId': 'define_123'
                }]
            }

            # 模拟支持的中间件列表
            mock_get_middlewares.return_value = {
                'data': [
                    {'id': 'define_123', 'name': 'redis 1.0.0'}
                ]
            }

            result = await bind_middleware_to_playground('playground_123', 'redis 1.0.0')

            assert result['success'] is True
            assert result['data'] is None
            assert result['error'] is None

    @pytest.mark.asyncio
    async def test_bind_middleware_to_playground_bind(self):
        """测试 bind_middleware_to_playground 函数 - 绑定新中间件场景"""
        with patch('heracles.agent_workspace.paas_sdk.utils.get_playground_info') as mock_get_info, \
            patch('heracles.agent_workspace.paas_sdk.utils.list_middlewares_of_codezone') as mock_list_middlewares, \
            patch('heracles.agent_workspace.paas_sdk.utils.get_paas_middlewares') as mock_get_middlewares, \
            patch('heracles.agent_workspace.paas_sdk.utils.bind_middleware_to_codezone') as mock_bind:
            # 模拟 playground 信息
            mock_get_info.return_value = {'data': {'codeZoneId': 'codezone_123'}}

            # 模拟当前中间件列表为空
            mock_list_middlewares.return_value = {'data': []}

            # 模拟支持的中间件列表
            mock_get_middlewares.return_value = {
                'data': [
                    {'id': 'define_123', 'name': 'redis 1.0.0'}
                ]
            }

            # 模拟绑定结果
            mock_bind.return_value = {'success': True, 'data': {'bound': True}}

            result = await bind_middleware_to_playground('playground_123', 'redis 1.0.0')

            assert result['success'] is True
            mock_bind.assert_called_once_with('codezone_123', 'redis 1.0.0')

    @pytest.mark.asyncio
    async def test_bind_middleware_to_playground_not_found(self):
        """测试 bind_middleware_to_playground 函数 - 中间件未找到场景"""
        with patch('heracles.agent_workspace.paas_sdk.utils.get_playground_info') as mock_get_info, \
            patch('heracles.agent_workspace.paas_sdk.utils.list_middlewares_of_codezone') as mock_list_middlewares, \
            patch('heracles.agent_workspace.paas_sdk.utils.get_paas_middlewares') as mock_get_middlewares:
            # 模拟 playground 信息
            mock_get_info.return_value = {'data': {'codeZoneId': 'codezone_123'}}

            # 模拟当前中间件列表为空
            mock_list_middlewares.return_value = {'data': []}

            # 模拟支持的中间件列表（不包含目标中间件）
            mock_get_middlewares.return_value = {
                'data': [
                    {'id': 'define_123', 'name': 'mysql 1.0.0'}
                ]
            }

            result = await bind_middleware_to_playground('playground_123', 'redis 1.0.0')

            assert not result['success']
            assert 'Middleware redis 1.0.0 not found in the list' in result['error']

    @pytest.mark.asyncio
    async def test_bind_middleware_to_playground_exception(self):
        """测试 bind_middleware_to_playground 函数 - 异常处理场景"""
        with patch('heracles.agent_workspace.paas_sdk.utils.get_playground_info') as mock_get_info:
            # 模拟获取 playground 信息时抛出异常
            mock_get_info.side_effect = Exception("Network error")

            result = await bind_middleware_to_playground('playground_123', 'redis 1.0.0')

            assert not result['success']
            assert 'Unexpected error while binding middleware redis 1.0.0 to playground playground_123' in result[
                'error']
            assert 'Network error' in result['error']


class TestTokenGeneration:
    """测试 token 生成函数"""

    def test_generate_token_with_different_inputs(self):
        """测试不同输入的 token 生成"""
        # 测试不同的 nonce 和 timestamp 组合
        token1 = generate_token('abc123', '1234567890')
        token2 = generate_token('def456', '0987654321')

        # 确保不同输入产生不同的 token
        assert token1 != token2
        assert isinstance(token1, str)
        assert isinstance(token2, str)

        # 确保相同输入产生相同的 token
        token3 = generate_token('abc123', '1234567890')
        assert token1 == token3
