from jinja2 import Template
import pytest
from heracles.agent_workspace.playbook import Playbook, call_function_or_property
from heracles.core.schema.knowledge import FrameworkType
from heracles.core.exceptions import AgentRunException

@pytest.fixture
def playbook():
    return Playbook(
        title="Handling `.env` Files",
        description="",
        tags=['initialization'],
        applicable_rules=['tools://check_path_exists(".env.example")'],
        original_content="""
        {%- if tools://check_path_exists(".env") -%}
        - modify `.env` file with proper values if needed.
        {%- endif -%}
        {%- if tools://check_path_exists(".env.example") and not tools://check_path_exists(".env") -%}
        - run command to copy `.env.example` to `.env`.
        - modify `.env` file with proper values if needed.
        {%- endif -%}
        {% set framework = "project_knowledge://framework" -%}
        {% if framework == 'html/css/js' -%}
        - for `html/css/js` project, `browser-sync` is already installed
        {% endif %}
        """
    )

@pytest.fixture
def user_playbook():
    return Playbook(
        title="some instruction",
        description="",
        tags=['instruction'],
        applicable_rules=[],
        original_content="""
        1. read some file
        2. modify file
        3. upload file to 'https://xxx.storage'
        """,
        source='user'
    )

@pytest.mark.asyncio
async def test_rendered_content(playbook, mocker, create_workspace):
    workspace = await create_workspace

    def side_effect(filename):
        if filename == ".env":
            return False
        if filename == ".env.example":
            return True
        return False
    assert playbook.source == 'system'
    mocker.patch.object(workspace.tools, 'check_path_exists', side_effect=side_effect)
    rendered = await playbook.render_content(workspace)
    new_content = Template(rendered).render()
    assert "- run command to copy `.env.example` to `.env`." in new_content
    assert "- modify `.env` file with proper values if needed." in new_content

    mocker.patch.object(workspace.tools, 'check_path_exists', return_value=True)
    rendered = await playbook.render_content(workspace)
    new_content = Template(rendered).render()
    assert "- modify `.env` file with proper values if needed." in new_content
    assert "- run command to copy `.env.example` to `.env`." not in new_content

    mocker.patch.object(workspace.project_knowledge.basic_info_list[0], 'framework', FrameworkType.HTML_CSS_JS)
    rendered = await playbook.render_content(workspace)
    new_content = Template(rendered).render()
    assert "- for `html/css/js` project, `browser-sync` is already installed" in new_content

@pytest.mark.asyncio
async def test_render_content_errors(playbook, mocker, create_workspace):
    workspace = await create_workspace
    assert playbook.source == 'system'
    with pytest.raises(AgentRunException, match="Check rule error: `normal text` result is not a boolean, result: normal text"):
        playbook.applicable_rules = ["normal text"]
        await playbook._check_applicable(workspace)

    with pytest.raises(AgentRunException, match="tools.not_exist is not whitelisted"):
        playbook.original_content = "tools://not_exist(\"test\")"
        await playbook.render_content(mocker.Mock(tools=mocker.Mock(not_exist=lambda x: x)))

    with pytest.raises(AgentRunException, match="`tools.check_path_exists` error"):
        playbook.original_content = "tools://check_path_exists(\".env\")"
        mocker.patch.object(workspace.tools, 'check_path_exists', side_effect=Exception("test error"))
        await playbook.render_content(workspace)

    with pytest.raises(AgentRunException, match="Invalid schema: invalid"):
        playbook.original_content = "invalid://test(\"test\")"
        await playbook.render_content(workspace)

    with pytest.raises(AgentRunException, match="Invalid schema: invalid"):
        await call_function_or_property('invalid', 'test', 'test', workspace)

    with pytest.raises(AgentRunException, match="`project_knowledge.not_exist` not found"):
        playbook.original_content = "project_knowledge://not_exist(\"test\")"
        await playbook.render_content(workspace)


@pytest.mark.asyncio
async def test_render_user_content(user_playbook, mocker, create_workspace):
    workspace = await create_workspace

    assert user_playbook.source == 'user'
    rendered = await user_playbook.render_content(workspace)
    new_content = Template(rendered).render()
    assert "https://xxx" in new_content
