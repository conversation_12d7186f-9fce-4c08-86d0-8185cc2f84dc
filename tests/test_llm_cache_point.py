import pytest
from unittest.mock import patch
from heracles.agent_controller.llm import LLM
from heracles.core.exceptions import AgentRunException


@pytest.fixture
def llm_instance():
    """创建LLM实例用于测试"""
    env_vars = {
        'LLM_MODEL': 'test-model',
        'LLM_API_KEY': 'test-api-key',
        'LLM_BASE_URL': 'http://test-base-url',
    }

    with patch('heracles.agent_controller.llm.get_env_var', side_effect=lambda key: env_vars.get(key, '')):
        llm = LLM()
        with patch.object(llm, '_get_real_name', return_value='test-model'):
            return llm


def test_add_cache_point(llm_instance):
    """测试add_cache_point方法的各种场景"""
    DIVIDER = '<---------------------------above_system_prompt_cached------------------------>'

    # 1. 测试基础功能：字符串内容和列表内容
    # 字符串内容
    messages: list[dict] = [{'role': 'system', 'content': 'system prompt'}, {'role': 'user', 'content': 'user message'}]
    result = llm_instance.add_cache_point(messages)
    assert result[0]['content'][0]['cache_control'] == {'type': 'ephemeral'}
    assert result[1]['content'][0]['cache_control'] == {'type': 'ephemeral'}

    # 列表内容
    messages = [
        {'role': 'system', 'content': [{'type': 'text', 'text': 'system prompt'}]},
        {'role': 'user', 'content': [{'type': 'text', 'text': 'user message'}]},
    ]
    result = llm_instance.add_cache_point(messages)
    assert result[0]['content'][0]['cache_control'] == {'type': 'ephemeral'}
    assert result[1]['content'][0]['cache_control'] == {'type': 'ephemeral'}

    # 2. 测试system_only参数
    messages = [
        {'role': 'system', 'content': [{'type': 'text', 'text': 'system prompt'}]},
        {'role': 'user', 'content': [{'type': 'text', 'text': 'user message'}]},
    ]
    result = llm_instance.add_cache_point(messages, system_only=True)
    assert result[0]['content'][0]['cache_control'] == {'type': 'ephemeral'}
    assert 'cache_control' not in result[1]['content'][0]

    # 3. 测试系统提示分隔符
    # 两个文本块
    messages = [{'role': 'system', 'content': f'part1{DIVIDER}part2'}, {'role': 'user', 'content': 'user message'}]
    result = llm_instance.add_cache_point(messages)
    assert len(result[0]['content']) == 2
    assert result[0]['content'][0]['text'] == 'part1'
    assert result[0]['content'][0]['cache_control'] == {'type': 'ephemeral'}
    assert result[0]['content'][1]['text'] == 'part2'
    assert 'cache_control' not in result[0]['content'][1]

    # 三个文本块
    messages = [{'role': 'system', 'content': f'part1{DIVIDER}part2{DIVIDER}part3'}, {'role': 'user', 'content': 'user message'}]
    result = llm_instance.add_cache_point(messages)
    assert len(result[0]['content']) == 3
    assert result[0]['content'][1]['cache_control'] == {'type': 'ephemeral'}
    assert 'cache_control' not in result[0]['content'][0]
    assert 'cache_control' not in result[0]['content'][2]

    # 4. 测试错误情况
    # 多个系统角色
    messages = [
        {'role': 'system', 'content': 'system1'},
        {'role': 'system', 'content': 'system2'},
        {'role': 'user', 'content': 'user message'},
    ]
    with pytest.raises(AgentRunException, match='Found multiple system role in message.'):
        llm_instance.add_cache_point(messages)

    # 非文本系统内容
    messages = [{'role': 'system', 'content': [{'type': 'image', 'image_url': 'url'}]}, {'role': 'user', 'content': 'user message'}]
    with pytest.raises(AgentRunException, match='System prompt contains non-text message.'):
        llm_instance.add_cache_point(messages)

    # 5. 测试其他角色处理
    messages = [
        {'role': 'system', 'content': 'system prompt'},
        {'role': 'user', 'content': 'user message'},
        {'role': 'assistant', 'content': 'assistant message'},
        {'role': 'tool', 'content': 'user message'},
    ]
    result = llm_instance.add_cache_point(messages)
    assert result[0]['content'][0]['cache_control'] == {'type': 'ephemeral'}
    assert result[3]['cache_control'] == {'type': 'ephemeral'}

    # 6. 测试边缘情况
    # 空消息列表
    assert llm_instance.add_cache_point([]) == []

    # 只有非system/user角色的消息
    other_roles = [{'role': 'assistant', 'content': 'Assistant message'}, {'role': 'function', 'content': 'Function message'}]
    other_roles_result = llm_instance.add_cache_point(other_roles)
    assert isinstance(other_roles_result[0]['content'], str)

    # 用户消息内容为空列表
    empty_content = [{'role': 'user', 'content': []}]
    llm_instance.add_cache_point(empty_content)  # 不应抛出错误


@pytest.mark.asyncio
async def test_add_cache_point_integration(llm_instance):
    """测试与call_acompletion的集成"""
    messages = [{'role': 'system', 'content': 'System message'}, {'role': 'user', 'content': 'User message'}]

    with patch('litellm.acompletion') as mock_acompletion:
        mock_acompletion.return_value = {'choices': [{'message': {'content': 'Test response'}}]}

        # 测试不带tools的调用 (system_only=True)
        await llm_instance.call_acompletion(messages=messages)
        processed_messages = mock_acompletion.call_args[1]['messages']
        assert processed_messages[0]['content'][0]['cache_control'] == {'type': 'ephemeral'}
        assert 'cache_control' not in processed_messages[1]['content'][0]

        # 测试带tools的调用 (system_only=False)
        mock_acompletion.reset_mock()
        await llm_instance.call_acompletion(messages=messages, tools=[{'type': 'function'}])
        processed_messages = mock_acompletion.call_args[1]['messages']
        assert processed_messages[0]['content'][0]['cache_control'] == {'type': 'ephemeral'}
        assert processed_messages[1]['content'][0]['cache_control'] == {'type': 'ephemeral'}
