import pytest
from typing import Any
from heracles.agent_memory.memory import Memory
from heracles.core.schema import MemoryMessage
from pydantic import ValidationError

@pytest.mark.asyncio
async def test_memory():
    memory = Memory()
    memory.add_user_message("hello, user")
    memory.add_assistant_message("hello, assistant")

    memory_message = MemoryMessage(role="role1", content="content1")
    memory.add(memory_message)
    memory.add_from_dict({"role": "role2", "content": "content2"})

    with pytest.raises(ValidationError):
        memory.add_from_dict({"role": "role2"})

@pytest.mark.asyncio
async def test_memory_remove_pair_messages():
    memory = Memory()
    memory.add_user_message("hello, user")
    memory.add_assistant_message("hello, assistant")
    memory.add_user_message("hello, user2")
    memory.add_assistant_message("hello, assistant2")

    assert len(memory.to_llm_messages('system_prompt')), 5
    memory.remove_newest_pair_memories()
    assert len(memory.to_llm_messages('system_prompt')), 3

@pytest.mark.asyncio
async def test_memory_tools():
    memory = Memory()
    memory.add_user_message("hello, user")
    memory.add_assistant_message("hello, assistant")
    memory.add_assistant_message("hello, assistant2")
    memory.add_tool_message('tool', '123')

    assert len(memory.to_llm_messages('system_prompt')), 4
    memory.remove_newest_pair_memories()
    assert len(memory.to_llm_messages('system_prompt')), 1

@pytest.mark.asyncio
async def test_memory_trim():
    messages: list[dict[str, Any]] = [
        {
            'role': 'system',
            'content': 'test',
        },
        {'role': 'user', 'content': '步骤里还有错误，请继续解决'},
        {
            'role': 'assistant',
            'content': '让我检查任务详情以帮助解决问题。\n\n我看到这个任务是关于清理已废弃的订阅功能测试文件，主要涉及删除4个文件。目标是移除不再需要的 subscribes 相关的测试文件。\n\n现在Step 1和Step 3的操作都标记为"abandoned"，这是正确的，因为我们应该专注于文件删除操作而不是运行测试。\n\n让我们重新整理一个简洁的计划：\n删除4个订阅功能相关的废弃测试文件，清理过程不需要运行测试。', # noqa
            'tool_calls': [
                {
                    'id': 'toolu_vrtx_01KqeKMd7a1W974QT8ZPeSoU',
                    'function': {
                        'arguments': """{
                            'title': 'Clean up deprecated subscription-related test files',
                            'task_actions': [
                                {'action': 'delete_file', 'path': 'spec/controllers/admin/subscribes_controller_spec.rb'},
                                {'action': 'delete_file', 'path': 'spec/controllers/subscribes_controller_spec.rb'},
                                {'action': 'delete_file', 'path': 'spec/controllers/unsuscribes_controller_spec.rb'},
                                {'action': 'delete_file', 'path': 'spec/factories/subscribes.rb'},
                            ],
                        }""",
                        'name': 'TaskStepModel',
                    },
                    'type': 'function',
                    'index': 0,
                }
            ],
        },
        {
            'role': 'tool',
            'content': 'A new instance of `TaskStepModel` has been successfully created',
            'tool_call_id': 'toolu_vrtx_01KqeKMd7a1W974QT8ZPeSoU',
        },
        {'role': 'user', 'content': '运行测试仍然不成功，请继续分析rspec结果并修改代码继续测试'},
        {
            'role': 'assistant',
            'content': '让我先查看一下RSpec的测试文件内容来分析问题。由于涉及到测试相关，我会先查看rails_helper.rb和测试目录中的内容。',
            'tool_calls': [
                {
                    'id': 'toolu_vrtx_01CBpUtw2zZaarfZxDCpNQ4p',
                    'function': {'arguments': {'path': 'spec/rails_helper.rb'}, 'name': 'read_file'},
                    'type': 'function',
                    'index': 0,
                }
            ],
        },
        {
            'role': 'tool',
            'tool_call_id': 'toolu_vrtx_01CBpUtw2zZaarfZxDCpNQ4p',
            'name': 'read_file',
            'content': "test",
        },
        {
            'role': 'assistant',
            'content': '\n\n让我们再看一下测试文件来定位问题：',
            'tool_calls': [
                {
                    'id': 'toolu_012gbuU2sr185RivALJHFb2Y',
                    'function': {'arguments': {'path': 'spec/support/factory_bot.rb'}, 'name': 'read_file'},
                    'type': 'function',
                    'index': 0,
                }
            ],
        },
        {
            'role': 'tool',
            'tool_call_id': 'toolu_012gbuU2sr185RivALJHFb2Y',
            'name': 'read_file',
            'content': 'RSpec.configure do |config|\n  config.include FactoryBot::Syntax::Methods\nend\n',
        },
        {
            'role': 'assistant',
            'content': '\n\n让我们检查一下还有没有其他可能存在订阅相关引用的文件。由于是订阅功能，我们需要检查一下models和controllers的测试文件：', # noqa
            'tool_calls': [
                {
                    'id': 'toolu_01VmyDxdMvjWb3nXAn3VDkNk',
                    'function': {'arguments': {'path': 'spec/spec_helper.rb'}, 'name': 'read_file'},
                    'type': 'function',
                    'index': 0,
                }
            ],
        },
        {
            'role': 'tool',
            'tool_call_id': 'toolu_01VmyDxdMvjWb3nXAn3VDkNk',
            'name': 'read_file',
            'content': '# This file was generated by the `rails generate rspec:install` command. Conventionally, all\n# specs live under a `spec` directory, which RSpec adds to the `$LOAD_PATH`.\n# The generated `.rspec` file contains `--require spec_helper` which will cause\n# this file to always be loaded, without a need to explicitly require it in any\n# files.\n#\n# Given that it is always loaded, you are encouraged to keep this file as\n# light-weight as possible. Requiring heavyweight dependencies from this file\n# will add to the boot time of your test suite on EVERY test run, even for an\n# individual file that may not need all of that loaded. Instead, consider making\n# a separate helper file that requires the additional dependencies and performs\n# the additional setup, and require it from the spec files that actually need\n# it.\n#\n# See http://rubydoc.info/gems/rspec-core/RSpec/Core/Configuration\nRSpec.configure do |config|\n  # rspec-expectations config goes here. You can use an alternate\n  # assertion/expectation library such as wrong or the stdlib/minitest\n  # assertions if you prefer.\n  config.expect_with :rspec do |expectations|\n    # This option will default to `true` in RSpec 4. It makes the `description`\n    # and `failure_message` of custom matchers include text for helper methods\n    # defined using `chain`, e.g.:\n    #     be_bigger_than(2).and_smaller_than(4).description\n    #     # => "be bigger than 2 and smaller than 4"\n    # ...rather than:\n    #     # => "be bigger than 2"\n    expectations.include_chain_clauses_in_custom_matcher_descriptions = true\n  end\n\n  # rspec-mocks config goes here. You can use an alternate test double\n  # library (such as bogus or mocha) by changing the `mock_with` option here.\n  config.mock_with :rspec do |mocks|\n    # Prevents you from mocking or stubbing a method that does not exist on\n    # a real object. This is generally recommended, and will default to\n    # `true` in RSpec 4.\n    mocks.verify_partial_doubles = true\n  end\n\n  # This option will default to `:apply_to_host_groups` in RSpec 4 (and will\n  # have no way to turn it off -- the option exists only for backwards\n  # compatibility in RSpec 3). It causes shared context metadata to be\n  # inherited by the metadata hash of host groups and examples, rather than\n  # triggering implicit auto-inclusion in groups with matching metadata.\n  config.shared_context_metadata_behavior = :apply_to_host_groups\n\n# The settings below are suggested to provide a good initial experience\n# with RSpec, but feel free to customize to your heart\'s content.\n=begin\n  # This allows you to limit a spec run to individual examples or groups\n  # you care about by tagging them with `:focus` metadata. When nothing\n  # is tagged with `:focus`, all examples get run. RSpec also provides\n  # aliases for `it`, `describe`, and `context` that include `:focus`\n  # metadata: `fit`, `fdescribe` and `fcontext`, respectively.\n  config.filter_run_when_matching :focus\n\n  # Allows RSpec to persist some state between runs in order to support\n  # the `--only-failures` and `--next-failure` CLI options. We recommend\n  # you configure your source control system to ignore this file.\n  config.example_status_persistence_file_path = "spec/examples.txt"\n\n  # Limits the available syntax to the non-monkey patched syntax that is\n  # recommended. For more details, see:\n  #   - http://rspec.info/blog/2012/06/rspecs-new-expectation-syntax/\n  #   - http://www.teaisaweso.me/blog/2013/05/27/rspecs-new-message-expectation-syntax/\n  #   - http://rspec.info/blog/2014/05/notable-changes-in-rspec-3/#zero-monkey-patching-mode\n  config.disable_monkey_patching!\n\n  # Many RSpec users commonly either run the entire suite or an individual\n  # file, and it\'s useful to allow more verbose output when running an\n  # individual spec file.\n  if config.files_to_run.one?\n    # Use the documentation formatter for detailed output,\n    # unless a formatter has already been configured\n    # (e.g. via a command-line flag).\n    config.default_formatter = "doc"\n  end\n\n  # Print the 10 slowest examples and example groups at the\n  # end of the spec run, to help surface which specs are running\n  # particularly slow.\n  config.profile_examples = 10\n\n  # Run specs in random order to surface order dependencies. If you find an\n  # order dependency and want to debug it, you can fix the order by providing\n  # the seed, which is printed after each run.\n  #     --seed 1234\n  config.order = :random\n\n  # Seed global randomization in this process using the `--seed` CLI option.\n  # Setting this allows you to use `--seed` to deterministically reproduce\n  # test failures related to randomization by passing the same `--seed` value\n  # as the one that triggered the failure.\n  Kernel.srand config.seed\n=end\nend\n', # noqa
        },
        {
            'role': 'assistant',
            'content': '\n\n看来可能是测试中还残留了一些subscription相关的引用，让我们继续检查一下项目中可能包含subscription引用的文件：',
            'tool_calls': [
                {
                    'id': 'toolu_01E1UXogdbqXA49ekb4XgocK',
                    'function': {'arguments': {'path': 'spec/models/post_spec.rb'}, 'name': 'read_file'},
                    'type': 'function',
                    'index': 0,
                }
            ],
        },
        {
            'role': 'tool',
            'tool_call_id': 'toolu_01E1UXogdbqXA49ekb4XgocK',
            'name': 'read_file',
            'content': 'test',
        },
        {
            'role': 'assistant',
            'content': '',
            'tool_calls': [
                {
                    'id': 'toolu_01Y7Dd8C1LqWB7NZw5Wd6g1f',
                    'function': {'arguments': {'path': 'spec/controllers/home_controller_spec.rb'}, 'name': 'read_file'},
                    'type': 'function',
                    'index': 0,
                }
            ],
        },
        {
            'role': 'tool',
            'tool_call_id': 'toolu_01Y7Dd8C1LqWB7NZw5Wd6g1f',
            'name': 'read_file',
            'content': "test",
        },
        {
            'role': 'assistant',
            'content': '\n\n我发现问题了。我们还需要检查是否有关于subscription的路由配置或其他引用。让我先看看routes.rb文件：',
            'tool_calls': [
                {
                    'id': 'toolu_vrtx_012Wm6ce87xNN2BytWmuggek',
                    'function': {'arguments': {'path': 'config/routes.rb'}, 'name': 'read_file'},
                    'type': 'function',
                    'index': 0,
                }
            ],
        },
        {
            'role': 'tool',
            'tool_call_id': 'toolu_vrtx_012Wm6ce87xNN2BytWmuggek',
            'name': 'read_file',
            'content': "test",
        },
        {'role': 'user', 'content': "You have reach the max_tool_call limit, now carefully give the answer or conclude that you can't."},
        {
            "role": "assistant",
            "content": "经过检查多个文件，我看到可能的问题所在。虽然我们删除了4个subscription相关的测试文件，但是看起来还需要检查和清理以下可能的问题：\n\n1. 数据库中可能还存在 subscribes 相关的表和迁移文件\n2. 项目中可能还存在 subscribe model 文件\n3. 可能存在其他文件对 subscription 功能的引用\n\n我应该继续查找和分析，但由于已达到工具调用限制，我建议：\n\n1. 让我继续查看项目中的迁移文件和模型文件\n2. 检查是否存在订阅相关的数据库表\n3. 搜索项目中所有可能包含\"subscribe\"或\"subscription\"引用的文件\n\n您是否希望我继续深入分析这些方面？", # noqa
            "tool_calls": None
        },
    ]
    memory = Memory()
    for message in messages:
        memory.add_from_dict(message)

    for size in range(0, len(messages)+1):
        res = memory.to_llm_messages('system_prompt', max_size=size)
        assert res[0]['role'] == 'system'
        assert res[1]['role'] == 'user'
