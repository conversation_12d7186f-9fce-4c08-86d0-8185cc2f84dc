import pytest
import asyncio
from heracles.core.utils.event_emitter import EventEmitter
from heracles.core.exceptions import AgentException

def on_user_registered(username):
    return f'User registered: {username}'

async def on_user_logged_in(username):
    return f'User logged in: {username}'

@pytest.mark.asyncio
async def test_event_emitter():
    emitter = EventEmitter(bubbling=False)

    emitter.on('user_registered', on_user_registered)
    emitter.on('user_logged_in', on_user_logged_in)

    assert emitter.has_listener('user_registered') is True
    assert emitter.has_listener('user_logged_in') is True
    assert emitter.has_listener('non_existent_event') is False

    result = await emitter.emit('user_registered', 'Alice')
    assert result == 'User registered: Alice'

    result = await emitter.emit('user_logged_in', 'Bob')
    assert result == 'User logged in: Bob'

    emitter.off('user_logged_in', on_user_logged_in)
    assert emitter.has_listener('user_logged_in') is False

    with pytest.raises(AgentException):
        await emitter.emit('user_logged_in', 'Charlie')

    emitter.off_all('user_registered')
    assert emitter.has_listener('user_registered') is False

    with pytest.raises(AgentException):
        emitter.off('non_existent_event', lambda x: x)

    with pytest.raises(AgentException):
        emitter.off_all('non_existent_event')

@pytest.mark.asyncio
async def test_event_emitter_bubbling():
    emitter = EventEmitter(bubbling=True)

    results = []
    def collect_results(result):
        results.append(result)

    emitter.on('user_registered', on_user_registered)
    emitter.on('user_registered', lambda x: f'Second listener: {x}')
    emitter.on('user_registered', collect_results)

    await emitter.emit('user_registered', 'Alice')
    assert results == ['Alice']

@pytest.mark.asyncio
async def test_event_emitter_non_bubbling():
    emitter = EventEmitter(bubbling=False)

    results = []
    def collect_results(result):
        results.append(result)

    emitter.on('test_event', lambda x: f'First: {x}')
    emitter.on('test_event', lambda x: f'Second: {x}')
    emitter.on('test_event', collect_results)

    result = await emitter.emit('test_event', 'Data')
    assert result is None
    assert results == ['Data']

@pytest.mark.asyncio
async def test_event_emitter_sync_and_async_mix():
    emitter = EventEmitter(bubbling=True)

    results = []
    async def async_listener(data):
        await asyncio.sleep(0.1)
        results.append(f'Async: {data}')
        return f'Async: {data}'

    def sync_listener(data):
        results.append(f'Sync: {data}')
        return f'Sync: {data}'

    emitter.on('mixed_event', async_listener)
    emitter.on('mixed_event', sync_listener)

    await emitter.emit('mixed_event', 'test')
    assert results == ['Async: test', 'Sync: test']

@pytest.mark.asyncio
async def test_event_emitter_error_handling():
    emitter = EventEmitter(bubbling=True)

    def error_listener(data):
        raise ValueError("Test error")

    emitter.on('error_event', error_listener)

    with pytest.raises(ValueError):
        await emitter.emit('error_event', 'test')

@pytest.mark.asyncio
async def test_event_emitter_multiple_listeners():
    emitter = EventEmitter(bubbling=True)

    results = []
    for i in range(5):
        emitter.on('multi_event', lambda x, i=i: results.append(f'Listener {i}: {x}'))

    await emitter.emit('multi_event', 'test')
    assert len(results) == 5
    assert results[-1] == 'Listener 4: test'
