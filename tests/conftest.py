import pytest
import litellm

from heracles.core.utils import mock_sleep
from heracles.core.schema import PlaygroundStatusType
from heracles.core.schema.knowledge import DetailedInfo, RepoBasicInfo, LanguageType, FrameworkType, RuntimeType
from heracles.core.schema.test import Mock<PERSON>ocketIOServer
from heracles.server.clacky.playground_channel import PlaygroundChannel
from heracles.server.clacky.playground_manager import PlaygroundManager
from heracles.server.clacky.playground import Playground

socketio_server = MockSocketIOServer()
playground_manager = PlaygroundManager()

@pytest.fixture
def create_blank_playground(mocker):
    """ 底层 fixture, 从零创建一个 "纯正" 的 playground, 不应该在测试用例上直接使用
    """
    async def _create_blank_playground(id):
        playground = Playground(id, socketio_server)

        def connect_ok():
            playground.set_playground_status(PlaygroundStatusType.OK)

        async def healthy_side_effect(*args, **kwargs):
            if kwargs.get('response_model') and kwargs.get('response_model') == RepoBasicInfo:
                return RepoBasicInfo(
                    basic_info_list=[DetailedInfo(
                        language=LanguageType.JAVASCRIPT,
                        runtime=RuntimeType.NODEJS,
                        runtime_version='20',
                        framework=FrameworkType.REACT,
                        framework_version='1.0.0',
                        dependency_command='npm install',
                        run_command='npm run dev',
                    )]
                )
            return 'LLM mock data'

        mocker.patch.object(playground.ide_server_client, 'connect', side_effect=connect_ok)
        mocker.patch.object(playground.ide_server_client, '_agent_func_call')
        mocker.patch.object(playground.ide_server_client, '_agent_is_active', return_value=True)
        mocker.patch.object(playground.ide_server_client, '_agent_rag_is_finish', return_value=True)
        mocker.patch.object(playground.ide_server_client, '_agent_terminal_is_running', return_value=True)

        # 保存原始的 acompletion 函数
        original_acompletion = litellm.acompletion

        async def mock_acompletion(*args, **kwargs):
            return await original_acompletion(*args, mock_response="LLM mock data", **kwargs)
        mocker.patch.object(litellm, 'acompletion', side_effect=mock_acompletion)
        mocker.patch('heracles.agent_roles.plan_role.PlanRole.pre_process_when_start_from_zero', return_value=None)

        # playbook 初始化加载依赖于中间件及环境信息
        mocker.patch.object(playground.agent_controller.analyze_role, 'aask', side_effect=healthy_side_effect)
        mock_playground_info = {
            'data': {
                'codeZoneId': 'test-id'
            }
        }
        mock_middlewares = {
            'data': [
                {
                    'name': 'test-middleware',
                    'innerEnvMap': {'key': 'value'}
                }
            ]
        }
        mocker.patch('heracles.agent_workspace.tools.bind_playground_info', return_value=mock_playground_info)
        mocker.patch('heracles.agent_workspace.tools.list_middlewares_of_codezone', return_value=mock_middlewares)
        mocker.patch('heracles.agent_workspace.agent_workspace.AgentWorkspace.get_supported_middleware_names', return_value=['m1', 'm2']) # noqa
        mocker.patch('heracles.agent_workspace.agent_workspace.AgentWorkspace.init_supported_middleware_names', return_value=None) # noqa

        mock_sleep(mocker)

        await playground.start()

        try:
            return playground
        finally:
            await playground.agent_controller.wait_tasks()
    return _create_blank_playground

@pytest.fixture
async def create_playground_channel(create_blank_playground, mocker):
    """ 高层 fixture, 构建一个完整的 channel 环境, 请主动在测试用例上使用
    """
    playground_manager._reset()
    playground = await create_blank_playground('playground-id-1')
    playground_manager.add_playground(playground)
    playground_channel = PlaygroundChannel('sid-1', socketio_server, playground_manager, 'playground-id-1', project_id=None)

    await playground_channel.start()
    return playground_channel

@pytest.fixture
async def create_workspace(create_playground_channel):
    """ 高层 fixture, 是 create_playground_channel 的更上层 fixture, 请更主动使用
    """
    playground_channel = await create_playground_channel
    playground = playground_channel.current_playground
    workspace = playground.agent_controller.workspace
    return workspace
