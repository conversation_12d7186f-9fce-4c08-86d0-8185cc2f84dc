import pytest
import time

from heracles.server.clacky.playground_manager import Playground<PERSON>anager
from heracles.core.exceptions import AgentRunException
from heracles.core.logger import heracles_logger as logger
from heracles.core.utils.redis_cache import redis_cache

from heracles.agent_roles.plan_role.create_test_task import create_test_task

@pytest.mark.asyncio
async def test_playground_manager(create_blank_playground, mocker):
    playground_1 = await create_blank_playground('id_1')
    playground_2 = await create_blank_playground('id_2')
    playground_3 = await create_blank_playground('id_3')
    playground_4 = await create_blank_playground('id_4')
    playground_5 = await create_blank_playground('id_5')
    playground_6 = await create_blank_playground('id_6')

    playground_manager = PlaygroundManager()
    playground_manager.add_playground(playground_1)
    playground_manager.add_playground(playground_2)
    playground_manager.add_playground(playground_3)
    playground_manager.add_playground(playground_4)
    playground_manager.add_playground(playground_5)
    playground_manager.add_playground(playground_6)

    # 测试重复添加和移除
    with pytest.raises(AgentRunException):
        playground_manager.add_playground(playground_1)
    playground_manager.remove_playground(playground_1)
    playground_manager.add_playground(playground_1)

    future_time = time.time() + 61
    mocker.patch('time.time', return_value=future_time)

    # 测试清理函数
    # 第一次, 每次清 5 个, 剩 1 个
    assert len(playground_manager.to_dict(clear_flag=True)) == 6
    playground_manager.start_cleanup_playground_task(error_raise=True, times=1, interval_time=1)
    assert playground_manager._asyncio_cleanup_playground_task is not None
    await playground_manager._asyncio_cleanup_playground_task
    assert len(playground_manager.to_dict(clear_flag=True)) == 1

    # 第二次, 清理干净
    playground_manager.start_cleanup_playground_task(error_raise=True, times=1, interval_time=1)
    await playground_manager._asyncio_cleanup_playground_task
    assert len(playground_manager.to_dict(clear_flag=True)) == 0

    # 取消任务
    playground_manager.cancel_cleanup_playground_task()
    assert playground_manager._asyncio_cleanup_playground_task is None

@pytest.mark.asyncio
async def test_playground_manager_clear_fail(create_blank_playground, mocker):
    playground_1 = await create_blank_playground('playground_id_1')

    playground_manager = PlaygroundManager()
    playground_manager.add_playground(playground_1)

    mocker.patch.object(playground_1, 'clear_playground', side_effect=AgentRunException("mock error info"))
    # 测试异常任务, 但避免输出错误日志
    mocker.patch.object(playground_1.logger, 'warning')
    mocker.patch.object(playground_1.logger, 'error')
    mocker.patch.object(logger, 'warning')
    mocker.patch.object(logger, 'error')

    future_time = time.time() + 61
    mocker.patch('time.time', return_value=future_time)

    playground_manager.start_cleanup_playground_task(error_raise=True, times=1, interval_time=1)
    assert playground_manager._asyncio_cleanup_playground_task is not None
    with pytest.raises(AgentRunException):
        await playground_manager._asyncio_cleanup_playground_task
    assert len(playground_manager.to_dict(clear_flag=True)) == 1

@pytest.mark.asyncio
async def test_playground_manager_delete_playground(create_blank_playground, mocker):
    playground_1 = await create_blank_playground('id_1')
    playground_2 = await create_blank_playground('id_2')
    playground_2.channel_start_time = time.time() - 31 * 24 * 60 * 60

    playground_manager = PlaygroundManager()
    playground_manager.add_playground(playground_1)
    playground_manager.add_playground(playground_2)

    playground_manager.start_cleanup_playground_task(error_raise=True, times=1, interval_time=1)
    assert playground_manager._asyncio_cleanup_playground_task is not None
    await playground_manager._asyncio_cleanup_playground_task
    assert len(playground_manager.to_dict()) == 1

@pytest.mark.asyncio
async def test_playground_manager_clear_all(create_blank_playground, mocker):
    playground_1 = await create_blank_playground('id_1')
    playground_1.save()
    playground_2 = await create_blank_playground('id_2')
    playground_2.save()

    task = create_test_task()
    task.enable_autosave('id_1')

    playground_manager = PlaygroundManager()
    playground_manager.add_playground(playground_1)
    playground_manager.add_playground(playground_2)

    playground_manager.show_playground_cache('id_1')

    assert len(redis_cache.keys('playground:*')) != 0
    assert len(redis_cache.keys('task:*')) != 0
    playground_manager.clear_all_playgrounds()
    assert len(redis_cache.keys('playground:*')) == 0
    assert len(redis_cache.keys('task:*')) == 0

@pytest.mark.asyncio
async def test_playground_manager_find_or_load_cache_by(create_blank_playground, mocker):
    playground_1 = await create_blank_playground('id_1')
    playground_2 = await create_blank_playground('id_2')
    playground_2.is_root = True
    playground_2.save()
    socketio_server = playground_1.socketio_server

    playground_manager = PlaygroundManager()
    playground_manager.add_playground(playground_1)
    # 已存在
    p1 = await playground_manager.find_or_load_cache_by('id_1', socketio_server)
    assert p1.channel_create_time == playground_1.channel_create_time
    # 从缓存加载
    p2 = await playground_manager.find_or_load_cache_by('id_2', socketio_server)
    assert p2.channel_create_time == playground_2.channel_create_time

    p3 = await playground_manager.find_or_load_cache_by('id_3', socketio_server)
    assert p3 is None
