import pytest
from unittest.mock import AsyncMock, patch

from heracles.core.exceptions import BrowserEx<PERSON>, PaasAgentException
from heracles.core.schema import FocusComponentType
from heracles.agent_workspace.browser import Browser


class MockFollowFocus:
    async def following_focus_component(self, area):
        pass


class TestBrowser:
    @pytest.fixture
    def browser(self):
        follow_focus = MockFollowFocus()
        return Browser(url="http://localhost:8000", follow_focus=follow_focus)

    @pytest.fixture
    def mock_paas_request(self):
        with patch("heracles.agent_workspace.browser.paas_request") as mock:
            yield mock

    @pytest.mark.asyncio
    async def test_follow_focused_component(self, browser):
        browser.follow_focus.following_focus_component = AsyncMock()
        area = FocusComponentType.BROWSER

        await browser.following_focus_component(area)

    @pytest.mark.asyncio
    async def test_goto(self, browser, mock_paas_request):
        # Test successful goto
        mock_paas_request.return_value = None
        await browser.goto("https://example.com")

        # Test error handling
        mock_paas_request.side_effect = PaasAgentException("Connection failed")
        with pytest.raises(BrowserException):
            await browser.goto("https://example.com")

    @pytest.mark.asyncio
    async def test_screenshot(self, browser, mock_paas_request):
        # Test successful screenshot
        mock_paas_request.return_value = {"data": "base64_image_data"}
        result = await browser.screenshot()
        assert result == "data:image/jpeg;base64,base64_image_data"

        # Test no data
        mock_paas_request.return_value = None
        result = await browser.screenshot()
        assert result is None

        # Test error handling
        mock_paas_request.side_effect = PaasAgentException("Screenshot failed")
        with pytest.raises(BrowserException):
            await browser.screenshot()

    @pytest.mark.asyncio
    async def test_get_console_logs(self, browser, mock_paas_request):
        # Test successful logs retrieval
        mock_paas_request.return_value = {"list": ["log1", "log2"]}
        result = await browser.get_console_logs()
        assert result == ["log1", "log2"]

        # Test no logs
        mock_paas_request.return_value = None
        result = await browser.get_console_logs()
        assert result == []

        # Test error handling
        mock_paas_request.side_effect = PaasAgentException("Console logs failed")
        with pytest.raises(BrowserException):
            await browser.get_console_logs()
