import pytest
from unittest.mock import AsyncMock, MagicMock
from heracles.agent_workspace.lint import Lin<PERSON>
from heracles.core.exceptions import PaasAgentException, LintException
from heracles.core.schema import FocusComponentType


@pytest.fixture
def mock_follow_focus():
    """创建 mock 的 follow_focus 对象"""
    follow_focus = MagicMock()
    follow_focus.following_focus_component = AsyncMock()
    return follow_focus


@pytest.fixture
def lint_instance(mock_follow_focus):
    """创建 Lint 实例"""
    return Lint("http://test-url", mock_follow_focus)


class TestLint:
    """Lint 类的单元测试"""

    def test_init(self, mock_follow_focus):
        """测试 Lint 类的初始化"""
        url = "http://test-url"
        lint = Lint(url, mock_follow_focus)

        assert lint.url == url + "/lint"
        assert lint.follow_focus == mock_follow_focus

    @pytest.mark.asyncio
    async def test_following_focus_component(self, lint_instance, mock_follow_focus):
        """测试 following_focus_component 方法"""
        area = FocusComponentType.EDITOR

        await lint_instance.following_focus_component(area)

        mock_follow_focus.following_focus_component.assert_called_once_with(area)

    @pytest.mark.asyncio
    async def test_diagnostic_success(self, lint_instance, mocker):
        """测试 diagnostic 方法成功的情况"""
        mock_response = {
            'data': {
                'errors': [],
                'warnings': ['warning message']
            }
        }
        mock_paas_request = mocker.patch(
            'heracles.agent_workspace.lint.paas_request',
            return_value=mock_response
        )

        paths = ['/path/to/file1.py', '/path/to/file2.py']
        result = await lint_instance.diagnostic(paths)

        expected_url = lint_instance.url + '/diagnostic'
        expected_data = {'paths': paths}

        mock_paas_request.assert_called_once_with("post", expected_url, expected_data)
        assert result == mock_response

    @pytest.mark.asyncio
    async def test_diagnostic_empty_response(self, lint_instance, mocker):
        """测试 diagnostic 方法返回空响应的情况"""
        _mock_paas_request = mocker.patch(
            'heracles.agent_workspace.lint.paas_request',
            return_value=None
        )

        paths = ['/path/to/file.py']
        result = await lint_instance.diagnostic(paths)

        assert not result

        paths = []
        result = await lint_instance.diagnostic(paths)

        assert not result

    @pytest.mark.asyncio
    async def test_diagnostic_paas_agent_exception(self, lint_instance, mocker):
        """测试 diagnostic 方法抛出 PaasAgentException 的情况"""
        _mock_paas_request = mocker.patch(
            'heracles.agent_workspace.lint.paas_request',
            side_effect=PaasAgentException("Connection failed")
        )

        paths = ['/path/to/file.py']

        with pytest.raises(LintException):
            await lint_instance.diagnostic(paths)

    @pytest.mark.asyncio
    async def test_fix_success(self, lint_instance, mocker):
        """测试 fix 方法成功的情况"""
        mock_response = {
            'data': {
                'fixed_files': ['/path/to/file1.py'],
                'fixes_applied': 5
            }
        }
        mock_paas_request = mocker.patch(
            'heracles.agent_workspace.lint.paas_request',
            return_value=mock_response
        )

        result = await lint_instance.fix()

        expected_url = lint_instance.url + '/fix'

        mock_paas_request.assert_called_once_with("post", expected_url)
        assert result == mock_response

    @pytest.mark.asyncio
    async def test_fix_empty_response(self, lint_instance, mocker):
        """测试 fix 方法返回空响应的情况"""
        _mock_paas_request = mocker.patch(
            'heracles.agent_workspace.lint.paas_request',
            return_value=None
        )

        result = await lint_instance.fix()

        assert not result

    @pytest.mark.asyncio
    async def test_fix_paas_agent_exception(self, lint_instance, mocker):
        """测试 fix 方法抛出 PaasAgentException 的情况"""
        _mock_paas_request = mocker.patch(
            'heracles.agent_workspace.lint.paas_request',
            side_effect=PaasAgentException("Service unavailable")
        )

        with pytest.raises(LintException):
            await lint_instance.fix()

    def test_focus_decorator_applied_to_diagnostic(self, lint_instance):
        """测试 diagnostic 方法是否正确应用了 focus 装饰器"""
        # 验证方法存在且可调用
        assert hasattr(lint_instance, 'diagnostic')
        assert callable(lint_instance.diagnostic)

    def test_focus_decorator_applied_to_fix(self, lint_instance):
        """测试 fix 方法是否正确应用了 focus 装饰器"""
        # 验证方法存在且可调用
        assert hasattr(lint_instance, 'fix')
        assert callable(lint_instance.fix)
