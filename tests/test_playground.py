import pytest
import asyncio
import time


from heracles.agent_roles.plan_role.create_test_task import create_test_task
from heracles.core.schema import PlaygroundStatusType
from heracles.core.exceptions import UserEventArgumentException, IDEServerFunCallException, IDEServerConnectException


@pytest.fixture
async def create_playground(create_playground_channel):
    playground_channel = await create_playground_channel
    playground = playground_channel.current_playground
    return playground

@pytest.mark.asyncio
async def test_playground_on_user_event(create_playground, mocker):
    playground = await create_playground

    with pytest.raises(UserEventArgumentException):
        await playground.on_user_event('noexist')

@pytest.mark.asyncio
async def test_playground_connect_ide_server(create_playground, mocker):
    playground = await create_playground

    def connect_ok():
        playground.set_playground_status(PlaygroundStatusType.OK)

    mocker.patch.object(playground.ide_server_client, 'connect', side_effect=connect_ok)
    playground.set_playground_status(PlaygroundStatusType.DRAFTED)
    await playground.connect_ide_server()
    assert playground.playground_status == PlaygroundStatusType.OK

    playground.set_playground_status(PlaygroundStatusType.DRAFTED)
    mocker.patch.object(playground.ide_server_client, 'connect', side_effect=IDEServerConnectException('x'))
    with pytest.raises(IDEServerConnectException):
        await playground.connect_ide_server()
    assert playground.playground_status == PlaygroundStatusType.CONNECT_FAILED

    playground.set_playground_status(PlaygroundStatusType.DRAFTED)
    mocker.patch.object(playground.logger, 'error')
    mocker.patch.object(playground.ide_server_client, 'connect', side_effect=ValueError('x'))
    with pytest.raises(ValueError):
        await playground.connect_ide_server()
    assert playground.playground_status == PlaygroundStatusType.CONNECT_FAILED

    playground.set_playground_status(PlaygroundStatusType.CONNECTING)
    async def connect_ok_delay():
        await asyncio.sleep(0.01)
        playground.set_playground_status(PlaygroundStatusType.OK)

    async def connect_ide_server():
        await playground.connect_ide_server()
    tasks = [connect_ide_server(), connect_ok_delay()]
    await asyncio.gather(*tasks)

@pytest.mark.asyncio
async def test_playground_func_call(create_playground):
    playground = await create_playground

    await playground.func_call('agent_file_tree')

    with pytest.raises(IDEServerFunCallException):
        await playground.func_call('noexist')
    with pytest.raises(IDEServerFunCallException):
        await playground.func_call('agent_noexist')

    playground.set_playground_status(PlaygroundStatusType.CONNECT_FAILED)
    with pytest.raises(IDEServerConnectException):
        await playground.func_call('agent_file_tree')

@pytest.mark.asyncio
async def test_playground_online_clear_etc(create_playground, mocker):
    playground = await create_playground

    assert playground.can_clear_playground() is False
    assert playground.is_channel_online() is True

    playground.reduce_channel_online_count()
    assert playground.can_clear_playground() is False
    assert playground.is_channel_online() is False

    future_time = time.time() + 120
    mocker.patch('time.time', return_value=future_time)
    assert playground.can_clear_playground() is True

    task_state = playground.agent_controller.task_state
    task_state.set_state(task_state.working)
    assert playground.can_clear_playground() is False
    task_state.set_state(task_state.done)
    assert playground.can_clear_playground() is True

@pytest.mark.asyncio
async def test_playground_on_off_ide_event(create_playground):
    playground = await create_playground

    def print_console(data):
        return data

    playground.on_ide_event('console', print_console)
    playground.off_ide_event('console', print_console)

@pytest.mark.asyncio
async def test_playground_get_playground_info(create_playground, mocker):
    playground = await create_playground

    res = playground.get_playground_info()
    assert res['task'] is None

    mocker.patch.object(playground.agent_controller.plan_role, 'make_plan', return_value=create_test_task())
    await playground.on_user_event('u_make_plan', 'test:xxx')
    await playground.agent_controller.wait_tasks()
    res = playground.get_playground_info()
    assert res['task'] is not None

@pytest.mark.asyncio
async def test_playground_trigger(create_playground):
    playground = await create_playground

    await playground.trigger_chunk_message('hello')
    await playground.trigger_message('hello')
    await playground.trigger_smart_detect_status_updated({'status': 'ok'})
    await playground.trigger_project_environment_result_updated({'status': 'ok'})

@pytest.mark.asyncio
async def test_playground_load_from_cache(create_playground, mocker):
    playground = await create_playground

    playground.add_channel_online_count()
    mocker.patch.object(playground.agent_controller.plan_role, 'make_plan', return_value=create_test_task())
    await playground.agent_controller.make_plan('test:xxx', '', None)
    playground.agent_controller.task_state.plan_task()
    playground.agent_controller.task_state.start_task()

    playground_id = playground.playground_id
    socketio_server = playground.socketio_server
    playground.agent_controller.task_state.appended_count = 2
    playground.agent_controller.task_state.appended_expansion_turn = 3
    playground.agent_controller.task_state.save()
    new_playground = await playground.load_from_cache(playground_id, socketio_server)
    assert new_playground.agent_controller.task == playground.agent_controller.task
    assert new_playground.agent_controller.task_state.state == playground.agent_controller.task_state.state
    assert new_playground.agent_controller.task_state.appended_count == 2
    assert new_playground.agent_controller.task_state.appended_expansion_turn == 3
